'use client'

import React, { useState, useEffect } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Search,
  CheckCircle,
  AlertTriangle,
  Ruler,
  Package,
  X,
  Loader2,
  HelpCircle
} from 'lucide-react'
import { Cavo } from '@/types'
import { parcoCaviApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'

// Enhanced Modal Components (imported from CableActionModals)
interface EnhancedModalHeaderProps {
  icon: React.ReactNode
  title: string
  cableId: string
  description?: string
}

const EnhancedModalHeader: React.FC<EnhancedModalHeaderProps> = ({
  icon,
  title,
  cableId,
  description
}) => (
  <DialogHeader>
    <DialogTitle className="flex items-center gap-2">
      {icon}
      <span className="flex items-center gap-2">
        {title}
        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-sm font-mono font-semibold">
          {cableId}
        </span>
      </span>
    </DialogTitle>
    {description && (
      <DialogDescription className="text-sm text-muted-foreground">
        {description}
      </DialogDescription>
    )}
  </DialogHeader>
)

interface EnhancedDialogContentProps {
  children: React.ReactNode
  className?: string
  onKeyDown?: (e: React.KeyboardEvent) => void
  ariaLabelledBy?: string
  ariaDescribedBy?: string
}

const EnhancedDialogContent: React.FC<EnhancedDialogContentProps> = ({
  children,
  className = "sm:max-w-md",
  onKeyDown,
  ariaLabelledBy,
  ariaDescribedBy
}) => (
  <DialogContent 
    className={className}
    onKeyDown={onKeyDown}
    aria-labelledby={ariaLabelledBy}
    aria-describedby={ariaDescribedBy}
    onPointerDownOutside={(e) => e.preventDefault()}
    onEscapeKeyDown={(e) => {
      if (onKeyDown) {
        onKeyDown(e as any)
      }
    }}
  >
    {children}
  </DialogContent>
)

// Types
interface Bobina {
  id_bobina: string
  numero_bobina: string
  tipologia: string
  n_conduttori: string
  sezione: string
  metri_totali: number
  metri_residui: number
  stato_bobina: string
  fornitore?: string
  compatible?: boolean
}

interface ModificaBobinaModalProps {
  open: boolean
  onClose: () => void
  cavo: Cavo | null
  onSave: (cavoId: string, bobinaId: string, option: string) => Promise<void>
}

interface InserisciMetriModalProps {
  open: boolean
  onClose: () => void
  cavo: Cavo | null
  onSave: (cavoId: string, metriPosati: number) => Promise<void>
}

// Cable Info Card Component
interface CableInfoCardProps {
  cavo: Cavo
}

const CableInfoCard: React.FC<CableInfoCardProps> = ({ cavo }) => (
  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-3">
    <div className="flex items-center gap-2 mb-3">
      <Package className="h-5 w-5 text-blue-600" />
      <h3 className="font-semibold text-blue-800">Informazioni Cavo {cavo.id_cavo}</h3>
    </div>

    <div className="grid grid-cols-2 gap-3 text-sm">
      <div className="flex flex-col">
        <span className="text-gray-600 text-xs font-medium uppercase tracking-wide">Tipologia</span>
        <span className="text-gray-900 font-medium">{cavo.tipologia || 'N/A'}</span>
      </div>

      <div className="flex flex-col">
        <span className="text-gray-600 text-xs font-medium uppercase tracking-wide">Formazione</span>
        <span className="text-gray-900 font-medium">{cavo.sezione || 'N/A'}</span>
      </div>

      <div className="flex flex-col">
        <span className="text-gray-600 text-xs font-medium uppercase tracking-wide">Da</span>
        <span className="text-gray-900 font-medium">{cavo.ubicazione_partenza || 'N/A'}</span>
      </div>

      <div className="flex flex-col">
        <span className="text-gray-600 text-xs font-medium uppercase tracking-wide">A</span>
        <span className="text-gray-900 font-medium">{cavo.ubicazione_arrivo || 'N/A'}</span>
      </div>

      <div className="flex flex-col col-span-2">
        <span className="text-gray-600 text-xs font-medium uppercase tracking-wide">Metri Posati</span>
        <span className="text-blue-600 font-bold text-lg">{cavo.metratura_reale || 0} m</span>
      </div>
    </div>
  </div>
)

// Modifica Bobina Modal
export const ModificaBobinaModal: React.FC<ModificaBobinaModalProps> = ({
  open,
  onClose,
  cavo,
  onSave
}) => {
  const { cantiere } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [loadingBobine, setLoadingBobine] = useState(false)
  const [selectedOption, setSelectedOption] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [activeTab, setActiveTab] = useState<'compatible' | 'incompatible'>('compatible')
  const [selectedBobina, setSelectedBobina] = useState<string>('')
  const [bobine, setBobine] = useState<Bobina[]>([])
  const [error, setError] = useState('')

  // Carica bobine reali dall'API
  const loadBobine = async () => {
    if (!cantiere?.id_cantiere || !cavo) return

    setLoadingBobine(true)
    setError('')

    try {
      console.log('🔄 ModificaBobinaModal: Caricamento bobine per cantiere:', cantiere.id_cantiere)

      // Usa la stessa logica di InserisciMetriDialog - SENZA parametri aggiuntivi
      const response = await parcoCaviApi.getBobine(cantiere.id_cantiere)

      if (response && Array.isArray(response)) {
        // Filtra solo per stato (disponibile o in uso) e metri residui > 0 - STESSA LOGICA DI InserisciMetriDialog
        const bobineUtilizzabili = response.filter((bobina: any) =>
          bobina.stato_bobina !== 'Terminata' &&
          bobina.stato_bobina !== 'Over' &&
          bobina.metri_residui > 0
        )

        // Determina compatibilità basata su tipologia e sezione
        const bobineWithCompatibility = bobineUtilizzabili.map((bobina: any) => ({
          ...bobina,
          compatible: bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione
        }))

        setBobine(bobineWithCompatibility)
        console.log('✅ ModificaBobinaModal: Bobine caricate:', bobineWithCompatibility.length)
        console.log('📋 ModificaBobinaModal: Dettaglio bobine:', bobineWithCompatibility.map(b => ({
          id: b.id_bobina,
          tipologia: b.tipologia,
          sezione: b.sezione,
          metri_residui: b.metri_residui,
          stato: b.stato_bobina,
          compatible: b.compatible
        })))
      } else {
        setBobine([])
        console.log('⚠️ ModificaBobinaModal: Nessuna bobina trovata')
      }
    } catch (error: any) {
      console.error('Errore caricamento bobine:', error)
      setError('Errore durante il caricamento delle bobine')
    } finally {
      setLoadingBobine(false)
    }
  }

  // Carica bobine quando si apre il modal
  useEffect(() => {
    if (open && cavo && cantiere) {
      loadBobine()
    }
  }, [open, cavo, cantiere])

  const filteredBobine = bobine.filter(bobina => {
    const matchesSearch = bobina.numero_bobina.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         bobina.tipologia.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesTab = activeTab === 'compatible' ? bobina.compatible : !bobina.compatible
    return matchesSearch && matchesTab
  })

  const compatibleCount = bobine.filter(b => b.compatible).length
  const incompatibleCount = bobine.filter(b => !b.compatible).length

  const handleSave = async () => {
    if (!cavo || !selectedOption) return

    setIsLoading(true)
    try {
      await onSave(cavo.id_cavo, selectedBobina, selectedOption)
      handleClose()
    } catch (error) {
      console.error('Error saving bobina modification:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleClose()
    }
  }

  const handleClose = () => {
    // Reset stati quando si chiude il modal
    setSelectedOption('')
    setSelectedBobina('')
    setSearchTerm('')
    setActiveTab('compatible')
    setError('')
    setBobine([])
    onClose()
  }

  if (!cavo) return null

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <EnhancedDialogContent
        className="sm:max-w-3xl max-h-[85vh] overflow-hidden"
        onKeyDown={handleKeyDown}
        ariaLabelledBy="modifica-bobina-title"
      >
        <EnhancedModalHeader
          icon={<Package className="h-5 w-5 text-blue-500" />}
          title="Modifica Bobina Cavo"
          cableId={cavo.id_cavo}
          description="Seleziona una nuova bobina per il cavo o modifica i parametri"
        />

        <div className="space-y-4 py-4 overflow-y-auto max-h-[calc(85vh-200px)]">
          <CableInfoCard cavo={cavo} />

          {/* Messaggio di errore */}
          {error && (
            <Alert className="bg-red-50 border-red-200">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                {error}
              </AlertDescription>
            </Alert>
          )}

          {/* Opzioni di modifica */}
          <div className="space-y-3">
            <Label className="text-sm font-semibold">Opzioni di modifica</Label>
            <div className="grid grid-cols-1 gap-2">
              <label className="flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors">
                <input
                  type="radio"
                  name="modifica-option"
                  value="cambia-bobina"
                  checked={selectedOption === 'cambia-bobina'}
                  onChange={(e) => setSelectedOption(e.target.value)}
                  className="text-blue-600 focus:ring-blue-500"
                />
                <div>
                  <span className="text-sm font-medium">Cambia bobina</span>
                  <p className="text-xs text-gray-500">Assegna una bobina diversa al cavo</p>
                </div>
              </label>
              <label className="flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors">
                <input
                  type="radio"
                  name="modifica-option"
                  value="bobina-vuota"
                  checked={selectedOption === 'bobina-vuota'}
                  onChange={(e) => setSelectedOption(e.target.value)}
                  className="text-blue-600 focus:ring-blue-500"
                />
                <div>
                  <span className="text-sm font-medium">Bobina vuota</span>
                  <p className="text-xs text-gray-500">Rimuovi l'associazione con la bobina attuale</p>
                </div>
              </label>
              <label className="flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors">
                <input
                  type="radio"
                  name="modifica-option"
                  value="annulla-posa"
                  checked={selectedOption === 'annulla-posa'}
                  onChange={(e) => setSelectedOption(e.target.value)}
                  className="text-red-600 focus:ring-red-500"
                />
                <div>
                  <span className="text-sm font-medium text-red-700">Annulla posa</span>
                  <p className="text-xs text-red-500">Annulla l'installazione e restituisci i metri alla bobina</p>
                </div>
              </label>
            </div>
          </div>

          {selectedOption === 'cambia-bobina' && (
            <>
              {/* Campo di ricerca */}
              <div className="space-y-2">
                <Label htmlFor="search-bobina" className="text-sm font-medium">
                  Cerca bobina
                </Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="search-bobina"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Cerca bobina per ID, tipologia o numero..."
                    className="pl-10 h-8"
                  />
                </div>
              </div>

              {/* Tabs per bobine compatibili/incompatibili */}
              <div className="space-y-2">
                <div className="flex border-b">
                  <button
                    onClick={() => setActiveTab('compatible')}
                    className={`flex items-center gap-2 px-3 py-1.5 text-sm font-medium border-b-2 transition-colors ${
                      activeTab === 'compatible'
                        ? 'border-green-500 text-green-700 bg-green-50'
                        : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    <CheckCircle className="h-3 w-3 text-green-500" />
                    Compatibili ({compatibleCount})
                  </button>
                  <button
                    onClick={() => setActiveTab('incompatible')}
                    className={`flex items-center gap-2 px-3 py-1.5 text-sm font-medium border-b-2 transition-colors ${
                      activeTab === 'incompatible'
                        ? 'border-yellow-500 text-yellow-700 bg-yellow-50'
                        : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    <AlertTriangle className="h-3 w-3 text-yellow-500" />
                    Incompatibili ({incompatibleCount})
                  </button>
                </div>

                {/* Lista bobine - Ridotta altezza per mostrare almeno 3 bobine */}
                <div className="h-32 overflow-y-auto border rounded-md bg-gray-50">
                  {loadingBobine ? (
                    <div className="p-3 text-center">
                      <Loader2 className="h-6 w-6 text-blue-500 mx-auto mb-2 animate-spin" />
                      <p className="text-xs text-gray-600">Caricamento bobine...</p>
                    </div>
                  ) : filteredBobine.length === 0 ? (
                    <div className="p-3 text-center">
                      <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                        <AlertTriangle className="h-6 w-6 text-yellow-500 mx-auto mb-1" />
                        <p className="text-xs text-yellow-800 font-medium mb-1">
                          Nessuna bobina {activeTab === 'compatible' ? 'compatibile' : 'incompatibile'} trovata
                        </p>
                        <p className="text-xs text-yellow-700">
                          Prova a modificare i criteri di ricerca
                        </p>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-1 p-1">
                      {filteredBobine.map((bobina) => (
                        <label
                          key={bobina.id_bobina}
                          className="flex items-center space-x-2 p-2 border rounded-md hover:bg-white cursor-pointer transition-colors bg-white"
                        >
                          <input
                            type="radio"
                            name="selected-bobina"
                            value={bobina.id_bobina}
                            checked={selectedBobina === bobina.id_bobina}
                            onChange={(e) => setSelectedBobina(e.target.value)}
                            className="text-blue-600 focus:ring-blue-500 w-3 h-3"
                          />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-1">
                              <span className="font-medium text-xs truncate">{bobina.numero_bobina}</span>
                              {bobina.compatible ? (
                                <CheckCircle className="h-3 w-3 text-green-500 flex-shrink-0" />
                              ) : (
                                <AlertTriangle className="h-3 w-3 text-yellow-500 flex-shrink-0" />
                              )}
                            </div>
                            <p className="text-xs text-gray-500 truncate">
                              {bobina.tipologia} - {bobina.sezione} - {bobina.metri_residui}m
                            </p>
                          </div>
                        </label>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          {/* Messaggio di conferma per annulla posa */}
          {selectedOption === 'annulla-posa' && (
            <Alert className="bg-red-50 border-red-200">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                <strong>ATTENZIONE:</strong> Questa operazione annullerà completamente l'installazione del cavo.
                Tutti i metri posati saranno restituiti alla bobina originale e lo stato del cavo sarà resettato a "Da installare".
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter className="gap-2 pt-4 border-t">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
            className="px-6 py-2 hover:bg-gray-50"
          >
            Annulla
          </Button>
          <Button
            onClick={handleSave}
            disabled={isLoading || !selectedOption || (selectedOption === 'cambia-bobina' && !selectedBobina)}
            className={`px-6 py-2 ${
              selectedOption === 'annulla-posa'
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-blue-600 hover:bg-blue-700 text-white'
            }`}
            variant={selectedOption === 'annulla-posa' ? 'destructive' : 'default'}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Salvando...
              </>
            ) : selectedOption === 'annulla-posa' ? (
              <>
                <AlertTriangle className="mr-2 h-4 w-4" />
                Annulla Posa
              </>
            ) : (
              'Salva Modifiche'
            )}
          </Button>
        </DialogFooter>
      </EnhancedDialogContent>
    </Dialog>
  )
}

// Inserisci Metri Posati Modal
export const InserisciMetriModal: React.FC<InserisciMetriModalProps> = ({
  open,
  onClose,
  cavo,
  onSave
}) => {
  const [isLoading, setIsLoading] = useState(false)
  const [metriPosati, setMetriPosati] = useState('')
  const [validationError, setValidationError] = useState('')

  // Mock data for demonstration
  const metriDaInstallare = 150
  const metriGiaInstallati = 75

  useEffect(() => {
    if (open) {
      setMetriPosati('')
      setValidationError('')
    }
  }, [open])

  const validateInput = (value: string) => {
    const numValue = parseFloat(value)

    if (!value.trim()) {
      return 'Il campo metri posati è obbligatorio'
    }

    if (isNaN(numValue) || numValue <= 0) {
      return 'Inserisci un valore numerico valido maggiore di 0'
    }

    if (numValue > metriDaInstallare) {
      return `I metri posati non possono superare i metri da installare (${metriDaInstallare}m)`
    }

    return ''
  }

  const handleInputChange = (value: string) => {
    setMetriPosati(value)
    const error = validateInput(value)
    setValidationError(error)
  }

  const handleSave = async () => {
    if (!cavo) return

    const error = validateInput(metriPosati)
    if (error) {
      setValidationError(error)
      return
    }

    setIsLoading(true)
    try {
      await onSave(cavo.id_cavo, parseFloat(metriPosati))
      onClose()
    } catch (error) {
      console.error('Error saving metri posati:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose()
    }
    if (e.key === 'Enter' && !validationError && metriPosati.trim()) {
      handleSave()
    }
  }

  const isFormValid = metriPosati.trim() && !validationError

  if (!cavo) return null

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <EnhancedDialogContent
        className="sm:max-w-lg"
        onKeyDown={handleKeyDown}
        ariaLabelledBy="inserisci-metri-title"
      >
        <EnhancedModalHeader
          icon={<Ruler className="h-5 w-5 text-green-500" />}
          title="Inserisci Metri Posati"
          cableId={cavo.id_cavo}
          description="Registra i metri di cavo effettivamente posati"
        />

        <div className="space-y-4 py-4">
          <CableInfoCard cavo={cavo} />

          {/* Box informativi */}
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Ruler className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-semibold text-blue-900">Metri da Installare</span>
              </div>
              <p className="text-2xl font-bold text-blue-700">{metriDaInstallare}m</p>
              <p className="text-xs text-blue-600">Lunghezza totale prevista</p>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm font-semibold text-green-900">Già Installati</span>
              </div>
              <p className="text-2xl font-bold text-green-700">{metriGiaInstallati}m</p>
              <p className="text-xs text-green-600">Precedentemente registrati</p>
            </div>
          </div>

          {/* Campo input metri posati */}
          <div className="space-y-2">
            <Label htmlFor="metri-posati" className="text-sm font-medium flex items-center gap-2">
              <Ruler className="h-4 w-4 text-gray-600" />
              Metri Posati *
            </Label>
            <div className="relative">
              <Input
                id="metri-posati"
                type="number"
                value={metriPosati}
                onChange={(e) => handleInputChange(e.target.value)}
                placeholder="Inserisci i metri posati..."
                min="0"
                max={metriDaInstallare}
                step="0.1"
                className={`pr-12 ${validationError ? 'border-red-500 focus:ring-red-500' : 'focus:ring-green-500'}`}
              />
              <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-gray-500">
                m
              </span>
            </div>
            {validationError && (
              <p className="text-sm text-red-600 flex items-center gap-1">
                <AlertTriangle className="h-3 w-3" />
                {validationError}
              </p>
            )}
            <p className="text-xs text-gray-500">
              Rimanenti da installare: {Math.max(0, metriDaInstallare - metriGiaInstallati - (parseFloat(metriPosati) || 0))}m
            </p>
          </div>

          {/* Informazioni aggiuntive */}
          <Alert className="bg-blue-50 border-blue-200">
            <HelpCircle className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-blue-800">
              <strong>Suggerimento:</strong> Assicurati di misurare accuratamente i metri posati.
              Questo valore verrà utilizzato per calcolare il progresso dell'installazione.
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
            className="flex-1 hover:bg-gray-50"
          >
            Annulla
          </Button>
          <Button
            onClick={handleSave}
            disabled={isLoading || !isFormValid}
            className={`flex-1 ${!isFormValid ? 'opacity-50 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700'}`}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Salvando...
              </>
            ) : (
              <>
                <CheckCircle className="mr-2 h-4 w-4" />
                Salva Metri
              </>
            )}
          </Button>
        </DialogFooter>
      </EnhancedDialogContent>
    </Dialog>
  )
}

// Export types
export type {
  ModificaBobinaModalProps,
  InserisciMetriModalProps,
  Bobina
}
