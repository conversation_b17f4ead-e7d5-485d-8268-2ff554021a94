{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|public).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "519923cd3d0546f91f65fc4041c4132f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "678370856b871f0578d7d43d1faba809da7fa1d79ad19a179796da38637289df", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "1f1bf6e9f847c006eeaad312eb364da69482a3a472e37fd0e558aa58a1a8f12b"}}}, "instrumentation": null, "functions": {}}