{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "e87237672789d4b0bb050f42a0624887", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "52d33f1900111c49d0b2c65c1f9bb86953b682e283634ad4c02e3b4110953a45", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "57f2229dd1861ec03b3820945f47c678b93fbef95bf1505bf3002d3b9da89fbe"}}}, "sortedMiddleware": ["/"], "functions": {}}