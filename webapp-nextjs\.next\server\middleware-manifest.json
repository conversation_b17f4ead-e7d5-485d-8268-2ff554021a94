{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "07d54b258b225b7b2cbbbe5ed1ec289d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0d279ac1db3eec0ccc2a8931fccd41ef9b5b6501d7ca171c08e11dabbd44ecd5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8c8eeb64e8f6219bb5794ec71a57ab19e53393bb161629dfe95701c88f92bd94"}}}, "sortedMiddleware": ["/"], "functions": {}}