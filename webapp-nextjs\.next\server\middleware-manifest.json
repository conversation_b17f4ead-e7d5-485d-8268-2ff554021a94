{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "8947abdbccb62a530a82edd8666760c6", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "45a3b2a4a1c0f1480d7a881b6536da56fed4ba958c2771bd46ee40bcb772dee3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "27c6ae94491ce68d81e593e613603688c50d0890507f3a93fc7a9453c828ee65"}}}, "sortedMiddleware": ["/"], "functions": {}}