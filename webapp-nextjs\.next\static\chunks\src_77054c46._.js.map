{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/hooks/useCantiere.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Cantiere } from '@/types'\n\ninterface UseCantiereResult {\n  cantiereId: number | null\n  cantiere: Cantiere | null\n  isValidCantiere: boolean\n  isLoading: boolean\n  error: string | null\n  validateCantiere: (id: number | string) => boolean\n  clearError: () => void\n}\n\n/**\n * Hook personalizzato per la gestione robusta del cantiere selezionato\n * Gestisce validazione, errori e sincronizzazione con AuthContext\n */\nexport function useCantiere(): UseCantiereResult {\n  const { cantiere, isLoading: authLoading } = useAuth()\n  const [cantiereId, setCantiereId] = useState<number | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  // Funzione per validare un ID cantiere\n  const validateCantiere = (id: number | string): boolean => {\n    if (id === null || id === undefined) return false\n    \n    const numId = typeof id === 'string' ? parseInt(id, 10) : id\n    \n    if (isNaN(numId) || numId <= 0) {\n      console.warn('🏗️ useCantiere: ID cantiere non valido:', id)\n      return false\n    }\n    \n    return true\n  }\n\n  // Effetto per sincronizzare con AuthContext e localStorage\n  useEffect(() => {\n    if (authLoading) {\n      console.log('🏗️ useCantiere: Autenticazione in corso...')\n      return\n    }\n\n    setIsLoading(true)\n    setError(null)\n\n    try {\n      let selectedId: number | null = null\n\n      // Priorità 1: Cantiere dal context di autenticazione (login cantiere diretto)\n      if (cantiere?.id_cantiere && validateCantiere(cantiere.id_cantiere)) {\n        selectedId = cantiere.id_cantiere\n        console.log('🏗️ useCantiere: Usando cantiere dal context (login cantiere):', selectedId)\n      } else {\n        // Priorità 2: Cantiere dal localStorage (cantiere_data per login cantiere)\n        const cantiereData = localStorage.getItem('cantiere_data')\n        if (cantiereData) {\n          try {\n            const parsedData = JSON.parse(cantiereData)\n            if (parsedData.id_cantiere && validateCantiere(parsedData.id_cantiere)) {\n              selectedId = parsedData.id_cantiere\n              console.log('🏗️ useCantiere: Usando cantiere da cantiere_data:', selectedId)\n            }\n          } catch (parseError) {\n            console.warn('🏗️ useCantiere: Errore parsing cantiere_data:', parseError)\n          }\n        }\n\n        // Priorità 3: Cantiere dal localStorage (selectedCantiereId per selezione manuale)\n        if (!selectedId) {\n          const storedId = localStorage.getItem('selectedCantiereId')\n          if (storedId && validateCantiere(storedId)) {\n            selectedId = parseInt(storedId, 10)\n            console.log('🏗️ useCantiere: Usando cantiere da selectedCantiereId:', selectedId)\n          }\n        }\n      }\n\n      if (selectedId) {\n        setCantiereId(selectedId)\n        console.log('🏗️ useCantiere: Cantiere valido impostato:', selectedId)\n      } else {\n        console.warn('🏗️ useCantiere: Nessun cantiere valido trovato')\n        setCantiereId(null)\n        setError('Nessun cantiere selezionato. Seleziona un cantiere per continuare.')\n      }\n    } catch (err) {\n      console.error('🏗️ useCantiere: Errore nella gestione cantiere:', err)\n      setError('Errore nella gestione del cantiere selezionato.')\n      setCantiereId(null)\n    } finally {\n      setIsLoading(false)\n    }\n  }, [cantiere, authLoading])\n\n  const clearError = () => setError(null)\n\n  return {\n    cantiereId,\n    cantiere,\n    isValidCantiere: cantiereId !== null && cantiereId > 0,\n    isLoading,\n    error,\n    validateCantiere,\n    clearError\n  }\n}\n\n/**\n * Hook semplificato che restituisce solo l'ID del cantiere valido o null\n */\nexport function useCantiereId(): number | null {\n  const { cantiereId } = useCantiere()\n  return cantiereId\n}\n\n/**\n * Hook che forza la presenza di un cantiere valido\n * Lancia un errore se non c'è un cantiere selezionato\n */\nexport function useRequiredCantiere(): { cantiereId: number; cantiere: Cantiere | null } {\n  const { cantiereId, cantiere, isLoading, error } = useCantiere()\n\n  if (isLoading) {\n    throw new Error('Caricamento cantiere in corso...')\n  }\n\n  if (error) {\n    throw new Error(error)\n  }\n\n  if (!cantiereId || cantiereId <= 0) {\n    throw new Error('Nessun cantiere selezionato. Seleziona un cantiere per continuare.')\n  }\n\n  return { cantiereId, cantiere }\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;AAHA;;;AAoBO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,uCAAuC;IACvC,MAAM,mBAAmB,CAAC;QACxB,IAAI,OAAO,QAAQ,OAAO,WAAW,OAAO;QAE5C,MAAM,QAAQ,OAAO,OAAO,WAAW,SAAS,IAAI,MAAM;QAE1D,IAAI,MAAM,UAAU,SAAS,GAAG;YAC9B,QAAQ,IAAI,CAAC,4CAA4C;YACzD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,2DAA2D;IAC3D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,aAAa;gBACf,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,aAAa;YACb,SAAS;YAET,IAAI;gBACF,IAAI,aAA4B;gBAEhC,8EAA8E;gBAC9E,IAAI,UAAU,eAAe,iBAAiB,SAAS,WAAW,GAAG;oBACnE,aAAa,SAAS,WAAW;oBACjC,QAAQ,GAAG,CAAC,kEAAkE;gBAChF,OAAO;oBACL,2EAA2E;oBAC3E,MAAM,eAAe,aAAa,OAAO,CAAC;oBAC1C,IAAI,cAAc;wBAChB,IAAI;4BACF,MAAM,aAAa,KAAK,KAAK,CAAC;4BAC9B,IAAI,WAAW,WAAW,IAAI,iBAAiB,WAAW,WAAW,GAAG;gCACtE,aAAa,WAAW,WAAW;gCACnC,QAAQ,GAAG,CAAC,sDAAsD;4BACpE;wBACF,EAAE,OAAO,YAAY;4BACnB,QAAQ,IAAI,CAAC,kDAAkD;wBACjE;oBACF;oBAEA,mFAAmF;oBACnF,IAAI,CAAC,YAAY;wBACf,MAAM,WAAW,aAAa,OAAO,CAAC;wBACtC,IAAI,YAAY,iBAAiB,WAAW;4BAC1C,aAAa,SAAS,UAAU;4BAChC,QAAQ,GAAG,CAAC,2DAA2D;wBACzE;oBACF;gBACF;gBAEA,IAAI,YAAY;oBACd,cAAc;oBACd,QAAQ,GAAG,CAAC,+CAA+C;gBAC7D,OAAO;oBACL,QAAQ,IAAI,CAAC;oBACb,cAAc;oBACd,SAAS;gBACX;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,oDAAoD;gBAClE,SAAS;gBACT,cAAc;YAChB,SAAU;gBACR,aAAa;YACf;QACF;gCAAG;QAAC;QAAU;KAAY;IAE1B,MAAM,aAAa,IAAM,SAAS;IAElC,OAAO;QACL;QACA;QACA,iBAAiB,eAAe,QAAQ,aAAa;QACrD;QACA;QACA;QACA;IACF;AACF;GA1FgB;;QAC+B,kIAAA,CAAA,UAAO;;;AA8F/C,SAAS;;IACd,MAAM,EAAE,UAAU,EAAE,GAAG;IACvB,OAAO;AACT;IAHgB;;QACS;;;AAQlB,SAAS;;IACd,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG;IAEnD,IAAI,WAAW;QACb,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,OAAO;QACT,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,CAAC,cAAc,cAAc,GAAG;QAClC,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;QAAE;QAAY;IAAS;AAChC;IAhBgB;;QACqC", "debugId": null}}, {"offset": {"line": 264, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cantiere/CantiereErrorBoundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { AlertCircle, Construction, RefreshCw, ArrowLeft } from 'lucide-react'\nimport { useRouter } from 'next/navigation'\nimport { useCantiere } from '@/hooks/useCantiere'\n\ninterface CantiereErrorBoundaryProps {\n  children: React.ReactNode\n  fallback?: React.ReactNode\n  showBackButton?: boolean\n  backUrl?: string\n}\n\n/**\n * Componente per gestire errori relativi alla selezione del cantiere\n * Mostra messaggi di errore appropriati e opzioni di recupero\n */\nexport function CantiereErrorBoundary({ \n  children, \n  fallback, \n  showBackButton = true, \n  backUrl = '/cantieri' \n}: CantiereErrorBoundaryProps) {\n  const router = useRouter()\n  const { cantiereId, cantiere, isValidCantiere, isLoading, error, clearError } = useCantiere()\n\n  // Se stiamo caricando, mostra un loader\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\">\n        <div className=\"max-w-4xl mx-auto\">\n          <Card>\n            <CardContent className=\"flex items-center justify-center p-8\">\n              <div className=\"text-center\">\n                <RefreshCw className=\"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600\" />\n                <p className=\"text-lg font-medium text-gray-700\">Caricamento cantiere...</p>\n                <p className=\"text-sm text-gray-500 mt-2\">Verifica della selezione cantiere in corso</p>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    )\n  }\n\n  // Se c'è un errore o il cantiere non è valido, mostra l'errore\n  if (error || !isValidCantiere) {\n    const errorMessage = error || 'Nessun cantiere selezionato'\n    \n    if (fallback) {\n      return <>{fallback}</>\n    }\n\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\">\n        <div className=\"max-w-4xl mx-auto space-y-6\">\n          \n          {/* Header con errore */}\n          <Card className=\"border-red-200 bg-red-50\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center text-red-800\">\n                <AlertCircle className=\"h-6 w-6 mr-2\" />\n                Problema con la selezione del cantiere\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <Alert variant=\"destructive\">\n                <AlertCircle className=\"h-4 w-4\" />\n                <AlertDescription>\n                  <strong>Errore:</strong> {errorMessage}\n                </AlertDescription>\n              </Alert>\n            </CardContent>\n          </Card>\n\n          {/* Informazioni di debug */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center text-gray-700\">\n                <Construction className=\"h-5 w-5 mr-2\" />\n                Informazioni cantiere\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                <div>\n                  <span className=\"font-medium text-gray-600\">ID Cantiere:</span>\n                  <span className=\"ml-2 text-gray-800\">{cantiereId || 'Non disponibile'}</span>\n                </div>\n                <div>\n                  <span className=\"font-medium text-gray-600\">Nome Cantiere:</span>\n                  <span className=\"ml-2 text-gray-800\">{cantiere?.commessa || 'Non disponibile'}</span>\n                </div>\n                <div>\n                  <span className=\"font-medium text-gray-600\">Cantiere Valido:</span>\n                  <span className={`ml-2 ${isValidCantiere ? 'text-green-600' : 'text-red-600'}`}>\n                    {isValidCantiere ? 'Sì' : 'No'}\n                  </span>\n                </div>\n                <div>\n                  <span className=\"font-medium text-gray-600\">localStorage ID:</span>\n                  <span className=\"ml-2 text-gray-800\">\n                    {typeof window !== 'undefined' ? localStorage.getItem('selectedCantiereId') || 'Non presente' : 'N/A'}\n                  </span>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Azioni di recupero */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-gray-700\">Azioni disponibili</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div className=\"flex flex-wrap gap-3\">\n                \n                {/* Pulsante per tornare ai cantieri */}\n                {showBackButton && (\n                  <Button \n                    onClick={() => router.push(backUrl)}\n                    variant=\"default\"\n                    className=\"flex items-center\"\n                  >\n                    <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                    Seleziona Cantiere\n                  </Button>\n                )}\n\n                {/* Pulsante per pulire errore */}\n                {error && (\n                  <Button \n                    onClick={clearError}\n                    variant=\"outline\"\n                    className=\"flex items-center\"\n                  >\n                    <RefreshCw className=\"h-4 w-4 mr-2\" />\n                    Riprova\n                  </Button>\n                )}\n\n                {/* Pulsante per pulire localStorage */}\n                <Button \n                  onClick={() => {\n                    localStorage.removeItem('selectedCantiereId')\n                    localStorage.removeItem('selectedCantiereName')\n                    localStorage.removeItem('cantiere_data')\n                    window.location.reload()\n                  }}\n                  variant=\"outline\"\n                  className=\"flex items-center text-orange-600 border-orange-300 hover:bg-orange-50\"\n                >\n                  <AlertCircle className=\"h-4 w-4 mr-2\" />\n                  Reset Dati Cantiere\n                </Button>\n              </div>\n\n              <div className=\"mt-4 p-3 bg-blue-50 rounded-lg\">\n                <p className=\"text-sm text-blue-800\">\n                  <strong>Suggerimento:</strong> Se il problema persiste, prova a selezionare nuovamente un cantiere \n                  dalla pagina principale o contatta l'amministratore del sistema.\n                </p>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    )\n  }\n\n  // Se tutto è ok, renderizza i children\n  return <>{children}</>\n}\n\n/**\n * Hook per utilizzare il CantiereErrorBoundary in modo condizionale\n */\nexport function useCantiereErrorBoundary() {\n  const { isValidCantiere, isLoading, error } = useCantiere()\n  \n  return {\n    shouldShowError: !isLoading && (!isValidCantiere || error),\n    isLoading,\n    error\n  }\n}\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;AARA;;;;;;;AAqBO,SAAS,sBAAsB,EACpC,QAAQ,EACR,QAAQ,EACR,iBAAiB,IAAI,EACrB,UAAU,WAAW,EACM;;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAE1F,wCAAwC;IACxC,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;8BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,6LAAC;oCAAE,WAAU;8CAAoC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOxD;IAEA,+DAA+D;IAC/D,IAAI,SAAS,CAAC,iBAAiB;QAC7B,MAAM,eAAe,SAAS;QAE9B,IAAI,UAAU;YACZ,qBAAO;0BAAG;;QACZ;QAEA,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAGb,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAI5C,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;;sDACb,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC,oIAAA,CAAA,mBAAgB;;8DACf,6LAAC;8DAAO;;;;;;gDAAgB;gDAAE;;;;;;;;;;;;;;;;;;;;;;;;kCAOlC,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,qNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAI7C,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAK,WAAU;8DAA4B;;;;;;8DAC5C,6LAAC;oDAAK,WAAU;8DAAsB,cAAc;;;;;;;;;;;;sDAEtD,6LAAC;;8DACC,6LAAC;oDAAK,WAAU;8DAA4B;;;;;;8DAC5C,6LAAC;oDAAK,WAAU;8DAAsB,UAAU,YAAY;;;;;;;;;;;;sDAE9D,6LAAC;;8DACC,6LAAC;oDAAK,WAAU;8DAA4B;;;;;;8DAC5C,6LAAC;oDAAK,WAAW,CAAC,KAAK,EAAE,kBAAkB,mBAAmB,gBAAgB;8DAC3E,kBAAkB,OAAO;;;;;;;;;;;;sDAG9B,6LAAC;;8DACC,6LAAC;oDAAK,WAAU;8DAA4B;;;;;;8DAC5C,6LAAC;oDAAK,WAAU;8DACb,uCAAgC,aAAa,OAAO,CAAC,yBAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQzF,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAgB;;;;;;;;;;;0CAEvC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;4CAGZ,gCACC,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,SAAQ;gDACR,WAAU;;kEAEV,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;4CAMzC,uBACC,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,SAAQ;gDACR,WAAU;;kEAEV,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAM1C,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS;oDACP,aAAa,UAAU,CAAC;oDACxB,aAAa,UAAU,CAAC;oDACxB,aAAa,UAAU,CAAC;oDACxB,OAAO,QAAQ,CAAC,MAAM;gDACxB;gDACA,SAAQ;gDACR,WAAU;;kEAEV,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAK5C,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;8DAAO;;;;;;gDAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAS9C;IAEA,uCAAuC;IACvC,qBAAO;kBAAG;;AACZ;GA3JgB;;QAMC,qIAAA,CAAA,YAAS;QACwD,8HAAA,CAAA,cAAW;;;KAP7E;AAgKT,SAAS;;IACd,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAExD,OAAO;QACL,iBAAiB,CAAC,aAAa,CAAC,CAAC,mBAAmB,KAAK;QACzD;QACA;IACF;AACF;IARgB;;QACgC,8HAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 755, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 807, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,6LAAC,2MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B;KArBS", "debugId": null}}, {"offset": {"line": 858, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/utils/softColors.ts"], "sourcesContent": ["/**\n * Palette di colori morbidi per il sistema CMS\n * Evita il rosso fuoco e usa tonalità più professionali\n */\n\nexport const SOFT_COLORS = {\n  // Stati di successo - Verde morbido\n  SUCCESS: {\n    bg: 'bg-emerald-50',\n    text: 'text-emerald-700',\n    border: 'border-emerald-200',\n    hover: 'hover:bg-emerald-100',\n    hex: '#10b981' // emerald-500\n  },\n\n  // Stati di warning - Giallo ambra/senape\n  WARNING: {\n    bg: 'bg-amber-50',\n    text: 'text-amber-700',\n    border: 'border-amber-200',\n    hover: 'hover:bg-amber-100',\n    hex: '#f59e0b' // amber-500\n  },\n\n  // Stati di attenzione - Arancione tenue\n  ATTENTION: {\n    bg: 'bg-orange-50',\n    text: 'text-orange-700',\n    border: 'border-orange-200',\n    hover: 'hover:bg-orange-100',\n    hex: '#ea580c' // orange-600\n  },\n\n  // Stati di errore - Rosso morbido (non fuoco)\n  ERROR: {\n    bg: 'bg-rose-50',\n    text: 'text-rose-700',\n    border: 'border-rose-200',\n    hover: 'hover:bg-rose-100',\n    hex: '#e11d48' // rose-600\n  },\n\n  // Stati informativi - Blu morbido\n  INFO: {\n    bg: 'bg-sky-50',\n    text: 'text-sky-700',\n    border: 'border-sky-200',\n    hover: 'hover:bg-sky-100',\n    hex: '#0284c7' // sky-600\n  },\n\n  // Stati neutri - Grigio\n  NEUTRAL: {\n    bg: 'bg-slate-50',\n    text: 'text-slate-700',\n    border: 'border-slate-200',\n    hover: 'hover:bg-slate-100',\n    hex: '#475569' // slate-600\n  },\n\n  // Stati di progresso - Indaco\n  PROGRESS: {\n    bg: 'bg-indigo-50',\n    text: 'text-indigo-700',\n    border: 'border-indigo-200',\n    hover: 'hover:bg-indigo-100',\n    hex: '#4f46e5' // indigo-600\n  }\n}\n\n/**\n * Colori specifici per stati bobine\n */\nexport const BOBINA_COLORS = {\n  DISPONIBILE: SOFT_COLORS.SUCCESS,\n  IN_USO: SOFT_COLORS.PROGRESS,\n  TERMINATA: SOFT_COLORS.NEUTRAL,\n  OVER: SOFT_COLORS.WARNING, // Giallo ambra invece di rosso\n  ERRORE: SOFT_COLORS.ERROR\n}\n\n/**\n * Colori specifici per stati cavi\n */\nexport const CAVO_COLORS = {\n  DA_INSTALLARE: SOFT_COLORS.NEUTRAL,\n  INSTALLATO: SOFT_COLORS.SUCCESS,\n  COLLEGATO_PARTENZA: SOFT_COLORS.INFO,\n  COLLEGATO_ARRIVO: SOFT_COLORS.INFO,\n  COLLEGATO: SOFT_COLORS.PROGRESS,\n  CERTIFICATO: SOFT_COLORS.SUCCESS,\n  SPARE: SOFT_COLORS.WARNING,\n  ERRORE: SOFT_COLORS.ERROR\n}\n\n/**\n * Colori specifici per stati comande\n */\nexport const COMANDA_COLORS = {\n  ATTIVA: SOFT_COLORS.SUCCESS,\n  COMPLETATA: SOFT_COLORS.PROGRESS,\n  ANNULLATA: SOFT_COLORS.NEUTRAL,\n  IN_CORSO: SOFT_COLORS.INFO,\n  ERRORE: SOFT_COLORS.ERROR\n}\n\n/**\n * Funzioni helper per ottenere classi CSS\n */\nexport const getSoftColorClasses = (colorType: keyof typeof SOFT_COLORS) => {\n  const color = SOFT_COLORS[colorType]\n  return {\n    badge: `${color.bg} ${color.text} ${color.border}`,\n    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,\n    alert: `${color.bg} ${color.text} ${color.border}`,\n    text: color.text,\n    bg: color.bg,\n    border: color.border,\n    hover: color.hover,\n    hex: color.hex\n  }\n}\n\nexport const getBobinaColorClasses = (stato: string) => {\n  const normalizedStato = stato?.toUpperCase() as keyof typeof BOBINA_COLORS\n  const color = BOBINA_COLORS[normalizedStato] || BOBINA_COLORS.ERRORE\n  \n  return {\n    badge: `${color.bg} ${color.text} ${color.border}`,\n    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,\n    alert: `${color.bg} ${color.text} ${color.border}`,\n    text: color.text,\n    bg: color.bg,\n    border: color.border,\n    hover: color.hover,\n    hex: color.hex\n  }\n}\n\nexport const getCavoColorClasses = (stato: string) => {\n  const normalizedStato = stato?.toUpperCase().replace(/\\s+/g, '_') as keyof typeof CAVO_COLORS\n  const color = CAVO_COLORS[normalizedStato] || CAVO_COLORS.ERRORE\n  \n  return {\n    badge: `${color.bg} ${color.text} ${color.border}`,\n    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,\n    alert: `${color.bg} ${color.text} ${color.border}`,\n    text: color.text,\n    bg: color.bg,\n    border: color.border,\n    hover: color.hover,\n    hex: color.hex\n  }\n}\n\nexport const getComandaColorClasses = (stato: string) => {\n  const normalizedStato = stato?.toUpperCase().replace(/\\s+/g, '_') as keyof typeof COMANDA_COLORS\n  const color = COMANDA_COLORS[normalizedStato] || COMANDA_COLORS.ERRORE\n  \n  return {\n    badge: `${color.bg} ${color.text} ${color.border}`,\n    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,\n    alert: `${color.bg} ${color.text} ${color.border}`,\n    text: color.text,\n    bg: color.bg,\n    border: color.border,\n    hover: color.hover,\n    hex: color.hex\n  }\n}\n\n/**\n * Colori per percentuali di progresso\n */\nexport const getProgressColor = (percentage: number) => {\n  if (percentage >= 90) return SOFT_COLORS.SUCCESS\n  if (percentage >= 70) return SOFT_COLORS.PROGRESS\n  if (percentage >= 50) return SOFT_COLORS.INFO\n  if (percentage >= 30) return SOFT_COLORS.WARNING\n  return SOFT_COLORS.ATTENTION\n}\n\n/**\n * Colori per priorità\n */\nexport const PRIORITY_COLORS = {\n  ALTA: SOFT_COLORS.ERROR,\n  MEDIA: SOFT_COLORS.WARNING,\n  BASSA: SOFT_COLORS.INFO,\n  NORMALE: SOFT_COLORS.NEUTRAL\n}\n\nexport const getPriorityColorClasses = (priority: string) => {\n  const normalizedPriority = priority?.toUpperCase() as keyof typeof PRIORITY_COLORS\n  const color = PRIORITY_COLORS[normalizedPriority] || PRIORITY_COLORS.NORMALE\n  \n  return {\n    badge: `${color.bg} ${color.text} ${color.border}`,\n    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,\n    alert: `${color.bg} ${color.text} ${color.border}`,\n    text: color.text,\n    bg: color.bg,\n    border: color.border,\n    hover: color.hover,\n    hex: color.hex\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;AAEM,MAAM,cAAc;IACzB,oCAAoC;IACpC,SAAS;QACP,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,cAAc;IAC/B;IAEA,yCAAyC;IACzC,SAAS;QACP,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,YAAY;IAC7B;IAEA,wCAAwC;IACxC,WAAW;QACT,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,aAAa;IAC9B;IAEA,8CAA8C;IAC9C,OAAO;QACL,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,WAAW;IAC5B;IAEA,kCAAkC;IAClC,MAAM;QACJ,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,UAAU;IAC3B;IAEA,wBAAwB;IACxB,SAAS;QACP,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,YAAY;IAC7B;IAEA,8BAA8B;IAC9B,UAAU;QACR,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,aAAa;IAC9B;AACF;AAKO,MAAM,gBAAgB;IAC3B,aAAa,YAAY,OAAO;IAChC,QAAQ,YAAY,QAAQ;IAC5B,WAAW,YAAY,OAAO;IAC9B,MAAM,YAAY,OAAO;IACzB,QAAQ,YAAY,KAAK;AAC3B;AAKO,MAAM,cAAc;IACzB,eAAe,YAAY,OAAO;IAClC,YAAY,YAAY,OAAO;IAC/B,oBAAoB,YAAY,IAAI;IACpC,kBAAkB,YAAY,IAAI;IAClC,WAAW,YAAY,QAAQ;IAC/B,aAAa,YAAY,OAAO;IAChC,OAAO,YAAY,OAAO;IAC1B,QAAQ,YAAY,KAAK;AAC3B;AAKO,MAAM,iBAAiB;IAC5B,QAAQ,YAAY,OAAO;IAC3B,YAAY,YAAY,QAAQ;IAChC,WAAW,YAAY,OAAO;IAC9B,UAAU,YAAY,IAAI;IAC1B,QAAQ,YAAY,KAAK;AAC3B;AAKO,MAAM,sBAAsB,CAAC;IAClC,MAAM,QAAQ,WAAW,CAAC,UAAU;IACpC,OAAO;QACL,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,QAAQ,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,CAAC,EAAE,MAAM,KAAK,EAAE;QAClE,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,EAAE;QACZ,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,KAAK;QAClB,KAAK,MAAM,GAAG;IAChB;AACF;AAEO,MAAM,wBAAwB,CAAC;IACpC,MAAM,kBAAkB,OAAO;IAC/B,MAAM,QAAQ,aAAa,CAAC,gBAAgB,IAAI,cAAc,MAAM;IAEpE,OAAO;QACL,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,QAAQ,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,CAAC,EAAE,MAAM,KAAK,EAAE;QAClE,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,EAAE;QACZ,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,KAAK;QAClB,KAAK,MAAM,GAAG;IAChB;AACF;AAEO,MAAM,sBAAsB,CAAC;IAClC,MAAM,kBAAkB,OAAO,cAAc,QAAQ,QAAQ;IAC7D,MAAM,QAAQ,WAAW,CAAC,gBAAgB,IAAI,YAAY,MAAM;IAEhE,OAAO;QACL,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,QAAQ,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,CAAC,EAAE,MAAM,KAAK,EAAE;QAClE,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,EAAE;QACZ,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,KAAK;QAClB,KAAK,MAAM,GAAG;IAChB;AACF;AAEO,MAAM,yBAAyB,CAAC;IACrC,MAAM,kBAAkB,OAAO,cAAc,QAAQ,QAAQ;IAC7D,MAAM,QAAQ,cAAc,CAAC,gBAAgB,IAAI,eAAe,MAAM;IAEtE,OAAO;QACL,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,QAAQ,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,CAAC,EAAE,MAAM,KAAK,EAAE;QAClE,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,EAAE;QACZ,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,KAAK;QAClB,KAAK,MAAM,GAAG;IAChB;AACF;AAKO,MAAM,mBAAmB,CAAC;IAC/B,IAAI,cAAc,IAAI,OAAO,YAAY,OAAO;IAChD,IAAI,cAAc,IAAI,OAAO,YAAY,QAAQ;IACjD,IAAI,cAAc,IAAI,OAAO,YAAY,IAAI;IAC7C,IAAI,cAAc,IAAI,OAAO,YAAY,OAAO;IAChD,OAAO,YAAY,SAAS;AAC9B;AAKO,MAAM,kBAAkB;IAC7B,MAAM,YAAY,KAAK;IACvB,OAAO,YAAY,OAAO;IAC1B,OAAO,YAAY,IAAI;IACvB,SAAS,YAAY,OAAO;AAC9B;AAEO,MAAM,0BAA0B,CAAC;IACtC,MAAM,qBAAqB,UAAU;IACrC,MAAM,QAAQ,eAAe,CAAC,mBAAmB,IAAI,gBAAgB,OAAO;IAE5E,OAAO;QACL,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,QAAQ,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,CAAC,EAAE,MAAM,KAAK,EAAE;QAClE,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,EAAE;QACZ,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,KAAK;QAClB,KAAK,MAAM,GAAG;IAChB;AACF", "debugId": null}}, {"offset": {"line": 1047, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm border-collapse\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"data-[state=selected]:bg-muted border-b\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;YAC9D,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2CACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 1185, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        data-slot=\"input\"\n        className={cn(\n          \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n          \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\n\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1222, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 1471, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Popover = PopoverPrimitive.Root\n\nconst PopoverTrigger = PopoverPrimitive.Trigger\n\nconst PopoverContent = React.forwardRef<\n  React.ElementRef<typeof PopoverPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\n  <PopoverPrimitive.Portal>\n    <PopoverPrimitive.Content\n      ref={ref}\n      align={align}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </PopoverPrimitive.Portal>\n))\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\n\nexport { Popover, PopoverTrigger, PopoverContent }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,UAAU,sKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,sKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,KAAK;YACL,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8aACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,sKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1519, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/common/FilterableTable.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useMemo } from 'react'\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from '@/components/ui/table'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport {\n  Popover,\n  PopoverContent,\n  PopoverTrigger,\n} from '@/components/ui/popover'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport {\n  Filter,\n  ChevronDown,\n  ChevronUp,\n  ArrowUpDown,\n  ArrowUp,\n  ArrowDown,\n  X,\n  ChevronLeft,\n  ChevronRight,\n  ChevronsLeft,\n  ChevronsRight\n} from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\nexport interface ColumnDef {\n  field: string\n  headerName: string\n  dataType?: 'text' | 'number' | 'date'\n  align?: 'left' | 'center' | 'right'\n  width?: number\n  disableFilter?: boolean\n  disableSort?: boolean\n  headerStyle?: React.CSSProperties\n  cellStyle?: React.CSSProperties\n  renderHeader?: () => React.ReactNode\n  renderCell?: (row: any) => React.ReactNode\n}\n\ninterface FilterableTableProps {\n  data: any[]\n  columns: ColumnDef[]\n  loading?: boolean\n  emptyMessage?: string\n  onFilteredDataChange?: (filteredData: any[]) => void\n  renderRow?: (row: any, index: number) => React.ReactNode\n  className?: string\n  pagination?: boolean\n  defaultRowsPerPage?: number\n}\n\ninterface SortConfig {\n  key: string | null\n  direction: 'asc' | 'desc' | null\n}\n\ninterface FilterConfig {\n  [key: string]: {\n    type: 'text' | 'select' | 'number'\n    value: string | string[]\n    operator?: 'contains' | 'equals' | 'gt' | 'lt' | 'gte' | 'lte'\n  }\n}\n\nexport default function FilterableTable({\n  data = [],\n  columns = [],\n  loading = false,\n  emptyMessage = 'Nessun dato disponibile',\n  onFilteredDataChange,\n  renderRow,\n  className,\n  pagination = true,\n  defaultRowsPerPage = 25\n}: FilterableTableProps) {\n  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: null, direction: null })\n  const [filters, setFilters] = useState<FilterConfig>({})\n  const [openFilters, setOpenFilters] = useState<{ [key: string]: boolean }>({})\n  const [currentPage, setCurrentPage] = useState(0)\n  const [rowsPerPage, setRowsPerPage] = useState(defaultRowsPerPage)\n\n  // Get unique values for select filters\n  const getUniqueValues = (field: string) => {\n    return [...new Set(data.map(item => item[field]).filter(Boolean))].sort()\n  }\n\n  // Apply filters and sorting\n  const filteredAndSortedData = useMemo(() => {\n    let filtered = [...data]\n\n    // Apply filters\n    Object.entries(filters).forEach(([field, filterConfig]) => {\n      if (!filterConfig.value || \n          (Array.isArray(filterConfig.value) && filterConfig.value.length === 0) ||\n          (typeof filterConfig.value === 'string' && filterConfig.value.trim() === '')) {\n        return\n      }\n\n      filtered = filtered.filter(item => {\n        const itemValue = item[field]\n        \n        if (filterConfig.type === 'select') {\n          const selectedValues = Array.isArray(filterConfig.value) ? filterConfig.value : [filterConfig.value]\n          return selectedValues.includes(itemValue)\n        }\n        \n        if (filterConfig.type === 'text') {\n          const searchValue = (filterConfig.value as string).toLowerCase()\n          const cellValue = String(itemValue || '').toLowerCase()\n          \n          if (filterConfig.operator === 'equals') {\n            return cellValue === searchValue\n          }\n          return cellValue.includes(searchValue)\n        }\n        \n        if (filterConfig.type === 'number') {\n          const numValue = parseFloat(itemValue)\n          const filterValue = parseFloat(filterConfig.value as string)\n          \n          if (isNaN(numValue) || isNaN(filterValue)) return false\n          \n          switch (filterConfig.operator) {\n            case 'equals': return numValue === filterValue\n            case 'gt': return numValue > filterValue\n            case 'lt': return numValue < filterValue\n            case 'gte': return numValue >= filterValue\n            case 'lte': return numValue <= filterValue\n            default: return numValue === filterValue\n          }\n        }\n        \n        return true\n      })\n    })\n\n    // Apply sorting\n    if (sortConfig.key && sortConfig.direction) {\n      filtered.sort((a, b) => {\n        const aValue = a[sortConfig.key!]\n        const bValue = b[sortConfig.key!]\n        \n        // Handle null/undefined values\n        if (aValue == null && bValue == null) return 0\n        if (aValue == null) return sortConfig.direction === 'asc' ? -1 : 1\n        if (bValue == null) return sortConfig.direction === 'asc' ? 1 : -1\n        \n        // Determine if values are numbers\n        const aNum = parseFloat(aValue)\n        const bNum = parseFloat(bValue)\n        const isNumeric = !isNaN(aNum) && !isNaN(bNum)\n        \n        let comparison = 0\n        if (isNumeric) {\n          comparison = aNum - bNum\n        } else {\n          comparison = String(aValue).localeCompare(String(bValue))\n        }\n        \n        return sortConfig.direction === 'asc' ? comparison : -comparison\n      })\n    }\n\n    return filtered\n  }, [data, filters, sortConfig])\n\n  // Calculate paginated data\n  const paginatedData = useMemo(() => {\n    if (!pagination) return filteredAndSortedData\n\n    const startIndex = currentPage * rowsPerPage\n    const endIndex = startIndex + rowsPerPage\n    return filteredAndSortedData.slice(startIndex, endIndex)\n  }, [filteredAndSortedData, currentPage, rowsPerPage, pagination])\n\n  // Reset page when filters change\n  useEffect(() => {\n    setCurrentPage(0)\n  }, [filters])\n\n  // Calculate pagination info\n  const totalPages = Math.ceil(filteredAndSortedData.length / rowsPerPage)\n  const startRow = currentPage * rowsPerPage + 1\n  const endRow = Math.min((currentPage + 1) * rowsPerPage, filteredAndSortedData.length)\n\n  // Notify parent of filtered data changes\n  useEffect(() => {\n    if (onFilteredDataChange) {\n      onFilteredDataChange(filteredAndSortedData)\n    }\n  }, [filteredAndSortedData]) // Rimuoviamo onFilteredDataChange dalle dipendenze per evitare loop infiniti\n\n  const handleSort = (field: string) => {\n    const column = columns.find(col => col.field === field)\n    if (column?.disableSort) return\n\n    setSortConfig(prev => {\n      if (prev.key === field) {\n        if (prev.direction === 'asc') return { key: field, direction: 'desc' }\n        if (prev.direction === 'desc') return { key: null, direction: null }\n      }\n      return { key: field, direction: 'asc' }\n    })\n  }\n\n  const updateFilter = (field: string, filterConfig: Partial<FilterConfig[string]>) => {\n    setFilters(prev => ({\n      ...prev,\n      [field]: { ...prev[field], ...filterConfig }\n    }))\n  }\n\n  const clearFilter = (field: string) => {\n    setFilters(prev => {\n      const newFilters = { ...prev }\n      delete newFilters[field]\n      return newFilters\n    })\n  }\n\n  const clearAllFilters = () => {\n    setFilters({})\n  }\n\n  const getSortIcon = (field: string) => {\n    if (sortConfig.key !== field) return <ArrowUpDown className=\"h-3 w-3\" />\n    if (sortConfig.direction === 'asc') return <ArrowUp className=\"h-3 w-3\" />\n    if (sortConfig.direction === 'desc') return <ArrowDown className=\"h-3 w-3\" />\n    return <ArrowUpDown className=\"h-3 w-3\" />\n  }\n\n  const hasActiveFilters = Object.keys(filters).length > 0\n\n  if (loading) {\n    return (\n      <Card className={className}>\n        <CardContent className=\"p-6\">\n          <div className=\"text-center\">Caricamento...</div>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  return (\n    <div className={className}>\n      {/* Active filters display */}\n      {hasActiveFilters && (\n        <div className=\"mb-4 flex flex-wrap gap-2 items-center\">\n          <span className=\"text-sm text-muted-foreground\">Filtri attivi:</span>\n          {Object.entries(filters).map(([field, filterConfig]) => {\n            const column = columns.find(col => col.field === field)\n            if (!column) return null\n            \n            const displayValue = Array.isArray(filterConfig.value) \n              ? filterConfig.value.join(', ')\n              : String(filterConfig.value)\n            \n            return (\n              <Badge key={field} variant=\"secondary\" className=\"gap-1\">\n                {column.headerName}: {displayValue}\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  className=\"h-auto p-0 hover:bg-transparent\"\n                  onClick={() => clearFilter(field)}\n                >\n                  <X className=\"h-3 w-3\" />\n                </Button>\n              </Badge>\n            )\n          })}\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={clearAllFilters}\n            className=\"h-6 px-2 text-xs\"\n          >\n            Pulisci tutti\n          </Button>\n        </div>\n      )}\n\n      {/* Table */}\n      <Card>\n        <CardContent className=\"p-0\">\n          <Table>\n            <TableHeader>\n              <TableRow className=\"bg-mariner-50 hover:bg-mariner-50\">\n                {columns.map((column) => (\n                  <TableHead\n                    key={column.field}\n                    className={cn(\n                      \"font-semibold text-mariner-900 border-b border-mariner-200\",\n                      column.align === 'center' && \"text-center\",\n                      column.align === 'right' && \"text-right\"\n                    )}\n                    style={{ width: column.width, ...column.headerStyle }}\n                  >\n                    {column.renderHeader ? (\n                      column.renderHeader()\n                    ) : (\n                      <div className=\"relative group\">\n                        <div className=\"flex items-center justify-between w-full\">\n                          <span className=\"truncate\">{column.headerName}</span>\n\n                          {/* Compact icons container - only visible on hover */}\n                          <div className=\"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\n                            {/* Sort button */}\n                            {!column.disableSort && (\n                              <Button\n                                variant=\"ghost\"\n                                size=\"sm\"\n                                className=\"h-4 w-4 p-0 hover:bg-mariner-100\"\n                                onClick={() => handleSort(column.field)}\n                              >\n                                {getSortIcon(column.field)}\n                              </Button>\n                            )}\n\n                            {/* Filter button */}\n                            {!column.disableFilter && (\n                              <Popover\n                                open={openFilters[column.field]}\n                                onOpenChange={(open) => setOpenFilters(prev => ({ ...prev, [column.field]: open }))}\n                              >\n                                <PopoverTrigger asChild>\n                                  <Button\n                                    variant=\"ghost\"\n                                    size=\"sm\"\n                                    className={cn(\n                                      \"h-4 w-4 p-0 hover:bg-mariner-100\",\n                                      filters[column.field] && \"text-mariner-600 opacity-100\"\n                                    )}\n                                  >\n                                    <Filter className=\"h-2.5 w-2.5\" />\n                                  </Button>\n                                </PopoverTrigger>\n                                <PopoverContent className=\"w-64\" align=\"start\">\n                                  <FilterContent\n                                    column={column}\n                                    data={data}\n                                    currentFilter={filters[column.field]}\n                                    onFilterChange={(filterConfig) => updateFilter(column.field, filterConfig)}\n                                    onClearFilter={() => clearFilter(column.field)}\n                                    getUniqueValues={() => getUniqueValues(column.field)}\n                                  />\n                                </PopoverContent>\n                              </Popover>\n                            )}\n                          </div>\n                        </div>\n\n                        {/* Active filter indicator */}\n                        {filters[column.field] && (\n                          <div className=\"absolute -top-1 -right-1 h-2 w-2 bg-mariner-600 rounded-full\"></div>\n                        )}\n                      </div>\n                    )}\n                  </TableHead>\n                ))}\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {paginatedData.length > 0 ? (\n                paginatedData.map((row, index) => (\n                  renderRow ? (\n                    renderRow(row, currentPage * rowsPerPage + index)\n                  ) : (\n                    <TableRow\n                      key={index}\n                      className=\"hover:bg-mariner-50 border-b border-mariner-100\"\n                    >\n                      {columns.map((column) => (\n                        <TableCell\n                          key={column.field}\n                          className={cn(\n                            \"py-2 px-4\",\n                            column.align === 'center' && \"text-center\",\n                            column.align === 'right' && \"text-right\"\n                          )}\n                          style={column.cellStyle}\n                        >\n                          {column.renderCell ? column.renderCell(row) : row[column.field]}\n                        </TableCell>\n                      ))}\n                    </TableRow>\n                  )\n                ))\n              ) : (\n                <TableRow>\n                  <TableCell colSpan={columns.length} className=\"text-center py-8 text-muted-foreground\">\n                    {emptyMessage}\n                  </TableCell>\n                </TableRow>\n              )}\n            </TableBody>\n          </Table>\n        </CardContent>\n      </Card>\n\n      {/* Pagination Controls */}\n      {pagination && filteredAndSortedData.length > 0 && (\n        <div className=\"flex items-center justify-between mt-4\">\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-sm text-muted-foreground\">\n              Righe per pagina:\n            </span>\n            <Select\n              value={rowsPerPage.toString()}\n              onValueChange={(value) => {\n                setRowsPerPage(Number(value))\n                setCurrentPage(0)\n              }}\n            >\n              <SelectTrigger className=\"w-20\">\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"10\">10</SelectItem>\n                <SelectItem value=\"25\">25</SelectItem>\n                <SelectItem value=\"50\">50</SelectItem>\n                <SelectItem value=\"100\">100</SelectItem>\n                <SelectItem value={filteredAndSortedData.length.toString()}>Tutto</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-sm text-muted-foreground\">\n              {filteredAndSortedData.length > 0 ? `${startRow}-${endRow} di ${filteredAndSortedData.length}` : '0 di 0'}\n            </span>\n\n            <div className=\"flex items-center space-x-1\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setCurrentPage(0)}\n                disabled={currentPage === 0}\n                className=\"h-8 w-8 p-0\"\n              >\n                <ChevronsLeft className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setCurrentPage(prev => Math.max(0, prev - 1))}\n                disabled={currentPage === 0}\n                className=\"h-8 w-8 p-0\"\n              >\n                <ChevronLeft className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setCurrentPage(prev => Math.min(totalPages - 1, prev + 1))}\n                disabled={currentPage >= totalPages - 1}\n                className=\"h-8 w-8 p-0\"\n              >\n                <ChevronRight className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setCurrentPage(totalPages - 1)}\n                disabled={currentPage >= totalPages - 1}\n                className=\"h-8 w-8 p-0\"\n              >\n                <ChevronsRight className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n\n// Filter content component\ninterface FilterContentProps {\n  column: ColumnDef\n  data: any[]\n  currentFilter?: FilterConfig[string]\n  onFilterChange: (filterConfig: Partial<FilterConfig[string]>) => void\n  onClearFilter: () => void\n  getUniqueValues: () => any[]\n}\n\nfunction FilterContent({\n  column,\n  currentFilter,\n  onFilterChange,\n  onClearFilter,\n  getUniqueValues\n}: FilterContentProps) {\n  const [localValue, setLocalValue] = useState(currentFilter?.value || '')\n  const [operator, setOperator] = useState(currentFilter?.operator || 'contains')\n\n  const uniqueValues = getUniqueValues()\n  const isSelectType = column.dataType !== 'number' && uniqueValues.length <= 20\n  const isNumberType = column.dataType === 'number'\n\n  const applyFilter = () => {\n    if (isSelectType) {\n      onFilterChange({\n        type: 'select',\n        value: Array.isArray(localValue) ? localValue : [localValue]\n      })\n    } else if (isNumberType) {\n      onFilterChange({\n        type: 'number',\n        value: localValue as string,\n        operator\n      })\n    } else {\n      onFilterChange({\n        type: 'text',\n        value: localValue as string,\n        operator\n      })\n    }\n  }\n\n  return (\n    <div className=\"space-y-3\">\n      <div className=\"font-medium text-sm\">Filtra {column.headerName}</div>\n      \n      {isSelectType ? (\n        <div className=\"space-y-2 max-h-48 overflow-y-auto\">\n          {uniqueValues.map(value => (\n            <div key={value} className=\"flex items-center space-x-2\">\n              <Checkbox\n                id={`filter-${value}`}\n                checked={Array.isArray(localValue) ? localValue.includes(value) : localValue === value}\n                onCheckedChange={(checked) => {\n                  if (Array.isArray(localValue)) {\n                    setLocalValue(checked \n                      ? [...localValue, value]\n                      : localValue.filter(v => v !== value)\n                    )\n                  } else {\n                    setLocalValue(checked ? [value] : [])\n                  }\n                }}\n              />\n              <label htmlFor={`filter-${value}`} className=\"text-sm\">\n                {value}\n              </label>\n            </div>\n          ))}\n        </div>\n      ) : (\n        <div className=\"space-y-2\">\n          {isNumberType && (\n            <Select value={operator} onValueChange={setOperator}>\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"equals\">Uguale a</SelectItem>\n                <SelectItem value=\"gt\">Maggiore di</SelectItem>\n                <SelectItem value=\"lt\">Minore di</SelectItem>\n                <SelectItem value=\"gte\">Maggiore o uguale</SelectItem>\n                <SelectItem value=\"lte\">Minore o uguale</SelectItem>\n              </SelectContent>\n            </Select>\n          )}\n          \n          {!isNumberType && (\n            <Select value={operator} onValueChange={setOperator}>\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"contains\">Contiene</SelectItem>\n                <SelectItem value=\"equals\">Uguale a</SelectItem>\n              </SelectContent>\n            </Select>\n          )}\n          \n          <Input\n            placeholder={`Cerca ${column.headerName.toLowerCase()}...`}\n            value={localValue as string}\n            onChange={(e) => setLocalValue(e.target.value)}\n            onKeyDown={(e) => e.key === 'Enter' && applyFilter()}\n          />\n        </div>\n      )}\n      \n      <div className=\"flex gap-2\">\n        <Button size=\"sm\" onClick={applyFilter}>\n          Applica\n        </Button>\n        <Button size=\"sm\" variant=\"outline\" onClick={onClearFilter}>\n          Pulisci\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAOA;AAKA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;;;AAzCA;;;;;;;;;;;;AAkFe,SAAS,gBAAgB,EACtC,OAAO,EAAE,EACT,UAAU,EAAE,EACZ,UAAU,KAAK,EACf,eAAe,yBAAyB,EACxC,oBAAoB,EACpB,SAAS,EACT,SAAS,EACT,aAAa,IAAI,EACjB,qBAAqB,EAAE,EACF;;IACrB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QAAE,KAAK;QAAM,WAAW;IAAK;IACtF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,CAAC;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B,CAAC;IAC5E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,uCAAuC;IACvC,MAAM,kBAAkB,CAAC;QACvB,OAAO;eAAI,IAAI,IAAI,KAAK,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC;SAAU,CAAC,IAAI;IACzE;IAEA,4BAA4B;IAC5B,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;0DAAE;YACpC,IAAI,WAAW;mBAAI;aAAK;YAExB,gBAAgB;YAChB,OAAO,OAAO,CAAC,SAAS,OAAO;kEAAC,CAAC,CAAC,OAAO,aAAa;oBACpD,IAAI,CAAC,aAAa,KAAK,IAClB,MAAM,OAAO,CAAC,aAAa,KAAK,KAAK,aAAa,KAAK,CAAC,MAAM,KAAK,KACnE,OAAO,aAAa,KAAK,KAAK,YAAY,aAAa,KAAK,CAAC,IAAI,OAAO,IAAK;wBAChF;oBACF;oBAEA,WAAW,SAAS,MAAM;0EAAC,CAAA;4BACzB,MAAM,YAAY,IAAI,CAAC,MAAM;4BAE7B,IAAI,aAAa,IAAI,KAAK,UAAU;gCAClC,MAAM,iBAAiB,MAAM,OAAO,CAAC,aAAa,KAAK,IAAI,aAAa,KAAK,GAAG;oCAAC,aAAa,KAAK;iCAAC;gCACpG,OAAO,eAAe,QAAQ,CAAC;4BACjC;4BAEA,IAAI,aAAa,IAAI,KAAK,QAAQ;gCAChC,MAAM,cAAc,AAAC,aAAa,KAAK,CAAY,WAAW;gCAC9D,MAAM,YAAY,OAAO,aAAa,IAAI,WAAW;gCAErD,IAAI,aAAa,QAAQ,KAAK,UAAU;oCACtC,OAAO,cAAc;gCACvB;gCACA,OAAO,UAAU,QAAQ,CAAC;4BAC5B;4BAEA,IAAI,aAAa,IAAI,KAAK,UAAU;gCAClC,MAAM,WAAW,WAAW;gCAC5B,MAAM,cAAc,WAAW,aAAa,KAAK;gCAEjD,IAAI,MAAM,aAAa,MAAM,cAAc,OAAO;gCAElD,OAAQ,aAAa,QAAQ;oCAC3B,KAAK;wCAAU,OAAO,aAAa;oCACnC,KAAK;wCAAM,OAAO,WAAW;oCAC7B,KAAK;wCAAM,OAAO,WAAW;oCAC7B,KAAK;wCAAO,OAAO,YAAY;oCAC/B,KAAK;wCAAO,OAAO,YAAY;oCAC/B;wCAAS,OAAO,aAAa;gCAC/B;4BACF;4BAEA,OAAO;wBACT;;gBACF;;YAEA,gBAAgB;YAChB,IAAI,WAAW,GAAG,IAAI,WAAW,SAAS,EAAE;gBAC1C,SAAS,IAAI;sEAAC,CAAC,GAAG;wBAChB,MAAM,SAAS,CAAC,CAAC,WAAW,GAAG,CAAE;wBACjC,MAAM,SAAS,CAAC,CAAC,WAAW,GAAG,CAAE;wBAEjC,+BAA+B;wBAC/B,IAAI,UAAU,QAAQ,UAAU,MAAM,OAAO;wBAC7C,IAAI,UAAU,MAAM,OAAO,WAAW,SAAS,KAAK,QAAQ,CAAC,IAAI;wBACjE,IAAI,UAAU,MAAM,OAAO,WAAW,SAAS,KAAK,QAAQ,IAAI,CAAC;wBAEjE,kCAAkC;wBAClC,MAAM,OAAO,WAAW;wBACxB,MAAM,OAAO,WAAW;wBACxB,MAAM,YAAY,CAAC,MAAM,SAAS,CAAC,MAAM;wBAEzC,IAAI,aAAa;wBACjB,IAAI,WAAW;4BACb,aAAa,OAAO;wBACtB,OAAO;4BACL,aAAa,OAAO,QAAQ,aAAa,CAAC,OAAO;wBACnD;wBAEA,OAAO,WAAW,SAAS,KAAK,QAAQ,aAAa,CAAC;oBACxD;;YACF;YAEA,OAAO;QACT;yDAAG;QAAC;QAAM;QAAS;KAAW;IAE9B,2BAA2B;IAC3B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;kDAAE;YAC5B,IAAI,CAAC,YAAY,OAAO;YAExB,MAAM,aAAa,cAAc;YACjC,MAAM,WAAW,aAAa;YAC9B,OAAO,sBAAsB,KAAK,CAAC,YAAY;QACjD;iDAAG;QAAC;QAAuB;QAAa;QAAa;KAAW;IAEhE,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,eAAe;QACjB;oCAAG;QAAC;KAAQ;IAEZ,4BAA4B;IAC5B,MAAM,aAAa,KAAK,IAAI,CAAC,sBAAsB,MAAM,GAAG;IAC5D,MAAM,WAAW,cAAc,cAAc;IAC7C,MAAM,SAAS,KAAK,GAAG,CAAC,CAAC,cAAc,CAAC,IAAI,aAAa,sBAAsB,MAAM;IAErF,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,sBAAsB;gBACxB,qBAAqB;YACvB;QACF;oCAAG;QAAC;KAAsB,EAAE,6EAA6E;;IAEzG,MAAM,aAAa,CAAC;QAClB,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;QACjD,IAAI,QAAQ,aAAa;QAEzB,cAAc,CAAA;YACZ,IAAI,KAAK,GAAG,KAAK,OAAO;gBACtB,IAAI,KAAK,SAAS,KAAK,OAAO,OAAO;oBAAE,KAAK;oBAAO,WAAW;gBAAO;gBACrE,IAAI,KAAK,SAAS,KAAK,QAAQ,OAAO;oBAAE,KAAK;oBAAM,WAAW;gBAAK;YACrE;YACA,OAAO;gBAAE,KAAK;gBAAO,WAAW;YAAM;QACxC;IACF;IAEA,MAAM,eAAe,CAAC,OAAe;QACnC,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;oBAAE,GAAG,IAAI,CAAC,MAAM;oBAAE,GAAG,YAAY;gBAAC;YAC7C,CAAC;IACH;IAEA,MAAM,cAAc,CAAC;QACnB,WAAW,CAAA;YACT,MAAM,aAAa;gBAAE,GAAG,IAAI;YAAC;YAC7B,OAAO,UAAU,CAAC,MAAM;YACxB,OAAO;QACT;IACF;IAEA,MAAM,kBAAkB;QACtB,WAAW,CAAC;IACd;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,WAAW,GAAG,KAAK,OAAO,qBAAO,6LAAC,2NAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QAC5D,IAAI,WAAW,SAAS,KAAK,OAAO,qBAAO,6LAAC,+MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QAC9D,IAAI,WAAW,SAAS,KAAK,QAAQ,qBAAO,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QACjE,qBAAO,6LAAC,2NAAA,CAAA,cAAW;YAAC,WAAU;;;;;;IAChC;IAEA,MAAM,mBAAmB,OAAO,IAAI,CAAC,SAAS,MAAM,GAAG;IAEvD,IAAI,SAAS;QACX,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAW;sBACf,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;8BAAc;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAW;;YAEb,kCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAAgC;;;;;;oBAC/C,OAAO,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,OAAO,aAAa;wBACjD,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;wBACjD,IAAI,CAAC,QAAQ,OAAO;wBAEpB,MAAM,eAAe,MAAM,OAAO,CAAC,aAAa,KAAK,IACjD,aAAa,KAAK,CAAC,IAAI,CAAC,QACxB,OAAO,aAAa,KAAK;wBAE7B,qBACE,6LAAC,oIAAA,CAAA,QAAK;4BAAa,SAAQ;4BAAY,WAAU;;gCAC9C,OAAO,UAAU;gCAAC;gCAAG;8CACtB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,YAAY;8CAE3B,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;2BARL;;;;;oBAYhB;kCACA,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAOL,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC,oIAAA,CAAA,QAAK;;0CACJ,6LAAC,oIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;8CACjB,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,oIAAA,CAAA,YAAS;4CAER,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA,OAAO,KAAK,KAAK,YAAY,eAC7B,OAAO,KAAK,KAAK,WAAW;4CAE9B,OAAO;gDAAE,OAAO,OAAO,KAAK;gDAAE,GAAG,OAAO,WAAW;4CAAC;sDAEnD,OAAO,YAAY,GAClB,OAAO,YAAY,mBAEnB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAY,OAAO,UAAU;;;;;;0EAG7C,6LAAC;gEAAI,WAAU;;oEAEZ,CAAC,OAAO,WAAW,kBAClB,6LAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,IAAM,WAAW,OAAO,KAAK;kFAErC,YAAY,OAAO,KAAK;;;;;;oEAK5B,CAAC,OAAO,aAAa,kBACpB,6LAAC,sIAAA,CAAA,UAAO;wEACN,MAAM,WAAW,CAAC,OAAO,KAAK,CAAC;wEAC/B,cAAc,CAAC,OAAS,eAAe,CAAA,OAAQ,CAAC;oFAAE,GAAG,IAAI;oFAAE,CAAC,OAAO,KAAK,CAAC,EAAE;gFAAK,CAAC;;0FAEjF,6LAAC,sIAAA,CAAA,iBAAc;gFAAC,OAAO;0FACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oFACL,SAAQ;oFACR,MAAK;oFACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oCACA,OAAO,CAAC,OAAO,KAAK,CAAC,IAAI;8FAG3B,cAAA,6LAAC,yMAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;;;;;;;;;;;0FAGtB,6LAAC,sIAAA,CAAA,iBAAc;gFAAC,WAAU;gFAAO,OAAM;0FACrC,cAAA,6LAAC;oFACC,QAAQ;oFACR,MAAM;oFACN,eAAe,OAAO,CAAC,OAAO,KAAK,CAAC;oFACpC,gBAAgB,CAAC,eAAiB,aAAa,OAAO,KAAK,EAAE;oFAC7D,eAAe,IAAM,YAAY,OAAO,KAAK;oFAC7C,iBAAiB,IAAM,gBAAgB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oDAS9D,OAAO,CAAC,OAAO,KAAK,CAAC,kBACpB,6LAAC;wDAAI,WAAU;;;;;;;;;;;;2CAhEhB,OAAO,KAAK;;;;;;;;;;;;;;;0CAwEzB,6LAAC,oIAAA,CAAA,YAAS;0CACP,cAAc,MAAM,GAAG,IACtB,cAAc,GAAG,CAAC,CAAC,KAAK,QACtB,YACE,UAAU,KAAK,cAAc,cAAc,uBAE3C,6LAAC,oIAAA,CAAA,WAAQ;wCAEP,WAAU;kDAET,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,oIAAA,CAAA,YAAS;gDAER,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aACA,OAAO,KAAK,KAAK,YAAY,eAC7B,OAAO,KAAK,KAAK,WAAW;gDAE9B,OAAO,OAAO,SAAS;0DAEtB,OAAO,UAAU,GAAG,OAAO,UAAU,CAAC,OAAO,GAAG,CAAC,OAAO,KAAK,CAAC;+CAR1D,OAAO,KAAK;;;;;uCALhB;;;;8DAoBX,6LAAC,oIAAA,CAAA,WAAQ;8CACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;wCAAC,SAAS,QAAQ,MAAM;wCAAE,WAAU;kDAC3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUd,cAAc,sBAAsB,MAAM,GAAG,mBAC5C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAgC;;;;;;0CAGhD,6LAAC,qIAAA,CAAA,SAAM;gCACL,OAAO,YAAY,QAAQ;gCAC3B,eAAe,CAAC;oCACd,eAAe,OAAO;oCACtB,eAAe;gCACjB;;kDAEA,6LAAC,qIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;kDAEd,6LAAC,qIAAA,CAAA,gBAAa;;0DACZ,6LAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,6LAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,6LAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,6LAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;0DACxB,6LAAC,qIAAA,CAAA,aAAU;gDAAC,OAAO,sBAAsB,MAAM,CAAC,QAAQ;0DAAI;;;;;;;;;;;;;;;;;;;;;;;;kCAKlE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CACb,sBAAsB,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC,EAAE,OAAO,IAAI,EAAE,sBAAsB,MAAM,EAAE,GAAG;;;;;;0CAGnG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe;wCAC9B,UAAU,gBAAgB;wCAC1B,WAAU;kDAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;kDAE1B,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;wCACzD,UAAU,gBAAgB;wCAC1B,WAAU;kDAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAEzB,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,aAAa,GAAG,OAAO;wCACtE,UAAU,eAAe,aAAa;wCACtC,WAAU;kDAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;kDAE1B,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe,aAAa;wCAC3C,UAAU,eAAe,aAAa;wCACtC,WAAU;kDAEV,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzC;GA3ZwB;KAAA;AAuaxB,SAAS,cAAc,EACrB,MAAM,EACN,aAAa,EACb,cAAc,EACd,aAAa,EACb,eAAe,EACI;;IACnB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,SAAS;IACrE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,YAAY;IAEpE,MAAM,eAAe;IACrB,MAAM,eAAe,OAAO,QAAQ,KAAK,YAAY,aAAa,MAAM,IAAI;IAC5E,MAAM,eAAe,OAAO,QAAQ,KAAK;IAEzC,MAAM,cAAc;QAClB,IAAI,cAAc;YAChB,eAAe;gBACb,MAAM;gBACN,OAAO,MAAM,OAAO,CAAC,cAAc,aAAa;oBAAC;iBAAW;YAC9D;QACF,OAAO,IAAI,cAAc;YACvB,eAAe;gBACb,MAAM;gBACN,OAAO;gBACP;YACF;QACF,OAAO;YACL,eAAe;gBACb,MAAM;gBACN,OAAO;gBACP;YACF;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;oBAAsB;oBAAQ,OAAO,UAAU;;;;;;;YAE7D,6BACC,6LAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAA,sBAChB,6LAAC;wBAAgB,WAAU;;0CACzB,6LAAC,uIAAA,CAAA,WAAQ;gCACP,IAAI,CAAC,OAAO,EAAE,OAAO;gCACrB,SAAS,MAAM,OAAO,CAAC,cAAc,WAAW,QAAQ,CAAC,SAAS,eAAe;gCACjF,iBAAiB,CAAC;oCAChB,IAAI,MAAM,OAAO,CAAC,aAAa;wCAC7B,cAAc,UACV;+CAAI;4CAAY;yCAAM,GACtB,WAAW,MAAM,CAAC,CAAA,IAAK,MAAM;oCAEnC,OAAO;wCACL,cAAc,UAAU;4CAAC;yCAAM,GAAG,EAAE;oCACtC;gCACF;;;;;;0CAEF,6LAAC;gCAAM,SAAS,CAAC,OAAO,EAAE,OAAO;gCAAE,WAAU;0CAC1C;;;;;;;uBAhBK;;;;;;;;;qCAsBd,6LAAC;gBAAI,WAAU;;oBACZ,8BACC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAU,eAAe;;0CACtC,6LAAC,qIAAA,CAAA,gBAAa;0CACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;0CAEd,6LAAC,qIAAA,CAAA,gBAAa;;kDACZ,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAS;;;;;;kDAC3B,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAK;;;;;;kDACvB,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAK;;;;;;kDACvB,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;kDACxB,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;;;;;;;;;;;;;oBAK7B,CAAC,8BACA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAU,eAAe;;0CACtC,6LAAC,qIAAA,CAAA,gBAAa;0CACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;0CAEd,6LAAC,qIAAA,CAAA,gBAAa;;kDACZ,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAW;;;;;;kDAC7B,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAS;;;;;;;;;;;;;;;;;;kCAKjC,6LAAC,oIAAA,CAAA,QAAK;wBACJ,aAAa,CAAC,MAAM,EAAE,OAAO,UAAU,CAAC,WAAW,GAAG,GAAG,CAAC;wBAC1D,OAAO;wBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC7C,WAAW,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;;;;;;;0BAK7C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAK,SAAS;kCAAa;;;;;;kCAGxC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAK,SAAQ;wBAAU,SAAS;kCAAe;;;;;;;;;;;;;;;;;;AAMpE;IA/GS;MAAA", "debugId": null}}, {"offset": {"line": 2520, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/SmartCaviFilter.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useCallback, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Card, CardContent } from '@/components/ui/card'\nimport {\n  Search,\n  X,\n  CheckSquare,\n  Square,\n  Filter,\n  Settings,\n  SquareCheck,\n  SquareMinus\n} from 'lucide-react'\nimport { Cavo } from '@/types'\n\ninterface SmartCaviFilterProps {\n  cavi: Cavo[]\n  onFilteredDataChange?: (filteredCavi: Cavo[]) => void\n  loading?: boolean\n  selectionEnabled?: boolean\n  onSelectionToggle?: () => void\n  selectedCount?: number\n  totalCount?: number\n}\n\nexport default function SmartCaviFilter({\n  cavi = [],\n  onFilteredDataChange,\n  loading = false,\n  selectionEnabled = false,\n  onSelectionToggle,\n  selectedCount = 0,\n  totalCount = 0\n}: SmartCaviFilterProps) {\n  const [searchText, setSearchText] = useState('')\n  const [searchType, setSearchType] = useState<'contains' | 'equals'>('contains')\n\n  // Normalize string for search\n  const normalizeString = (str: string | null | undefined): string => {\n    if (!str) return ''\n    return str.toString().toLowerCase().trim()\n  }\n\n  // Extract cable info for advanced search\n  const getCavoInfo = (idCavo: string) => {\n    const match = idCavo.match(/^([A-Z]+)(\\d+)([A-Z]*)$/)\n    if (match) {\n      return {\n        prefix: match[1],\n        number: match[2],\n        suffix: match[3] || ''\n      }\n    }\n    return { prefix: '', number: idCavo, suffix: '' }\n  }\n\n  // Check if a cable matches a search term\n  const cavoMatchesTerm = useCallback((cavo: Cavo, term: string, exactMatch: boolean): boolean => {\n    const normalizedTerm = normalizeString(term)\n    \n    if (!normalizedTerm) return true\n\n    // Basic cable info\n    const cavoId = normalizeString(cavo.id_cavo)\n    const { prefix: cavoPrefix, number: cavoNumber, suffix: cavoSuffix } = getCavoInfo(cavo.id_cavo || '')\n    \n    // Cable properties\n    const tipologia = normalizeString(cavo.tipologia)\n    const formazione = normalizeString(cavo.formazione || cavo.sezione)\n    const utility = normalizeString(cavo.utility)\n    const sistema = normalizeString(cavo.sistema)\n    \n    // Locations\n    const ubicazionePartenza = normalizeString(cavo.da || cavo.ubicazione_partenza)\n    const ubicazioneArrivo = normalizeString(cavo.a || cavo.ubicazione_arrivo)\n    const utenzaPartenza = normalizeString(cavo.utenza_partenza)\n    const utenzaArrivo = normalizeString(cavo.utenza_arrivo)\n    \n    // Reel info\n    const bobina = normalizeString(cavo.id_bobina)\n    const bobinaDisplay = cavo.id_bobina === 'BOBINA_VUOTA' ? 'bobina vuota' :\n                         cavo.id_bobina === null ? '' :\n                         normalizeString(cavo.id_bobina)\n\n    // All text fields to search\n    const textFields = [\n      cavoId, cavoPrefix, cavoNumber, cavoSuffix, tipologia, formazione, utility, sistema,\n      ubicazionePartenza, ubicazioneArrivo, utenzaPartenza, utenzaArrivo,\n      bobina, bobinaDisplay\n    ]\n\n    // Numeric fields for range search\n    const numericFields = [\n      { value: cavo.metri_teorici, name: 'metri_teorici' },\n      { value: cavo.metratura_reale || cavo.metri_posati, name: 'metratura_reale' },\n      { value: parseFloat(formazione), name: 'formazione' }\n    ]\n\n    // Check for range queries (e.g., \">100\", \"<=50\")\n    const rangeMatch = normalizedTerm.match(/^([><=]+)(\\d+(?:\\.\\d+)?)$/)\n    if (rangeMatch) {\n      const operator = rangeMatch[1]\n      const value = parseFloat(rangeMatch[2])\n      \n      return numericFields.some(field => {\n        if (field.value == null || isNaN(field.value)) return false\n        \n        switch (operator) {\n          case '>': return field.value > value\n          case '>=': return field.value >= value\n          case '<': return field.value < value\n          case '<=': return field.value <= value\n          case '=': return field.value === value\n          default: return false\n        }\n      })\n    }\n\n    // Check for exact numeric match\n    const numericTerm = parseFloat(normalizedTerm)\n    if (!isNaN(numericTerm)) {\n      const numericMatch = numericFields.some(field => \n        field.value != null && !isNaN(field.value) && field.value === numericTerm\n      )\n      if (numericMatch) return true\n    }\n\n    // Text search\n    if (exactMatch) {\n      return textFields.some(field => field === normalizedTerm)\n    } else {\n      return textFields.some(field => field.includes(normalizedTerm))\n    }\n  }, [])\n\n  // Apply filter\n  const applyFilter = useCallback(() => {\n    if (!searchText.trim()) {\n      onFilteredDataChange?.(cavi)\n      return\n    }\n\n    // Split search terms by comma\n    const searchTerms = searchText.split(',')\n      .map(term => term.trim())\n      .filter(term => term.length > 0)\n\n    let filtered: Cavo[] = []\n\n    if (searchType === 'equals') {\n      if (searchTerms.length === 1) {\n        // Single term: exact search\n        filtered = cavi.filter(cavo => cavoMatchesTerm(cavo, searchTerms[0], true))\n      } else {\n        // Multiple terms: all must match (AND)\n        filtered = cavi.filter(cavo =>\n          searchTerms.every(term => cavoMatchesTerm(cavo, term, true))\n        )\n      }\n    } else {\n      // Contains search: at least one term must match (OR)\n      filtered = cavi.filter(cavo =>\n        searchTerms.some(term => cavoMatchesTerm(cavo, term, false))\n      )\n    }\n\n    onFilteredDataChange?.(filtered)\n  }, [searchText, searchType, cavi, cavoMatchesTerm]) // Rimuoviamo onFilteredDataChange dalle dipendenze\n\n  // Apply filter when dependencies change\n  useEffect(() => {\n    applyFilter()\n  }, [searchText, searchType, cavi, cavoMatchesTerm]) // Usiamo le dipendenze dirette invece di applyFilter\n\n  const handleSearchTextChange = (value: string) => {\n    setSearchText(value)\n  }\n\n  const clearFilter = () => {\n    setSearchText('')\n    setSearchType('contains')\n  }\n\n  return (\n    <Card className=\"mb-1\">\n      <CardContent className=\"p-1\">\n        <div className=\"flex items-center gap-1\">\n          {/* Search input - takes most space */}\n          <div className=\"flex-1 relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n            <Input\n              placeholder=\"Cerca per ID, sistema, utility, tipologia, ubicazione...\"\n              value={searchText}\n              onChange={(e) => handleSearchTextChange(e.target.value)}\n              disabled={loading}\n              className=\"pl-10 pr-10 h-8\"\n              aria-label=\"Campo di ricerca intelligente per cavi\"\n            />\n            {searchText && (\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"absolute right-1 top-1/2 transform -translate-y-1/2 h-5 w-5 p-0\"\n                onClick={clearFilter}\n              >\n                <X className=\"h-2.5 w-2.5\" />\n              </Button>\n            )}\n          </div>\n\n          {/* Search type selector */}\n          <div className=\"w-32\">\n            <Select value={searchType} onValueChange={(value: 'contains' | 'equals') => setSearchType(value)}>\n              <SelectTrigger className=\"h-8\">\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"contains\">Contiene</SelectItem>\n                <SelectItem value=\"equals\">Uguale a</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Advanced filters button - removed as per user request */}\n\n          {/* Clear button */}\n          {searchText && (\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={clearFilter}\n              disabled={loading}\n              className=\"transition-all duration-200 hover:scale-105\"\n              aria-label=\"Pulisci ricerca\"\n            >\n              <X className=\"h-4 w-4 mr-1\" />\n              Pulisci\n            </Button>\n          )}\n\n          {/* Selection toggle button - only show when there are items to select */}\n          {onSelectionToggle && totalCount > 0 && (\n            <Button\n              variant={selectionEnabled ? \"default\" : \"outline\"}\n              size=\"sm\"\n              onClick={onSelectionToggle}\n              className=\"flex items-center gap-2 transition-all duration-200 hover:scale-105\"\n              aria-label={selectionEnabled ? 'Disabilita modalità selezione' : 'Abilita modalità selezione'}\n            >\n              {selectionEnabled ? <SquareCheck className=\"h-4 w-4\" /> : <Square className=\"h-4 w-4\" />}\n              {selectionEnabled ? 'Disabilita Selezione' : 'Abilita Selezione'}\n            </Button>\n          )}\n\n          {/* Deselect all button - only show when selection is enabled and items are selected */}\n          {selectionEnabled && selectedCount > 0 && (\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => {\n                // This would need to be passed as a prop or handled by parent\n                // For now, we'll just show the button\n              }}\n              className=\"flex items-center gap-2 transition-all duration-200 hover:scale-105 text-orange-600 border-orange-300 hover:bg-orange-50\"\n              aria-label={`Deseleziona tutti i ${selectedCount} cavi selezionati`}\n            >\n              <SquareMinus className=\"h-4 w-4\" />\n              Deseleziona Tutto ({selectedCount})\n            </Button>\n          )}\n        </div>\n\n        {/* Search help text - ultra compact */}\n        {searchText && (\n          <div className=\"mt-0.5 text-xs text-muted-foreground\">\n            <div className=\"flex flex-wrap gap-1\">\n              <span>💡</span>\n              <span>• Virgole per multipli</span>\n              <span>• &gt;100, &lt;=50 per numeri</span>\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAOA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAdA;;;;;;;AAoCe,SAAS,gBAAgB,EACtC,OAAO,EAAE,EACT,oBAAoB,EACpB,UAAU,KAAK,EACf,mBAAmB,KAAK,EACxB,iBAAiB,EACjB,gBAAgB,CAAC,EACjB,aAAa,CAAC,EACO;;IACrB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAEpE,8BAA8B;IAC9B,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,KAAK,OAAO;QACjB,OAAO,IAAI,QAAQ,GAAG,WAAW,GAAG,IAAI;IAC1C;IAEA,yCAAyC;IACzC,MAAM,cAAc,CAAC;QACnB,MAAM,QAAQ,OAAO,KAAK,CAAC;QAC3B,IAAI,OAAO;YACT,OAAO;gBACL,QAAQ,KAAK,CAAC,EAAE;gBAChB,QAAQ,KAAK,CAAC,EAAE;gBAChB,QAAQ,KAAK,CAAC,EAAE,IAAI;YACtB;QACF;QACA,OAAO;YAAE,QAAQ;YAAI,QAAQ;YAAQ,QAAQ;QAAG;IAClD;IAEA,yCAAyC;IACzC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC,MAAY,MAAc;YAC7D,MAAM,iBAAiB,gBAAgB;YAEvC,IAAI,CAAC,gBAAgB,OAAO;YAE5B,mBAAmB;YACnB,MAAM,SAAS,gBAAgB,KAAK,OAAO;YAC3C,MAAM,EAAE,QAAQ,UAAU,EAAE,QAAQ,UAAU,EAAE,QAAQ,UAAU,EAAE,GAAG,YAAY,KAAK,OAAO,IAAI;YAEnG,mBAAmB;YACnB,MAAM,YAAY,gBAAgB,KAAK,SAAS;YAChD,MAAM,aAAa,gBAAgB,KAAK,UAAU,IAAI,KAAK,OAAO;YAClE,MAAM,UAAU,gBAAgB,KAAK,OAAO;YAC5C,MAAM,UAAU,gBAAgB,KAAK,OAAO;YAE5C,YAAY;YACZ,MAAM,qBAAqB,gBAAgB,KAAK,EAAE,IAAI,KAAK,mBAAmB;YAC9E,MAAM,mBAAmB,gBAAgB,KAAK,CAAC,IAAI,KAAK,iBAAiB;YACzE,MAAM,iBAAiB,gBAAgB,KAAK,eAAe;YAC3D,MAAM,eAAe,gBAAgB,KAAK,aAAa;YAEvD,YAAY;YACZ,MAAM,SAAS,gBAAgB,KAAK,SAAS;YAC7C,MAAM,gBAAgB,KAAK,SAAS,KAAK,iBAAiB,iBACrC,KAAK,SAAS,KAAK,OAAO,KAC1B,gBAAgB,KAAK,SAAS;YAEnD,4BAA4B;YAC5B,MAAM,aAAa;gBACjB;gBAAQ;gBAAY;gBAAY;gBAAY;gBAAW;gBAAY;gBAAS;gBAC5E;gBAAoB;gBAAkB;gBAAgB;gBACtD;gBAAQ;aACT;YAED,kCAAkC;YAClC,MAAM,gBAAgB;gBACpB;oBAAE,OAAO,KAAK,aAAa;oBAAE,MAAM;gBAAgB;gBACnD;oBAAE,OAAO,KAAK,eAAe,IAAI,KAAK,YAAY;oBAAE,MAAM;gBAAkB;gBAC5E;oBAAE,OAAO,WAAW;oBAAa,MAAM;gBAAa;aACrD;YAED,iDAAiD;YACjD,MAAM,aAAa,eAAe,KAAK,CAAC;YACxC,IAAI,YAAY;gBACd,MAAM,WAAW,UAAU,CAAC,EAAE;gBAC9B,MAAM,QAAQ,WAAW,UAAU,CAAC,EAAE;gBAEtC,OAAO,cAAc,IAAI;oEAAC,CAAA;wBACxB,IAAI,MAAM,KAAK,IAAI,QAAQ,MAAM,MAAM,KAAK,GAAG,OAAO;wBAEtD,OAAQ;4BACN,KAAK;gCAAK,OAAO,MAAM,KAAK,GAAG;4BAC/B,KAAK;gCAAM,OAAO,MAAM,KAAK,IAAI;4BACjC,KAAK;gCAAK,OAAO,MAAM,KAAK,GAAG;4BAC/B,KAAK;gCAAM,OAAO,MAAM,KAAK,IAAI;4BACjC,KAAK;gCAAK,OAAO,MAAM,KAAK,KAAK;4BACjC;gCAAS,OAAO;wBAClB;oBACF;;YACF;YAEA,gCAAgC;YAChC,MAAM,cAAc,WAAW;YAC/B,IAAI,CAAC,MAAM,cAAc;gBACvB,MAAM,eAAe,cAAc,IAAI;iFAAC,CAAA,QACtC,MAAM,KAAK,IAAI,QAAQ,CAAC,MAAM,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK;;gBAEhE,IAAI,cAAc,OAAO;YAC3B;YAEA,cAAc;YACd,IAAI,YAAY;gBACd,OAAO,WAAW,IAAI;oEAAC,CAAA,QAAS,UAAU;;YAC5C,OAAO;gBACL,OAAO,WAAW,IAAI;oEAAC,CAAA,QAAS,MAAM,QAAQ,CAAC;;YACjD;QACF;uDAAG,EAAE;IAEL,eAAe;IACf,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE;YAC9B,IAAI,CAAC,WAAW,IAAI,IAAI;gBACtB,uBAAuB;gBACvB;YACF;YAEA,8BAA8B;YAC9B,MAAM,cAAc,WAAW,KAAK,CAAC,KAClC,GAAG;wEAAC,CAAA,OAAQ,KAAK,IAAI;uEACrB,MAAM;wEAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;;YAEhC,IAAI,WAAmB,EAAE;YAEzB,IAAI,eAAe,UAAU;gBAC3B,IAAI,YAAY,MAAM,KAAK,GAAG;oBAC5B,4BAA4B;oBAC5B,WAAW,KAAK,MAAM;oEAAC,CAAA,OAAQ,gBAAgB,MAAM,WAAW,CAAC,EAAE,EAAE;;gBACvE,OAAO;oBACL,uCAAuC;oBACvC,WAAW,KAAK,MAAM;oEAAC,CAAA,OACrB,YAAY,KAAK;4EAAC,CAAA,OAAQ,gBAAgB,MAAM,MAAM;;;gBAE1D;YACF,OAAO;gBACL,qDAAqD;gBACrD,WAAW,KAAK,MAAM;gEAAC,CAAA,OACrB,YAAY,IAAI;wEAAC,CAAA,OAAQ,gBAAgB,MAAM,MAAM;;;YAEzD;YAEA,uBAAuB;QACzB;mDAAG;QAAC;QAAY;QAAY;QAAM;KAAgB,EAAE,mDAAmD;;IAEvG,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG;QAAC;QAAY;QAAY;QAAM;KAAgB,EAAE,qDAAqD;;IAEzG,MAAM,yBAAyB,CAAC;QAC9B,cAAc;IAChB;IAEA,MAAM,cAAc;QAClB,cAAc;QACd,cAAc;IAChB;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;;8BACrB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,uBAAuB,EAAE,MAAM,CAAC,KAAK;oCACtD,UAAU;oCACV,WAAU;oCACV,cAAW;;;;;;gCAEZ,4BACC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;8CAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAMnB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAY,eAAe,CAAC,QAAiC,cAAc;;kDACxF,6LAAC,qIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;kDAEd,6LAAC,qIAAA,CAAA,gBAAa;;0DACZ,6LAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAW;;;;;;0DAC7B,6LAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAS;;;;;;;;;;;;;;;;;;;;;;;wBAQhC,4BACC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,UAAU;4BACV,WAAU;4BACV,cAAW;;8CAEX,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;wBAMjC,qBAAqB,aAAa,mBACjC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS,mBAAmB,YAAY;4BACxC,MAAK;4BACL,SAAS;4BACT,WAAU;4BACV,cAAY,mBAAmB,kCAAkC;;gCAEhE,iCAAmB,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;yDAAe,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAC3E,mBAAmB,yBAAyB;;;;;;;wBAKhD,oBAAoB,gBAAgB,mBACnC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACP,8DAA8D;4BAC9D,sCAAsC;4BACxC;4BACA,WAAU;4BACV,cAAY,CAAC,oBAAoB,EAAE,cAAc,iBAAiB,CAAC;;8CAEnE,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAAY;gCACf;gCAAc;;;;;;;;;;;;;gBAMvC,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;0CACN,6LAAC;0CAAK;;;;;;0CACN,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB;GApQwB;KAAA", "debugId": null}}, {"offset": {"line": 2979, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/common/TruncatedText.tsx"], "sourcesContent": ["import React, { useState } from 'react'\n\ninterface TruncatedTextProps {\n  text: string\n  maxLength?: number\n  className?: string\n}\n\nexport default function TruncatedText({\n  text,\n  maxLength = 20,\n  className = \"\"\n}: TruncatedTextProps) {\n  const [showTooltip, setShowTooltip] = useState(false)\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })\n\n  if (!text) return <span className=\"text-gray-400\">-</span>\n\n  const shouldTruncate = text.length > maxLength\n  const displayText = shouldTruncate ? `${text.substring(0, maxLength)}...` : text\n\n  if (!shouldTruncate) {\n    return <span className={className}>{text}</span>\n  }\n\n  const handleMouseEnter = (e: React.MouseEvent) => {\n    setMousePosition({ x: e.clientX, y: e.clientY })\n    setShowTooltip(true)\n  }\n\n  const handleMouseMove = (e: React.MouseEvent) => {\n    setMousePosition({ x: e.clientX, y: e.clientY })\n  }\n\n  return (\n    <div className=\"relative inline-block\">\n      <span\n        className={`cursor-help ${className}`}\n        style={{\n          textOverflow: 'ellipsis',\n          whiteSpace: 'nowrap',\n          overflow: 'hidden',\n          maxWidth: '100%',\n          display: 'inline-block'\n        }}\n        onMouseEnter={handleMouseEnter}\n        onMouseMove={handleMouseMove}\n        onMouseLeave={() => setShowTooltip(false)}\n        title={text} // Fallback browser tooltip\n      >\n        {displayText}\n      </span>\n\n      {/* Custom tooltip */}\n      {showTooltip && (\n        <div\n          className=\"fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none\"\n          style={{\n            top: mousePosition.y - 40,\n            left: mousePosition.x - 150,\n            maxWidth: '300px',\n            wordWrap: 'break-word',\n            whiteSpace: 'normal'\n          }}\n        >\n          {text}\n          {/* Arrow */}\n          <div\n            className=\"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0\"\n            style={{\n              borderLeft: '5px solid transparent',\n              borderRight: '5px solid transparent',\n              borderTop: '5px solid #1f2937'\n            }}\n          />\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;;AAQe,SAAS,cAAc,EACpC,IAAI,EACJ,YAAY,EAAE,EACd,YAAY,EAAE,EACK;;IACnB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAEhE,IAAI,CAAC,MAAM,qBAAO,6LAAC;QAAK,WAAU;kBAAgB;;;;;;IAElD,MAAM,iBAAiB,KAAK,MAAM,GAAG;IACrC,MAAM,cAAc,iBAAiB,GAAG,KAAK,SAAS,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG;IAE5E,IAAI,CAAC,gBAAgB;QACnB,qBAAO,6LAAC;YAAK,WAAW;sBAAY;;;;;;IACtC;IAEA,MAAM,mBAAmB,CAAC;QACxB,iBAAiB;YAAE,GAAG,EAAE,OAAO;YAAE,GAAG,EAAE,OAAO;QAAC;QAC9C,eAAe;IACjB;IAEA,MAAM,kBAAkB,CAAC;QACvB,iBAAiB;YAAE,GAAG,EAAE,OAAO;YAAE,GAAG,EAAE,OAAO;QAAC;IAChD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,WAAW,CAAC,YAAY,EAAE,WAAW;gBACrC,OAAO;oBACL,cAAc;oBACd,YAAY;oBACZ,UAAU;oBACV,UAAU;oBACV,SAAS;gBACX;gBACA,cAAc;gBACd,aAAa;gBACb,cAAc,IAAM,eAAe;gBACnC,OAAO;0BAEN;;;;;;YAIF,6BACC,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,KAAK,cAAc,CAAC,GAAG;oBACvB,MAAM,cAAc,CAAC,GAAG;oBACxB,UAAU;oBACV,UAAU;oBACV,YAAY;gBACd;;oBAEC;kCAED,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,YAAY;4BACZ,aAAa;4BACb,WAAW;wBACb;;;;;;;;;;;;;;;;;;AAMZ;GAvEwB;KAAA", "debugId": null}}, {"offset": {"line": 3098, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/tooltips/CableTooltips.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useRef, useEffect } from 'react'\nimport { createPortal } from 'react-dom'\n\ninterface TooltipProps {\n  content: string | React.ReactNode\n  children: React.ReactNode\n  position?: 'top' | 'bottom' | 'left' | 'right' | 'auto'\n  delay?: number\n  className?: string\n  disabled?: boolean\n  maxWidth?: number\n}\n\ninterface TooltipPosition {\n  top: number\n  left: number\n  position: 'top' | 'bottom' | 'left' | 'right'\n}\n\nexport const CableTooltip: React.FC<TooltipProps> = ({\n  content,\n  children,\n  position = 'auto',\n  delay = 500,\n  className = '',\n  disabled = false,\n  maxWidth = 250\n}) => {\n  const [isVisible, setIsVisible] = useState(false)\n  const [tooltipPosition, setTooltipPosition] = useState<TooltipPosition | null>(null)\n  const triggerRef = useRef<HTMLDivElement>(null)\n  const tooltipRef = useRef<HTMLDivElement>(null)\n  const timeoutRef = useRef<NodeJS.Timeout | null>(null)\n\n  const calculatePosition = (): TooltipPosition | null => {\n    if (!triggerRef.current) return null\n\n    const triggerRect = triggerRef.current.getBoundingClientRect()\n    const viewportWidth = window.innerWidth\n    const viewportHeight = window.innerHeight\n    const tooltipWidth = maxWidth\n    const tooltipHeight = 40 // Estimated height\n\n    let finalPosition = position\n    let top = 0\n    let left = 0\n\n    // Auto positioning logic\n    if (position === 'auto') {\n      const spaceTop = triggerRect.top\n      const spaceBottom = viewportHeight - triggerRect.bottom\n      const spaceLeft = triggerRect.left\n      const spaceRight = viewportWidth - triggerRect.right\n\n      if (spaceTop > tooltipHeight && spaceTop > spaceBottom) {\n        finalPosition = 'top'\n      } else if (spaceBottom > tooltipHeight) {\n        finalPosition = 'bottom'\n      } else if (spaceRight > tooltipWidth) {\n        finalPosition = 'right'\n      } else {\n        finalPosition = 'left'\n      }\n    }\n\n    // Calculate position based on final position\n    switch (finalPosition) {\n      case 'top':\n        top = triggerRect.top - tooltipHeight - 8\n        left = triggerRect.left + (triggerRect.width / 2) - (tooltipWidth / 2)\n        break\n      case 'bottom':\n        top = triggerRect.bottom + 8\n        left = triggerRect.left + (triggerRect.width / 2) - (tooltipWidth / 2)\n        break\n      case 'left':\n        top = triggerRect.top + (triggerRect.height / 2) - (tooltipHeight / 2)\n        left = triggerRect.left - tooltipWidth - 8\n        break\n      case 'right':\n        top = triggerRect.top + (triggerRect.height / 2) - (tooltipHeight / 2)\n        left = triggerRect.right + 8\n        break\n    }\n\n    // Ensure tooltip stays within viewport\n    left = Math.max(8, Math.min(left, viewportWidth - tooltipWidth - 8))\n    top = Math.max(8, Math.min(top, viewportHeight - tooltipHeight - 8))\n\n    return { top, left, position: finalPosition }\n  }\n\n  const showTooltip = () => {\n    if (disabled) return\n\n    if (timeoutRef.current) {\n      clearTimeout(timeoutRef.current)\n    }\n\n    timeoutRef.current = setTimeout(() => {\n      const position = calculatePosition()\n      if (position) {\n        setTooltipPosition(position)\n        setIsVisible(true)\n      }\n    }, delay)\n  }\n\n  const hideTooltip = () => {\n    if (timeoutRef.current) {\n      clearTimeout(timeoutRef.current)\n      timeoutRef.current = null\n    }\n    setIsVisible(false)\n    setTooltipPosition(null)\n  }\n\n  useEffect(() => {\n    return () => {\n      if (timeoutRef.current) {\n        clearTimeout(timeoutRef.current)\n      }\n    }\n  }, [])\n\n  const getArrowClasses = (pos: string) => {\n    const baseClasses = \"absolute w-0 h-0 border-solid\"\n    switch (pos) {\n      case 'top':\n        return `${baseClasses} top-full left-1/2 transform -translate-x-1/2 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-gray-900`\n      case 'bottom':\n        return `${baseClasses} bottom-full left-1/2 transform -translate-x-1/2 border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-gray-900`\n      case 'left':\n        return `${baseClasses} left-full top-1/2 transform -translate-y-1/2 border-t-4 border-b-4 border-l-4 border-t-transparent border-b-transparent border-l-gray-900`\n      case 'right':\n        return `${baseClasses} right-full top-1/2 transform -translate-y-1/2 border-t-4 border-b-4 border-r-4 border-t-transparent border-b-transparent border-r-gray-900`\n      default:\n        return baseClasses\n    }\n  }\n\n  const tooltipElement = isVisible && tooltipPosition ? (\n    <div\n      ref={tooltipRef}\n      className={`fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none transition-opacity duration-200 ${className}`}\n      style={{\n        top: tooltipPosition.top,\n        left: tooltipPosition.left,\n        maxWidth: maxWidth,\n        wordWrap: 'break-word',\n        whiteSpace: 'normal'\n      }}\n      role=\"tooltip\"\n      aria-hidden={!isVisible}\n    >\n      {content}\n      <div className={getArrowClasses(tooltipPosition.position)} />\n    </div>\n  ) : null\n\n  return (\n    <>\n      <div\n        ref={triggerRef}\n        onMouseEnter={showTooltip}\n        onMouseLeave={hideTooltip}\n        onFocus={showTooltip}\n        onBlur={hideTooltip}\n        className=\"inline-block\"\n        aria-describedby={isVisible ? 'tooltip' : undefined}\n      >\n        {children}\n      </div>\n      {typeof document !== 'undefined' && tooltipElement && createPortal(tooltipElement, document.body)}\n    </>\n  )\n}\n\n// Specialized tooltips for different cable actions\ninterface ActionTooltipProps {\n  action: 'connect' | 'disconnect' | 'certify' | 'generate_pdf' | 'install' | 'modify'\n  cableId?: string\n  disabled?: boolean\n  children: React.ReactNode\n}\n\nexport const ActionTooltip: React.FC<ActionTooltipProps> = ({\n  action,\n  cableId,\n  disabled = false,\n  children\n}) => {\n  const getTooltipContent = () => {\n    if (disabled) {\n      switch (action) {\n        case 'connect':\n          return 'Collegamento disponibile solo per cavi installati'\n        case 'disconnect':\n          return 'Nessun collegamento da scollegare'\n        case 'certify':\n          return 'Certificazione disponibile solo per cavi installati e collegati'\n        case 'generate_pdf':\n          return 'Generazione PDF disponibile solo per cavi certificati'\n        default:\n          return 'Azione non disponibile'\n      }\n    }\n\n    switch (action) {\n      case 'connect':\n        return `Collega il cavo ${cableId || ''} alle sue destinazioni`\n      case 'disconnect':\n        return `Scollega il cavo ${cableId || ''} da tutte le connessioni`\n      case 'certify':\n        return `Certifica il cavo ${cableId || ''} dopo i test di collaudo`\n      case 'generate_pdf':\n        return `Genera certificato PDF per il cavo ${cableId || ''}`\n      case 'install':\n        return `Inserisci metri installati per il cavo ${cableId || ''}`\n      case 'modify':\n        return `Modifica i dati della bobina per il cavo ${cableId || ''}`\n      default:\n        return 'Azione disponibile'\n    }\n  }\n\n  return (\n    <CableTooltip\n      content={getTooltipContent()}\n      disabled={false}\n      delay={300}\n      position=\"auto\"\n    >\n      {children}\n    </CableTooltip>\n  )\n}\n\n// KPI Tooltip for statistics\ninterface KpiTooltipProps {\n  type: 'total' | 'installed' | 'in_progress' | 'to_install' | 'connected' | 'certified'\n  count: number\n  percentage?: number\n  children: React.ReactNode\n}\n\nexport const KpiTooltip: React.FC<KpiTooltipProps> = ({\n  type,\n  count,\n  percentage,\n  children\n}) => {\n  const getTooltipContent = () => {\n    const baseText = `${count} cavi`\n    const percentageText = percentage !== undefined ? ` (${percentage.toFixed(1)}%)` : ''\n    \n    switch (type) {\n      case 'total':\n        return `Totale cavi nel progetto: ${baseText}`\n      case 'installed':\n        return `Cavi fisicamente installati: ${baseText}${percentageText}`\n      case 'in_progress':\n        return `Cavi in corso di installazione: ${baseText}${percentageText}`\n      case 'to_install':\n        return `Cavi ancora da installare: ${baseText}${percentageText}`\n      case 'connected':\n        return `Cavi completamente collegati: ${baseText}${percentageText}`\n      case 'certified':\n        return `Cavi certificati e collaudati: ${baseText}${percentageText}`\n      default:\n        return baseText\n    }\n  }\n\n  return (\n    <CableTooltip\n      content={getTooltipContent()}\n      delay={200}\n      position=\"bottom\"\n    >\n      {children}\n    </CableTooltip>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;;;AAHA;;;AAqBO,MAAM,eAAuC,CAAC,EACnD,OAAO,EACP,QAAQ,EACR,WAAW,MAAM,EACjB,QAAQ,GAAG,EACX,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,WAAW,GAAG,EACf;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IAC/E,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAEjD,MAAM,oBAAoB;QACxB,IAAI,CAAC,WAAW,OAAO,EAAE,OAAO;QAEhC,MAAM,cAAc,WAAW,OAAO,CAAC,qBAAqB;QAC5D,MAAM,gBAAgB,OAAO,UAAU;QACvC,MAAM,iBAAiB,OAAO,WAAW;QACzC,MAAM,eAAe;QACrB,MAAM,gBAAgB,GAAG,mBAAmB;;QAE5C,IAAI,gBAAgB;QACpB,IAAI,MAAM;QACV,IAAI,OAAO;QAEX,yBAAyB;QACzB,IAAI,aAAa,QAAQ;YACvB,MAAM,WAAW,YAAY,GAAG;YAChC,MAAM,cAAc,iBAAiB,YAAY,MAAM;YACvD,MAAM,YAAY,YAAY,IAAI;YAClC,MAAM,aAAa,gBAAgB,YAAY,KAAK;YAEpD,IAAI,WAAW,iBAAiB,WAAW,aAAa;gBACtD,gBAAgB;YAClB,OAAO,IAAI,cAAc,eAAe;gBACtC,gBAAgB;YAClB,OAAO,IAAI,aAAa,cAAc;gBACpC,gBAAgB;YAClB,OAAO;gBACL,gBAAgB;YAClB;QACF;QAEA,6CAA6C;QAC7C,OAAQ;YACN,KAAK;gBACH,MAAM,YAAY,GAAG,GAAG,gBAAgB;gBACxC,OAAO,YAAY,IAAI,GAAI,YAAY,KAAK,GAAG,IAAM,eAAe;gBACpE;YACF,KAAK;gBACH,MAAM,YAAY,MAAM,GAAG;gBAC3B,OAAO,YAAY,IAAI,GAAI,YAAY,KAAK,GAAG,IAAM,eAAe;gBACpE;YACF,KAAK;gBACH,MAAM,YAAY,GAAG,GAAI,YAAY,MAAM,GAAG,IAAM,gBAAgB;gBACpE,OAAO,YAAY,IAAI,GAAG,eAAe;gBACzC;YACF,KAAK;gBACH,MAAM,YAAY,GAAG,GAAI,YAAY,MAAM,GAAG,IAAM,gBAAgB;gBACpE,OAAO,YAAY,KAAK,GAAG;gBAC3B;QACJ;QAEA,uCAAuC;QACvC,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,gBAAgB,eAAe;QACjE,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,iBAAiB,gBAAgB;QAEjE,OAAO;YAAE;YAAK;YAAM,UAAU;QAAc;IAC9C;IAEA,MAAM,cAAc;QAClB,IAAI,UAAU;QAEd,IAAI,WAAW,OAAO,EAAE;YACtB,aAAa,WAAW,OAAO;QACjC;QAEA,WAAW,OAAO,GAAG,WAAW;YAC9B,MAAM,WAAW;YACjB,IAAI,UAAU;gBACZ,mBAAmB;gBACnB,aAAa;YACf;QACF,GAAG;IACL;IAEA,MAAM,cAAc;QAClB,IAAI,WAAW,OAAO,EAAE;YACtB,aAAa,WAAW,OAAO;YAC/B,WAAW,OAAO,GAAG;QACvB;QACA,aAAa;QACb,mBAAmB;IACrB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;0CAAO;oBACL,IAAI,WAAW,OAAO,EAAE;wBACtB,aAAa,WAAW,OAAO;oBACjC;gBACF;;QACF;iCAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,MAAM,cAAc;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO,GAAG,YAAY,0IAA0I,CAAC;YACnK,KAAK;gBACH,OAAO,GAAG,YAAY,6IAA6I,CAAC;YACtK,KAAK;gBACH,OAAO,GAAG,YAAY,0IAA0I,CAAC;YACnK,KAAK;gBACH,OAAO,GAAG,YAAY,2IAA2I,CAAC;YACpK;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,aAAa,gCAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAC,6HAA6H,EAAE,WAAW;QACtJ,OAAO;YACL,KAAK,gBAAgB,GAAG;YACxB,MAAM,gBAAgB,IAAI;YAC1B,UAAU;YACV,UAAU;YACV,YAAY;QACd;QACA,MAAK;QACL,eAAa,CAAC;;YAEb;0BACD,6LAAC;gBAAI,WAAW,gBAAgB,gBAAgB,QAAQ;;;;;;;;;;;eAExD;IAEJ,qBACE;;0BACE,6LAAC;gBACC,KAAK;gBACL,cAAc;gBACd,cAAc;gBACd,SAAS;gBACT,QAAQ;gBACR,WAAU;gBACV,oBAAkB,YAAY,YAAY;0BAEzC;;;;;;YAEF,OAAO,aAAa,eAAe,gCAAkB,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB,SAAS,IAAI;;;AAGtG;GA7Ja;KAAA;AAuKN,MAAM,gBAA8C,CAAC,EAC1D,MAAM,EACN,OAAO,EACP,WAAW,KAAK,EAChB,QAAQ,EACT;IACC,MAAM,oBAAoB;QACxB,IAAI,UAAU;YACZ,OAAQ;gBACN,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT;oBACE,OAAO;YACX;QACF;QAEA,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC,gBAAgB,EAAE,WAAW,GAAG,sBAAsB,CAAC;YACjE,KAAK;gBACH,OAAO,CAAC,iBAAiB,EAAE,WAAW,GAAG,wBAAwB,CAAC;YACpE,KAAK;gBACH,OAAO,CAAC,kBAAkB,EAAE,WAAW,GAAG,wBAAwB,CAAC;YACrE,KAAK;gBACH,OAAO,CAAC,mCAAmC,EAAE,WAAW,IAAI;YAC9D,KAAK;gBACH,OAAO,CAAC,uCAAuC,EAAE,WAAW,IAAI;YAClE,KAAK;gBACH,OAAO,CAAC,yCAAyC,EAAE,WAAW,IAAI;YACpE;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,UAAU;QACV,OAAO;QACP,UAAS;kBAER;;;;;;AAGP;MAlDa;AA4DN,MAAM,aAAwC,CAAC,EACpD,IAAI,EACJ,KAAK,EACL,UAAU,EACV,QAAQ,EACT;IACC,MAAM,oBAAoB;QACxB,MAAM,WAAW,GAAG,MAAM,KAAK,CAAC;QAChC,MAAM,iBAAiB,eAAe,YAAY,CAAC,EAAE,EAAE,WAAW,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG;QAEnF,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC,0BAA0B,EAAE,UAAU;YAChD,KAAK;gBACH,OAAO,CAAC,6BAA6B,EAAE,WAAW,gBAAgB;YACpE,KAAK;gBACH,OAAO,CAAC,gCAAgC,EAAE,WAAW,gBAAgB;YACvE,KAAK;gBACH,OAAO,CAAC,2BAA2B,EAAE,WAAW,gBAAgB;YAClE,KAAK;gBACH,OAAO,CAAC,8BAA8B,EAAE,WAAW,gBAAgB;YACrE,KAAK;gBACH,OAAO,CAAC,+BAA+B,EAAE,WAAW,gBAAgB;YACtE;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,OAAO;QACP,UAAS;kBAER;;;;;;AAGP;MArCa", "debugId": null}}, {"offset": {"line": 3361, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/CaviTable.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useMemo, useEffect } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { getCavoColorClasses } from '@/utils/softColors'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { TableRow, TableCell } from '@/components/ui/table'\nimport { Cavo } from '@/types'\nimport FilterableTable, { ColumnDef } from '@/components/common/FilterableTable'\nimport SmartCaviFilter from './SmartCaviFilter'\nimport TruncatedText from '@/components/common/TruncatedText'\nimport { ActionTooltip } from './tooltips/CableTooltips'\nimport {\n  MoreHorizontal,\n  Cable,\n  Settings,\n  Zap,\n  CheckCircle,\n  AlertCircle,\n  Clock,\n  Package,\n  Link,\n  Unlink,\n  Award,\n  Play,\n  Pause,\n  X,\n  Check,\n  FileText,\n  Download,\n  AlertTriangle,\n  Wrench\n} from 'lucide-react'\n\ninterface CaviTableProps {\n  cavi: Cavo[]\n  loading?: boolean\n  selectionEnabled?: boolean\n  selectedCavi?: string[]\n  onSelectionChange?: (selectedIds: string[]) => void\n  onStatusAction?: (cavo: Cavo, action: string) => void\n  onContextMenuAction?: (cavo: Cavo, action: string) => void\n}\n\nexport default function CaviTable({\n  cavi = [],\n  loading = false,\n  selectionEnabled = false,\n  selectedCavi = [],\n  onSelectionChange,\n  onStatusAction,\n  onContextMenuAction\n}: CaviTableProps) {\n  const [smartFilteredCavi, setSmartFilteredCavi] = useState(cavi)\n  const [filteredCavi, setFilteredCavi] = useState(cavi)\n  const [internalSelectionEnabled, setInternalSelectionEnabled] = useState(selectionEnabled)\n\n  // Aggiorna i cavi quando cambiano i cavi originali\n  useEffect(() => {\n    setSmartFilteredCavi(cavi)\n    setFilteredCavi(cavi)\n  }, [cavi])\n\n  // Gestione filtri intelligenti\n  const handleSmartFilterChange = (filtered: Cavo[]) => {\n    setSmartFilteredCavi(filtered)\n  }\n\n  // Gestione filtri tabella\n  const handleTableFilterChange = (filtered: Cavo[]) => {\n    setFilteredCavi(filtered)\n  }\n\n  const handleSelectionToggle = () => {\n    setInternalSelectionEnabled(!internalSelectionEnabled)\n  }\n\n  // Gestione selezione\n  const handleSelectAll = (checked: boolean) => {\n    if (onSelectionChange) {\n      onSelectionChange(checked ? filteredCavi.map(c => c.id_cavo) : [])\n    }\n  }\n\n  const handleSelectCavo = (cavoId: string, checked: boolean) => {\n    if (onSelectionChange) {\n      const newSelection = checked\n        ? [...selectedCavi, cavoId]\n        : selectedCavi.filter(id => id !== cavoId)\n      onSelectionChange(newSelection)\n    }\n  }\n\n  // Bulk action handlers\n  const handleBulkExport = async () => {\n    try {\n      const response = await fetch('/api/cavi/export', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          selectedIds: selectedCavi,\n          cantiereId: 1 // TODO: Get from context\n        })\n      })\n\n      if (response.ok) {\n        const blob = await response.blob()\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = `cavi_export_${new Date().toISOString().split('T')[0]}.csv`\n        document.body.appendChild(a)\n        a.click()\n        window.URL.revokeObjectURL(url)\n        document.body.removeChild(a)\n      } else {\n        const error = await response.json()\n        alert(`Errore durante l'esportazione: ${error.error}`)\n      }\n    } catch (error) {\n      alert('Errore durante l\\'esportazione')\n    }\n  }\n\n  const handleBulkStatusChange = async () => {\n    const newStatus = prompt('Inserisci il nuovo stato (Da installare, In corso, Installato):')\n    if (!newStatus) return\n\n    try {\n      const response = await fetch('/api/cavi/bulk-status', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          selectedIds: selectedCavi,\n          cantiereId: 1, // TODO: Get from context\n          newStatus\n        })\n      })\n\n      const result = await response.json()\n      if (result.success) {\n        alert(result.message)\n        // TODO: Refresh data\n      } else {\n        alert(`Errore: ${result.error}`)\n      }\n    } catch (error) {\n      alert('Errore durante il cambio stato')\n    }\n  }\n\n  const handleBulkAssignCommand = () => {\n    // TODO: Implementare modal per selezione comanda\n    alert(`Assegnazione comanda per ${selectedCavi.length} cavi`)\n  }\n\n  const handleBulkDelete = async () => {\n    if (!confirm(`Sei sicuro di voler eliminare ${selectedCavi.length} cavi?`)) {\n      return\n    }\n\n    try {\n      const response = await fetch('/api/cavi/bulk-delete', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          selectedIds: selectedCavi,\n          cantiereId: 1 // TODO: Get from context\n        })\n      })\n\n      const result = await response.json()\n      if (result.success) {\n        alert(result.message)\n        // TODO: Refresh data\n      } else {\n        alert(`Errore: ${result.error}`)\n      }\n    } catch (error) {\n      alert('Errore durante l\\'eliminazione')\n    }\n  }\n\n  // Define columns matching original webapp structure\n  const columns: ColumnDef[] = useMemo(() => {\n    const baseColumns: ColumnDef[] = [\n      {\n        field: 'id_cavo',\n        headerName: 'ID',\n        dataType: 'text',\n        width: 70,\n        align: 'left',\n        renderCell: (row: Cavo) => (\n          <span className=\"font-semibold text-mariner-900\">{row.id_cavo}</span>\n        )\n      },\n      {\n        field: 'sistema',\n        headerName: 'Sistema',\n        dataType: 'text',\n        width: 80,\n        renderCell: (row: Cavo) => (\n          <TruncatedText text={row.sistema || ''} maxLength={8} />\n        )\n      },\n      {\n        field: 'utility',\n        headerName: 'Utility',\n        dataType: 'text',\n        width: 80,\n        renderCell: (row: Cavo) => (\n          <TruncatedText text={row.utility || ''} maxLength={8} />\n        )\n      },\n      {\n        field: 'tipologia',\n        headerName: 'Tipologia',\n        dataType: 'text',\n        width: 100,\n        renderCell: (row: Cavo) => (\n          <TruncatedText text={row.tipologia || ''} maxLength={12} />\n        )\n      },\n      {\n        field: 'formazione',\n        headerName: 'Form.',\n        dataType: 'text',\n        align: 'left',\n        width: 60,\n        renderCell: (row: Cavo) => row.formazione || row.sezione\n      },\n      {\n        field: 'metri_teorici',\n        headerName: 'M.Teor.',\n        dataType: 'number',\n        align: 'left',\n        width: 70,\n        renderCell: (row: Cavo) => row.metri_teorici ? row.metri_teorici.toFixed(1) : '0'\n      },\n      {\n        field: 'metri_posati',\n        headerName: 'M.Reali',\n        dataType: 'number',\n        align: 'left',\n        width: 70,\n        renderCell: (row: Cavo) => {\n          const metri = row.metri_posati || row.metratura_reale || 0\n          return metri ? metri.toFixed(1) : '0'\n        }\n      },\n      {\n        field: 'ubicazione_partenza',\n        headerName: 'Da',\n        dataType: 'text',\n        width: 140,\n        renderCell: (row: Cavo) => (\n          <TruncatedText\n            text={row.da || row.ubicazione_partenza || ''}\n            maxLength={18}\n          />\n        )\n      },\n      {\n        field: 'ubicazione_arrivo',\n        headerName: 'A',\n        dataType: 'text',\n        width: 140,\n        renderCell: (row: Cavo) => (\n          <TruncatedText\n            text={row.a || row.ubicazione_arrivo || ''}\n            maxLength={18}\n          />\n        )\n      },\n      {\n        field: 'id_bobina',\n        headerName: 'Bobina',\n        dataType: 'text',\n        width: 80,\n        align: 'center',\n        renderCell: (row: Cavo) => {\n          const idBobina = row.id_bobina\n\n          // Debug: log del formato bobina per capire il pattern\n          if (idBobina && idBobina !== 'BOBINA_VUOTA' && idBobina !== 'N/A') {\n          }\n\n          if (!idBobina || idBobina === 'N/A') {\n            return <span className=\"text-gray-400\">-</span>\n          }\n\n          if (idBobina === 'BOBINA_VUOTA') {\n            return (\n              <Badge\n                variant=\"outline\"\n                className=\"text-xs px-2 py-0.5 text-orange-600 border-orange-300 bg-orange-50\"\n              >\n                Vuota\n              </Badge>\n            )\n          }\n\n          // Estrai solo Y da diversi formati possibili (come nella webapp originale):\n          // Pattern dalla webapp originale: /_B(.+)$/\n          let match = idBobina.match(/_B(.+)$/)\n          if (match) {\n            return <span className=\"font-medium\">{match[1]}</span>\n          }\n\n          // Pattern alternativo: _b (minuscolo)\n          match = idBobina.match(/_b(.+)$/)\n          if (match) {\n            return <span className=\"font-medium\">{match[1]}</span>\n          }\n\n          // Pattern per cX_bY o cX_BY\n          match = idBobina.match(/c\\d+_[bB](\\d+)$/)\n          if (match) {\n            return <span className=\"font-medium\">{match[1]}</span>\n          }\n\n          // Pattern generale per numeri alla fine\n          match = idBobina.match(/(\\d+)$/)\n          if (match) {\n            return <span className=\"font-medium\">{match[1]}</span>\n          }\n\n          // Se non corrisponde a nessun pattern, mostra l'ID completo\n          return <span className=\"font-medium text-xs\">{idBobina}</span>\n        }\n      },\n      {\n        field: 'stato_installazione',\n        headerName: 'Stato',\n        dataType: 'text',\n        align: 'left',\n        width: 120,\n        disableFilter: true,\n        disableSort: true,\n        renderCell: (row: Cavo) => getStatusButton(row)\n      },\n      {\n        field: 'collegamenti',\n        headerName: 'Collegamenti',\n        dataType: 'text',\n        align: 'left',\n        width: 180,\n        disableFilter: true,\n        disableSort: true,\n        renderCell: (row: Cavo) => getConnectionButton(row)\n      },\n      {\n        field: 'certificato',\n        headerName: 'Certificato',\n        dataType: 'text',\n        align: 'left',\n        width: 130,\n        disableFilter: true,\n        disableSort: true,\n        renderCell: (row: Cavo) => getCertificationButton(row)\n      },\n\n    ]\n\n    // Add selection column if enabled\n    if (internalSelectionEnabled) {\n      baseColumns.unshift({\n        field: 'selection',\n        headerName: '',\n        disableFilter: true,\n        disableSort: true,\n        width: 50,\n        align: 'left',\n        renderHeader: () => (\n          <Checkbox\n            checked={selectedCavi.length === filteredCavi.length && filteredCavi.length > 0}\n            onCheckedChange={handleSelectAll}\n          />\n        ),\n        renderCell: (row: Cavo) => (\n          <Checkbox\n            checked={selectedCavi.includes(row.id_cavo)}\n            onCheckedChange={(checked) => handleSelectCavo(row.id_cavo, checked as boolean)}\n            onClick={(e) => e.stopPropagation()}\n          />\n        )\n      })\n    }\n\n    return baseColumns\n  }, [internalSelectionEnabled, selectedCavi, filteredCavi, handleSelectAll, handleSelectCavo])\n\n  // Custom row renderer for selection and context menu\n  const renderRow = (row: Cavo, index: number) => {\n    const isSelected = selectedCavi.includes(row.id_cavo)\n\n    return (\n      <TableRow\n        key={row.id_cavo}\n        className={`\n          ${isSelected ? 'bg-blue-50 border-blue-200' : 'bg-white'}\n          hover:bg-blue-50 hover:border-blue-200 hover:shadow-sm\n          cursor-pointer border-b border-gray-200\n          transition-all duration-200 ease-in-out\n          ${isSelected ? 'ring-1 ring-blue-300' : ''}\n        `}\n        onClick={() => internalSelectionEnabled && handleSelectCavo(row.id_cavo, !isSelected)}\n        onContextMenu={(e) => {\n          e.preventDefault()\n          onContextMenuAction?.(row, 'context_menu')\n        }}\n      >\n        {columns.map((column) => (\n          <TableCell\n            key={column.field}\n            className={`\n              py-2 px-2 text-sm text-left\n              ${isSelected ? 'text-blue-900' : 'text-gray-900'}\n              transition-colors duration-200\n            `}\n            style={{ width: column.width, ...column.cellStyle }}\n            onClick={(e) => {\n              // Prevent row click for action columns\n              if (['stato_installazione', 'collegamenti', 'certificato'].includes(column.field)) {\n                e.stopPropagation()\n              }\n            }}\n          >\n            {column.renderCell ? column.renderCell(row) : (row[column.field] || <span className=\"text-gray-400\">-</span>)}\n          </TableCell>\n        ))}\n      </TableRow>\n    )\n  }\n\n  // Funzioni di utilità per lo stato\n  const getStatusBadge = (cavo: Cavo) => {\n    // Verifica se il cavo è assegnato a una comanda\n    const comandaPosa = cavo.comanda_posa\n    const comandaPartenza = cavo.comanda_partenza\n    const comandaArrivo = cavo.comanda_arrivo\n    const comandaCertificazione = cavo.comanda_certificazione\n\n    // Trova la comanda attiva (priorità: posa > partenza > arrivo > certificazione)\n    const comandaAttiva = comandaPosa || comandaPartenza || comandaArrivo || comandaCertificazione\n\n    // Se c'è una comanda attiva e lo stato è \"In corso\", mostra il codice comanda\n    if (comandaAttiva && cavo.stato_installazione === 'In corso') {\n      const colorClasses = getCavoColorClasses('IN_CORSO')\n      return (\n        <Badge\n          className={`cursor-pointer ${colorClasses.badge} ${colorClasses.hover}`}\n          onClick={() => onStatusAction?.(cavo, 'view_command', comandaAttiva)}\n        >\n          {comandaAttiva}\n        </Badge>\n      )\n    }\n\n    // Logica normale per gli altri stati\n    const stato = cavo.stato_installazione || 'Da installare'\n    const colorClasses = getCavoColorClasses(stato)\n\n    return (\n      <Badge className={colorClasses.badge}>\n        {stato}\n      </Badge>\n    )\n  }\n\n  const getStatusButton = (cavo: Cavo) => {\n    // Verifica se il cavo è installato controllando metri_posati o metratura_reale\n    const metriInstallati = cavo.metri_posati || cavo.metratura_reale || 0\n    const isInstalled = metriInstallati > 0\n\n    // Verifica se il cavo è assegnato a una comanda attiva\n    const comandaPosa = cavo.comanda_posa\n    const comandaPartenza = cavo.comanda_partenza\n    const comandaArrivo = cavo.comanda_arrivo\n    const comandaCertificazione = cavo.comanda_certificazione\n    const comandaAttiva = comandaPosa || comandaPartenza || comandaArrivo || comandaCertificazione\n\n    // Se c'è una comanda attiva e lo stato è \"In corso\", mostra il codice comanda\n    if (comandaAttiva && cavo.stato_installazione === 'In corso') {\n      return (\n        <ActionTooltip action=\"install\" cableId={cavo.id_cavo}>\n          <Badge\n            className=\"bg-blue-600 text-white cursor-pointer hover:bg-blue-700 hover:scale-105 transition-all duration-200 hover:shadow-md px-3 py-1 font-semibold\"\n            onClick={(e) => {\n              e.stopPropagation()\n              onStatusAction?.(cavo, 'view_command', comandaAttiva)\n            }}\n          >\n            <Settings className=\"w-3 h-3 mr-1\" />\n            {comandaAttiva}\n          </Badge>\n        </ActionTooltip>\n      )\n    }\n\n    // Determina lo stato del cavo\n    const stato = cavo.stato_installazione || 'Da installare'\n\n    if (stato === 'Installato' || isInstalled) {\n      return (\n        <ActionTooltip action=\"modify\" cableId={cavo.id_cavo}>\n          <Badge\n            className=\"bg-green-100 text-green-800 cursor-pointer hover:bg-green-200 hover:text-green-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-green-300\"\n            onClick={(e) => {\n              e.stopPropagation()\n              onStatusAction?.(cavo, 'modify_reel')\n            }}\n          >\n            <CheckCircle className=\"w-3 h-3 mr-1\" />\n            Installato\n          </Badge>\n        </ActionTooltip>\n      )\n    } else if (stato === 'In corso') {\n      return (\n        <ActionTooltip action=\"install\" cableId={cavo.id_cavo}>\n          <Badge\n            className=\"bg-yellow-100 text-yellow-800 cursor-pointer hover:bg-yellow-200 hover:text-yellow-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-yellow-300\"\n            onClick={(e) => {\n              e.stopPropagation()\n              onStatusAction?.(cavo, 'insert_meters')\n            }}\n          >\n            <Play className=\"w-3 h-3 mr-1\" />\n            In corso\n          </Badge>\n        </ActionTooltip>\n      )\n    } else {\n      return (\n        <ActionTooltip action=\"install\" cableId={cavo.id_cavo}>\n          <Badge\n            variant=\"outline\"\n            className=\"text-gray-700 cursor-pointer hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium\"\n            onClick={(e) => {\n              e.stopPropagation()\n              onStatusAction?.(cavo, 'insert_meters')\n            }}\n          >\n            <Pause className=\"w-3 h-3 mr-1\" />\n            Da installare\n          </Badge>\n        </ActionTooltip>\n      )\n    }\n  }\n\n  const getConnectionButton = (cavo: Cavo) => {\n    const isInstalled = cavo.metri_posati > 0 || cavo.metratura_reale > 0\n    const collegamento = cavo.collegamento || cavo.collegamenti || 0\n\n    if (!isInstalled) {\n      return (\n        <ActionTooltip action=\"connect\" disabled={true}>\n          <Badge\n            variant=\"outline\"\n            className=\"text-gray-400 cursor-not-allowed bg-gray-50 border-gray-200 px-3 py-1\"\n          >\n            <X className=\"w-3 h-3 mr-1\" />\n            Non disponibile\n          </Badge>\n        </ActionTooltip>\n      )\n    }\n\n    let label, actionType, badgeClass, icon, tooltipAction\n\n    switch (collegamento) {\n      case 0:\n        label = \"Collega\"\n        actionType = \"connect_cable\"\n        badgeClass = \"bg-gray-100 text-gray-700 cursor-pointer hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-gray-300\"\n        icon = <Link className=\"w-3 h-3 mr-1\" />\n        tooltipAction = \"connect\"\n        break\n      case 1:\n        label = \"Completa Arrivo\"\n        actionType = \"connect_arrival\"\n        badgeClass = \"bg-yellow-100 text-yellow-800 cursor-pointer hover:bg-yellow-200 hover:text-yellow-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-yellow-300\"\n        icon = <Link className=\"w-3 h-3 mr-1\" />\n        tooltipAction = \"connect\"\n        break\n      case 2:\n        label = \"Completa Partenza\"\n        actionType = \"connect_departure\"\n        badgeClass = \"bg-yellow-100 text-yellow-800 cursor-pointer hover:bg-yellow-200 hover:text-yellow-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-yellow-300\"\n        icon = <Link className=\"w-3 h-3 mr-1\" />\n        tooltipAction = \"connect\"\n        break\n      case 3:\n        label = \"Scollega\"\n        actionType = \"disconnect_cable\"\n        badgeClass = \"bg-green-100 text-green-800 cursor-pointer hover:bg-green-200 hover:text-green-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-green-300\"\n        icon = <Unlink className=\"w-3 h-3 mr-1\" />\n        tooltipAction = \"disconnect\"\n        break\n      default:\n        label = \"Gestisci\"\n        actionType = \"manage_connections\"\n        badgeClass = \"bg-blue-100 text-blue-800 cursor-pointer hover:bg-blue-200 hover:text-blue-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-blue-300\"\n        icon = <Settings className=\"w-3 h-3 mr-1\" />\n        tooltipAction = \"connect\"\n        break\n    }\n\n    return (\n      <ActionTooltip action={tooltipAction} cableId={cavo.id_cavo}>\n        <Badge\n          className={badgeClass}\n          onClick={(e) => {\n            e.stopPropagation()\n            onStatusAction?.(cavo, actionType)\n          }}\n        >\n          {icon}\n          {label}\n        </Badge>\n      </ActionTooltip>\n    )\n  }\n\n  const getCertificationButton = (cavo: Cavo) => {\n    const isInstalled = cavo.metri_posati > 0 || cavo.metratura_reale > 0\n    const isCertified = cavo.certificato === true || cavo.certificato === 'SI' || cavo.certificato === 'CERTIFICATO'\n\n    if (!isInstalled) {\n      return (\n        <ActionTooltip action=\"certify\" disabled={true}>\n          <Badge\n            variant=\"outline\"\n            className=\"text-gray-400 cursor-not-allowed bg-gray-50 border-gray-200 px-3 py-1\"\n          >\n            <X className=\"w-3 h-3 mr-1\" />\n            Non disponibile\n          </Badge>\n        </ActionTooltip>\n      )\n    }\n\n    if (isCertified) {\n      return (\n        <ActionTooltip action=\"generate_pdf\" cableId={cavo.id_cavo}>\n          <Badge\n            className=\"bg-green-600 text-white cursor-pointer hover:bg-green-700 transition-all duration-200 hover:scale-105 px-3 py-1 font-semibold\"\n            onClick={(e) => {\n              e.stopPropagation()\n              onStatusAction?.(cavo, 'generate_pdf')\n            }}\n          >\n            <Download className=\"w-3 h-3 mr-1\" />\n            Genera PDF\n          </Badge>\n        </ActionTooltip>\n      )\n    }\n\n    return (\n      <ActionTooltip action=\"certify\" cableId={cavo.id_cavo}>\n        <Badge\n          variant=\"outline\"\n          className=\"text-purple-700 cursor-pointer hover:bg-purple-50 hover:text-purple-800 hover:border-purple-300 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border-purple-200\"\n          onClick={(e) => {\n            e.stopPropagation()\n            onStatusAction?.(cavo, 'create_certificate')\n          }}\n        >\n          <Award className=\"w-3 h-3 mr-1\" />\n          Certifica\n        </Badge>\n      </ActionTooltip>\n    )\n  }\n\n  return (\n    <div className=\"relative\">\n      {/* Smart Filter */}\n      <SmartCaviFilter\n        cavi={cavi}\n        onFilteredDataChange={handleSmartFilterChange}\n        loading={loading}\n        selectionEnabled={internalSelectionEnabled}\n        onSelectionToggle={handleSelectionToggle}\n      />\n\n      {/* Filterable Table */}\n      <FilterableTable\n        data={smartFilteredCavi}\n        columns={columns}\n        loading={loading}\n        emptyMessage=\"Nessun cavo disponibile\"\n        onFilteredDataChange={handleTableFilterChange}\n        renderRow={renderRow}\n      />\n\n      {/* Contextual Action Bar - appears only when items are selected */}\n      {internalSelectionEnabled && selectedCavi.length > 0 && (\n        <div className=\"sticky bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-10\">\n          <div className=\"flex items-center justify-between p-3\">\n            <div className=\"flex items-center space-x-3\">\n              <Badge variant=\"secondary\" className=\"bg-mariner-100 text-mariner-800\">\n                {selectedCavi.length} cavi selezionati\n              </Badge>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => handleSelectAll(false)}\n                className=\"text-xs\"\n              >\n                Deseleziona tutto\n              </Button>\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => handleBulkExport()}\n                className=\"flex items-center space-x-1\"\n              >\n                <span>📊</span>\n                <span>Esporta</span>\n              </Button>\n\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => handleBulkStatusChange()}\n                className=\"flex items-center space-x-1\"\n              >\n                <span>🔄</span>\n                <span>Cambia Stato</span>\n              </Button>\n\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => handleBulkAssignCommand()}\n                className=\"flex items-center space-x-1\"\n              >\n                <span>📋</span>\n                <span>Assegna Comanda</span>\n              </Button>\n\n              <Button\n                variant=\"destructive\"\n                size=\"sm\"\n                onClick={() => handleBulkDelete()}\n                className=\"flex items-center space-x-1\"\n              >\n                <span>🗑️</span>\n                <span>Elimina</span>\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAdA;;;;;;;;;;;;AA8Ce,SAAS,UAAU,EAChC,OAAO,EAAE,EACT,UAAU,KAAK,EACf,mBAAmB,KAAK,EACxB,eAAe,EAAE,EACjB,iBAAiB,EACjB,cAAc,EACd,mBAAmB,EACJ;;IACf,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzE,mDAAmD;IACnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,qBAAqB;YACrB,gBAAgB;QAClB;8BAAG;QAAC;KAAK;IAET,+BAA+B;IAC/B,MAAM,0BAA0B,CAAC;QAC/B,qBAAqB;IACvB;IAEA,0BAA0B;IAC1B,MAAM,0BAA0B,CAAC;QAC/B,gBAAgB;IAClB;IAEA,MAAM,wBAAwB;QAC5B,4BAA4B,CAAC;IAC/B;IAEA,qBAAqB;IACrB,MAAM,kBAAkB,CAAC;QACvB,IAAI,mBAAmB;YACrB,kBAAkB,UAAU,aAAa,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,IAAI,EAAE;QACnE;IACF;IAEA,MAAM,mBAAmB,CAAC,QAAgB;QACxC,IAAI,mBAAmB;YACrB,MAAM,eAAe,UACjB;mBAAI;gBAAc;aAAO,GACzB,aAAa,MAAM,CAAC,CAAA,KAAM,OAAO;YACrC,kBAAkB;QACpB;IACF;IAEA,uBAAuB;IACvB,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,aAAa;oBACb,YAAY,EAAE,yBAAyB;gBACzC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;gBACvC,MAAM,IAAI,SAAS,aAAa,CAAC;gBACjC,EAAE,IAAI,GAAG;gBACT,EAAE,QAAQ,GAAG,CAAC,YAAY,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;gBACxE,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,EAAE,KAAK;gBACP,OAAO,GAAG,CAAC,eAAe,CAAC;gBAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,CAAC,+BAA+B,EAAE,MAAM,KAAK,EAAE;YACvD;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,yBAAyB;QAC7B,MAAM,YAAY,OAAO;QACzB,IAAI,CAAC,WAAW;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,aAAa;oBACb,YAAY;oBACZ;gBACF;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,OAAO,OAAO;YACpB,qBAAqB;YACvB,OAAO;gBACL,MAAM,CAAC,QAAQ,EAAE,OAAO,KAAK,EAAE;YACjC;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,0BAA0B;QAC9B,iDAAiD;QACjD,MAAM,CAAC,yBAAyB,EAAE,aAAa,MAAM,CAAC,KAAK,CAAC;IAC9D;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,QAAQ,CAAC,8BAA8B,EAAE,aAAa,MAAM,CAAC,MAAM,CAAC,GAAG;YAC1E;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,aAAa;oBACb,YAAY,EAAE,yBAAyB;gBACzC;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,OAAO,OAAO;YACpB,qBAAqB;YACvB,OAAO;gBACL,MAAM,CAAC,QAAQ,EAAE,OAAO,KAAK,EAAE;YACjC;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,oDAAoD;IACpD,MAAM,UAAuB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;sCAAE;YACnC,MAAM,cAA2B;gBAC/B;oBACE,OAAO;oBACP,YAAY;oBACZ,UAAU;oBACV,OAAO;oBACP,OAAO;oBACP,UAAU;sDAAE,CAAC,oBACX,6LAAC;gCAAK,WAAU;0CAAkC,IAAI,OAAO;;;;;;;gBAEjE;gBACA;oBACE,OAAO;oBACP,YAAY;oBACZ,UAAU;oBACV,OAAO;oBACP,UAAU;sDAAE,CAAC,oBACX,6LAAC,gJAAA,CAAA,UAAa;gCAAC,MAAM,IAAI,OAAO,IAAI;gCAAI,WAAW;;;;;;;gBAEvD;gBACA;oBACE,OAAO;oBACP,YAAY;oBACZ,UAAU;oBACV,OAAO;oBACP,UAAU;sDAAE,CAAC,oBACX,6LAAC,gJAAA,CAAA,UAAa;gCAAC,MAAM,IAAI,OAAO,IAAI;gCAAI,WAAW;;;;;;;gBAEvD;gBACA;oBACE,OAAO;oBACP,YAAY;oBACZ,UAAU;oBACV,OAAO;oBACP,UAAU;sDAAE,CAAC,oBACX,6LAAC,gJAAA,CAAA,UAAa;gCAAC,MAAM,IAAI,SAAS,IAAI;gCAAI,WAAW;;;;;;;gBAEzD;gBACA;oBACE,OAAO;oBACP,YAAY;oBACZ,UAAU;oBACV,OAAO;oBACP,OAAO;oBACP,UAAU;sDAAE,CAAC,MAAc,IAAI,UAAU,IAAI,IAAI,OAAO;;gBAC1D;gBACA;oBACE,OAAO;oBACP,YAAY;oBACZ,UAAU;oBACV,OAAO;oBACP,OAAO;oBACP,UAAU;sDAAE,CAAC,MAAc,IAAI,aAAa,GAAG,IAAI,aAAa,CAAC,OAAO,CAAC,KAAK;;gBAChF;gBACA;oBACE,OAAO;oBACP,YAAY;oBACZ,UAAU;oBACV,OAAO;oBACP,OAAO;oBACP,UAAU;sDAAE,CAAC;4BACX,MAAM,QAAQ,IAAI,YAAY,IAAI,IAAI,eAAe,IAAI;4BACzD,OAAO,QAAQ,MAAM,OAAO,CAAC,KAAK;wBACpC;;gBACF;gBACA;oBACE,OAAO;oBACP,YAAY;oBACZ,UAAU;oBACV,OAAO;oBACP,UAAU;sDAAE,CAAC,oBACX,6LAAC,gJAAA,CAAA,UAAa;gCACZ,MAAM,IAAI,EAAE,IAAI,IAAI,mBAAmB,IAAI;gCAC3C,WAAW;;;;;;;gBAGjB;gBACA;oBACE,OAAO;oBACP,YAAY;oBACZ,UAAU;oBACV,OAAO;oBACP,UAAU;sDAAE,CAAC,oBACX,6LAAC,gJAAA,CAAA,UAAa;gCACZ,MAAM,IAAI,CAAC,IAAI,IAAI,iBAAiB,IAAI;gCACxC,WAAW;;;;;;;gBAGjB;gBACA;oBACE,OAAO;oBACP,YAAY;oBACZ,UAAU;oBACV,OAAO;oBACP,OAAO;oBACP,UAAU;sDAAE,CAAC;4BACX,MAAM,WAAW,IAAI,SAAS;4BAE9B,sDAAsD;4BACtD,IAAI,YAAY,aAAa,kBAAkB,aAAa,OAAO,CACnE;4BAEA,IAAI,CAAC,YAAY,aAAa,OAAO;gCACnC,qBAAO,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;4BACzC;4BAEA,IAAI,aAAa,gBAAgB;gCAC/B,qBACE,6LAAC,oIAAA,CAAA,QAAK;oCACJ,SAAQ;oCACR,WAAU;8CACX;;;;;;4BAIL;4BAEA,4EAA4E;4BAC5E,4CAA4C;4BAC5C,IAAI,QAAQ,SAAS,KAAK,CAAC;4BAC3B,IAAI,OAAO;gCACT,qBAAO,6LAAC;oCAAK,WAAU;8CAAe,KAAK,CAAC,EAAE;;;;;;4BAChD;4BAEA,sCAAsC;4BACtC,QAAQ,SAAS,KAAK,CAAC;4BACvB,IAAI,OAAO;gCACT,qBAAO,6LAAC;oCAAK,WAAU;8CAAe,KAAK,CAAC,EAAE;;;;;;4BAChD;4BAEA,4BAA4B;4BAC5B,QAAQ,SAAS,KAAK,CAAC;4BACvB,IAAI,OAAO;gCACT,qBAAO,6LAAC;oCAAK,WAAU;8CAAe,KAAK,CAAC,EAAE;;;;;;4BAChD;4BAEA,wCAAwC;4BACxC,QAAQ,SAAS,KAAK,CAAC;4BACvB,IAAI,OAAO;gCACT,qBAAO,6LAAC;oCAAK,WAAU;8CAAe,KAAK,CAAC,EAAE;;;;;;4BAChD;4BAEA,4DAA4D;4BAC5D,qBAAO,6LAAC;gCAAK,WAAU;0CAAuB;;;;;;wBAChD;;gBACF;gBACA;oBACE,OAAO;oBACP,YAAY;oBACZ,UAAU;oBACV,OAAO;oBACP,OAAO;oBACP,eAAe;oBACf,aAAa;oBACb,UAAU;sDAAE,CAAC,MAAc,gBAAgB;;gBAC7C;gBACA;oBACE,OAAO;oBACP,YAAY;oBACZ,UAAU;oBACV,OAAO;oBACP,OAAO;oBACP,eAAe;oBACf,aAAa;oBACb,UAAU;sDAAE,CAAC,MAAc,oBAAoB;;gBACjD;gBACA;oBACE,OAAO;oBACP,YAAY;oBACZ,UAAU;oBACV,OAAO;oBACP,OAAO;oBACP,eAAe;oBACf,aAAa;oBACb,UAAU;sDAAE,CAAC,MAAc,uBAAuB;;gBACpD;aAED;YAED,kCAAkC;YAClC,IAAI,0BAA0B;gBAC5B,YAAY,OAAO,CAAC;oBAClB,OAAO;oBACP,YAAY;oBACZ,eAAe;oBACf,aAAa;oBACb,OAAO;oBACP,OAAO;oBACP,YAAY;sDAAE,kBACZ,6LAAC,uIAAA,CAAA,WAAQ;gCACP,SAAS,aAAa,MAAM,KAAK,aAAa,MAAM,IAAI,aAAa,MAAM,GAAG;gCAC9E,iBAAiB;;;;;;;oBAGrB,UAAU;sDAAE,CAAC,oBACX,6LAAC,uIAAA,CAAA,WAAQ;gCACP,SAAS,aAAa,QAAQ,CAAC,IAAI,OAAO;gCAC1C,eAAe;kEAAE,CAAC,UAAY,iBAAiB,IAAI,OAAO,EAAE;;gCAC5D,OAAO;kEAAE,CAAC,IAAM,EAAE,eAAe;;;;;;;;gBAGvC;YACF;YAEA,OAAO;QACT;qCAAG;QAAC;QAA0B;QAAc;QAAc;QAAiB;KAAiB;IAE5F,qDAAqD;IACrD,MAAM,YAAY,CAAC,KAAW;QAC5B,MAAM,aAAa,aAAa,QAAQ,CAAC,IAAI,OAAO;QAEpD,qBACE,6LAAC,oIAAA,CAAA,WAAQ;YAEP,WAAW,CAAC;UACV,EAAE,aAAa,+BAA+B,WAAW;;;;UAIzD,EAAE,aAAa,yBAAyB,GAAG;QAC7C,CAAC;YACD,SAAS,IAAM,4BAA4B,iBAAiB,IAAI,OAAO,EAAE,CAAC;YAC1E,eAAe,CAAC;gBACd,EAAE,cAAc;gBAChB,sBAAsB,KAAK;YAC7B;sBAEC,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,oIAAA,CAAA,YAAS;oBAER,WAAW,CAAC;;cAEV,EAAE,aAAa,kBAAkB,gBAAgB;;YAEnD,CAAC;oBACD,OAAO;wBAAE,OAAO,OAAO,KAAK;wBAAE,GAAG,OAAO,SAAS;oBAAC;oBAClD,SAAS,CAAC;wBACR,uCAAuC;wBACvC,IAAI;4BAAC;4BAAuB;4BAAgB;yBAAc,CAAC,QAAQ,CAAC,OAAO,KAAK,GAAG;4BACjF,EAAE,eAAe;wBACnB;oBACF;8BAEC,OAAO,UAAU,GAAG,OAAO,UAAU,CAAC,OAAQ,GAAG,CAAC,OAAO,KAAK,CAAC,kBAAI,6LAAC;wBAAK,WAAU;kCAAgB;;;;;;mBAd/F,OAAO,KAAK;;;;;WAhBhB,IAAI,OAAO;;;;;IAmCtB;IAEA,mCAAmC;IACnC,MAAM,iBAAiB,CAAC;QACtB,gDAAgD;QAChD,MAAM,cAAc,KAAK,YAAY;QACrC,MAAM,kBAAkB,KAAK,gBAAgB;QAC7C,MAAM,gBAAgB,KAAK,cAAc;QACzC,MAAM,wBAAwB,KAAK,sBAAsB;QAEzD,gFAAgF;QAChF,MAAM,gBAAgB,eAAe,mBAAmB,iBAAiB;QAEzE,8EAA8E;QAC9E,IAAI,iBAAiB,KAAK,mBAAmB,KAAK,YAAY;YAC5D,MAAM,eAAe,CAAA,GAAA,6HAAA,CAAA,sBAAmB,AAAD,EAAE;YACzC,qBACE,6LAAC,oIAAA,CAAA,QAAK;gBACJ,WAAW,CAAC,eAAe,EAAE,aAAa,KAAK,CAAC,CAAC,EAAE,aAAa,KAAK,EAAE;gBACvE,SAAS,IAAM,iBAAiB,MAAM,gBAAgB;0BAErD;;;;;;QAGP;QAEA,qCAAqC;QACrC,MAAM,QAAQ,KAAK,mBAAmB,IAAI;QAC1C,MAAM,eAAe,CAAA,GAAA,6HAAA,CAAA,sBAAmB,AAAD,EAAE;QAEzC,qBACE,6LAAC,oIAAA,CAAA,QAAK;YAAC,WAAW,aAAa,KAAK;sBACjC;;;;;;IAGP;IAEA,MAAM,kBAAkB,CAAC;QACvB,+EAA+E;QAC/E,MAAM,kBAAkB,KAAK,YAAY,IAAI,KAAK,eAAe,IAAI;QACrE,MAAM,cAAc,kBAAkB;QAEtC,uDAAuD;QACvD,MAAM,cAAc,KAAK,YAAY;QACrC,MAAM,kBAAkB,KAAK,gBAAgB;QAC7C,MAAM,gBAAgB,KAAK,cAAc;QACzC,MAAM,wBAAwB,KAAK,sBAAsB;QACzD,MAAM,gBAAgB,eAAe,mBAAmB,iBAAiB;QAEzE,8EAA8E;QAC9E,IAAI,iBAAiB,KAAK,mBAAmB,KAAK,YAAY;YAC5D,qBACE,6LAAC,0JAAA,CAAA,gBAAa;gBAAC,QAAO;gBAAU,SAAS,KAAK,OAAO;0BACnD,cAAA,6LAAC,oIAAA,CAAA,QAAK;oBACJ,WAAU;oBACV,SAAS,CAAC;wBACR,EAAE,eAAe;wBACjB,iBAAiB,MAAM,gBAAgB;oBACzC;;sCAEA,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBACnB;;;;;;;;;;;;QAIT;QAEA,8BAA8B;QAC9B,MAAM,QAAQ,KAAK,mBAAmB,IAAI;QAE1C,IAAI,UAAU,gBAAgB,aAAa;YACzC,qBACE,6LAAC,0JAAA,CAAA,gBAAa;gBAAC,QAAO;gBAAS,SAAS,KAAK,OAAO;0BAClD,cAAA,6LAAC,oIAAA,CAAA,QAAK;oBACJ,WAAU;oBACV,SAAS,CAAC;wBACR,EAAE,eAAe;wBACjB,iBAAiB,MAAM;oBACzB;;sCAEA,6LAAC,8NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;QAKhD,OAAO,IAAI,UAAU,YAAY;YAC/B,qBACE,6LAAC,0JAAA,CAAA,gBAAa;gBAAC,QAAO;gBAAU,SAAS,KAAK,OAAO;0BACnD,cAAA,6LAAC,oIAAA,CAAA,QAAK;oBACJ,WAAU;oBACV,SAAS,CAAC;wBACR,EAAE,eAAe;wBACjB,iBAAiB,MAAM;oBACzB;;sCAEA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;QAKzC,OAAO;YACL,qBACE,6LAAC,0JAAA,CAAA,gBAAa;gBAAC,QAAO;gBAAU,SAAS,KAAK,OAAO;0BACnD,cAAA,6LAAC,oIAAA,CAAA,QAAK;oBACJ,SAAQ;oBACR,WAAU;oBACV,SAAS,CAAC;wBACR,EAAE,eAAe;wBACjB,iBAAiB,MAAM;oBACzB;;sCAEA,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;QAK1C;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,cAAc,KAAK,YAAY,GAAG,KAAK,KAAK,eAAe,GAAG;QACpE,MAAM,eAAe,KAAK,YAAY,IAAI,KAAK,YAAY,IAAI;QAE/D,IAAI,CAAC,aAAa;YAChB,qBACE,6LAAC,0JAAA,CAAA,gBAAa;gBAAC,QAAO;gBAAU,UAAU;0BACxC,cAAA,6LAAC,oIAAA,CAAA,QAAK;oBACJ,SAAQ;oBACR,WAAU;;sCAEV,6LAAC,+LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;QAKtC;QAEA,IAAI,OAAO,YAAY,YAAY,MAAM;QAEzC,OAAQ;YACN,KAAK;gBACH,QAAQ;gBACR,aAAa;gBACb,aAAa;gBACb,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;gBACvB,gBAAgB;gBAChB;YACF,KAAK;gBACH,QAAQ;gBACR,aAAa;gBACb,aAAa;gBACb,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;gBACvB,gBAAgB;gBAChB;YACF,KAAK;gBACH,QAAQ;gBACR,aAAa;gBACb,aAAa;gBACb,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;gBACvB,gBAAgB;gBAChB;YACF,KAAK;gBACH,QAAQ;gBACR,aAAa;gBACb,aAAa;gBACb,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;gBACzB,gBAAgB;gBAChB;YACF;gBACE,QAAQ;gBACR,aAAa;gBACb,aAAa;gBACb,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;gBAC3B,gBAAgB;gBAChB;QACJ;QAEA,qBACE,6LAAC,0JAAA,CAAA,gBAAa;YAAC,QAAQ;YAAe,SAAS,KAAK,OAAO;sBACzD,cAAA,6LAAC,oIAAA,CAAA,QAAK;gBACJ,WAAW;gBACX,SAAS,CAAC;oBACR,EAAE,eAAe;oBACjB,iBAAiB,MAAM;gBACzB;;oBAEC;oBACA;;;;;;;;;;;;IAIT;IAEA,MAAM,yBAAyB,CAAC;QAC9B,MAAM,cAAc,KAAK,YAAY,GAAG,KAAK,KAAK,eAAe,GAAG;QACpE,MAAM,cAAc,KAAK,WAAW,KAAK,QAAQ,KAAK,WAAW,KAAK,QAAQ,KAAK,WAAW,KAAK;QAEnG,IAAI,CAAC,aAAa;YAChB,qBACE,6LAAC,0JAAA,CAAA,gBAAa;gBAAC,QAAO;gBAAU,UAAU;0BACxC,cAAA,6LAAC,oIAAA,CAAA,QAAK;oBACJ,SAAQ;oBACR,WAAU;;sCAEV,6LAAC,+LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;QAKtC;QAEA,IAAI,aAAa;YACf,qBACE,6LAAC,0JAAA,CAAA,gBAAa;gBAAC,QAAO;gBAAe,SAAS,KAAK,OAAO;0BACxD,cAAA,6LAAC,oIAAA,CAAA,QAAK;oBACJ,WAAU;oBACV,SAAS,CAAC;wBACR,EAAE,eAAe;wBACjB,iBAAiB,MAAM;oBACzB;;sCAEA,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;QAK7C;QAEA,qBACE,6LAAC,0JAAA,CAAA,gBAAa;YAAC,QAAO;YAAU,SAAS,KAAK,OAAO;sBACnD,cAAA,6LAAC,oIAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,WAAU;gBACV,SAAS,CAAC;oBACR,EAAE,eAAe;oBACjB,iBAAiB,MAAM;gBACzB;;kCAEA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;;;;;;IAK1C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,gJAAA,CAAA,UAAe;gBACd,MAAM;gBACN,sBAAsB;gBACtB,SAAS;gBACT,kBAAkB;gBAClB,mBAAmB;;;;;;0BAIrB,6LAAC,kJAAA,CAAA,UAAe;gBACd,MAAM;gBACN,SAAS;gBACT,SAAS;gBACT,cAAa;gBACb,sBAAsB;gBACtB,WAAW;;;;;;YAIZ,4BAA4B,aAAa,MAAM,GAAG,mBACjD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;wCAClC,aAAa,MAAM;wCAAC;;;;;;;8CAEvB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;;;;;;;sCAKH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM;oCACf,WAAU;;sDAEV,6LAAC;sDAAK;;;;;;sDACN,6LAAC;sDAAK;;;;;;;;;;;;8CAGR,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM;oCACf,WAAU;;sDAEV,6LAAC;sDAAK;;;;;;sDACN,6LAAC;sDAAK;;;;;;;;;;;;8CAGR,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM;oCACf,WAAU;;sDAEV,6LAAC;sDAAK;;;;;;sDACN,6LAAC;sDAAK;;;;;;;;;;;;8CAGR,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM;oCACf,WAAU;;sDAEV,6LAAC;sDAAK;;;;;;sDACN,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;GArtBwB;KAAA", "debugId": null}}, {"offset": {"line": 4483, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/CaviStatistics.tsx"], "sourcesContent": ["'use client'\n\nimport { useMemo, useState } from 'react'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport {\n  Cable,\n  CheckCircle,\n  Clock,\n  AlertTriangle,\n  Zap,\n  Package,\n  BarChart3,\n  Filter,\n  X\n} from 'lucide-react'\nimport { Cavo } from '@/types'\nimport { KpiTooltip } from './tooltips/CableTooltips'\n\ninterface CaviStatisticsProps {\n  cavi: Cavo[]\n  filteredCavi: Cavo[]\n  className?: string\n  revisioneCorrente?: string\n}\n\nexport default function CaviStatistics({\n  cavi,\n  filteredCavi,\n  className,\n  revisioneCorrente\n}: CaviStatisticsProps) {\n  const stats = useMemo(() => {\n    const totalCavi = cavi.length\n    const filteredCount = filteredCavi.length\n    \n    // Installation status\n    const installati = filteredCavi.filter(c => \n      c.stato_installazione === 'Installato' || \n      (c.metri_posati && c.metri_posati > 0) ||\n      (c.metratura_reale && c.metratura_reale > 0)\n    ).length\n    \n    const inCorso = filteredCavi.filter(c => \n      c.stato_installazione === 'In corso'\n    ).length\n    \n    const daInstallare = filteredCount - installati - inCorso\n    \n    // Connection status\n    const collegati = filteredCavi.filter(c => {\n      const collegamento = c.collegamento || c.collegamenti || 0\n      return collegamento === 3 // Both sides connected\n    }).length\n    \n    const parzialmenteCollegati = filteredCavi.filter(c => {\n      const collegamento = c.collegamento || c.collegamenti || 0\n      return collegamento === 1 || collegamento === 2 // One side connected\n    }).length\n    \n    const nonCollegati = filteredCavi.filter(c => {\n      const collegamento = c.collegamento || c.collegamenti || 0\n      return collegamento === 0 && (c.metri_posati > 0 || c.metratura_reale > 0)\n    }).length\n    \n    // Certification status\n    const certificati = filteredCavi.filter(c => \n      c.certificato === true || \n      c.certificato === 'SI' || \n      c.certificato === 'CERTIFICATO'\n    ).length\n    \n    // Meters calculation\n    const metriTotali = filteredCavi.reduce((sum, c) => sum + (c.metri_teorici || 0), 0)\n    const metriInstallati = filteredCavi.reduce((sum, c) => {\n      const metri = c.metri_posati || c.metratura_reale || 0\n      return sum + metri\n    }, 0)\n\n    // Calcolo IAP (Indice di Avanzamento Ponderato) come nella webapp originale\n    const calculateIAP = (nTot: number, nInst: number, nColl: number, nCert: number): number => {\n      // Pesi per le fasi del progetto\n      const Wp = 2.0  // Peso fase Posa\n      const Wc = 1.5  // Peso fase Collegamento\n      const Wz = 0.5  // Peso fase Certificazione\n\n      // Se non ci sono cavi, ritorna 0\n      if (nTot === 0) return 0\n\n      // Calcolo del numeratore (Sforzo Completato)\n      const sforzoSoloInstallati = (nInst - nColl) * Wp\n      const sforzoSoloCollegati = (nColl - nCert) * (Wp + Wc)\n      const sforzoCertificati = nCert * (Wp + Wc + Wz)\n      const numeratore = sforzoSoloInstallati + sforzoSoloCollegati + sforzoCertificati\n\n      // Calcolo del denominatore (Sforzo Massimo Previsto)\n      const denominatore = nTot * (Wp + Wc + Wz)\n\n      // Calcolo finale dell'IAP in percentuale\n      const iap = (numeratore / denominatore) * 100\n\n      return Math.round(iap * 100) / 100 // Arrotonda a 2 decimali\n    }\n\n    const percentualeInstallazione = calculateIAP(filteredCount, installati, collegati, certificati)\n    \n    return {\n      totalCavi,\n      filteredCount,\n      installati,\n      inCorso,\n      daInstallare,\n      collegati,\n      parzialmenteCollegati,\n      nonCollegati,\n      certificati,\n      metriTotali,\n      metriInstallati,\n      percentualeInstallazione\n    }\n  }, [cavi, filteredCavi])\n\n  // Removed click-to-filter functionality as per user request\n\n  return (\n    <Card className={className}>\n      <CardContent className=\"p-1.5\">\n        {/* Header with revision and filter indicator */}\n        <div className=\"flex items-center justify-between mb-1\">\n          <div className=\"flex items-center space-x-1.5\">\n            <BarChart3 className=\"h-3.5 w-3.5 text-mariner-600\" />\n            <span className=\"text-xs font-semibold text-mariner-900\">Statistiche Cavi</span>\n            {/* Filter indicator removed */}\n          </div>\n          <div className=\"flex items-center space-x-1\">\n            {/* Clear filter button removed */}\n            {revisioneCorrente && (\n              <Badge variant=\"outline\" className=\"text-xs font-medium py-0 px-1.5 h-5\">\n                Rev. {revisioneCorrente}\n              </Badge>\n            )}\n          </div>\n        </div>\n\n        {/* Statistics distributed across full width */}\n        <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2\">\n\n          {/* Total cables */}\n          <KpiTooltip type=\"total\" count={stats.totalCavi}>\n            <div className=\"flex items-center space-x-1.5 bg-mariner-50 px-1.5 py-1 rounded-lg\">\n              <Cable className=\"h-3.5 w-3.5 text-mariner-600\" />\n              <div>\n                <div className=\"font-bold text-mariner-900 text-sm\">{stats.filteredCount}</div>\n                <div className=\"text-xs text-mariner-600\">di {stats.totalCavi} cavi</div>\n              </div>\n            </div>\n          </KpiTooltip>\n\n          {/* Installation status */}\n          <KpiTooltip\n            type=\"installed\"\n            count={stats.installati}\n            percentage={(stats.installati / stats.filteredCount) * 100}\n          >\n            <div\n              className=\"flex items-center space-x-1.5 bg-green-50 px-1.5 py-1 rounded-lg\"\n              aria-label={`Cavi installati: ${stats.installati} cavi`}\n            >\n              <CheckCircle className=\"h-3.5 w-3.5 text-green-600\" />\n              <div>\n                <div className=\"font-bold text-green-700 text-sm\">{stats.installati}</div>\n                <div className=\"text-xs text-green-600\">installati</div>\n              </div>\n            </div>\n          </KpiTooltip>\n\n          <KpiTooltip\n            type=\"in_progress\"\n            count={stats.inCorso}\n            percentage={(stats.inCorso / stats.filteredCount) * 100}\n          >\n            <div\n              className=\"flex items-center space-x-1.5 bg-yellow-50 px-1.5 py-1 rounded-lg\"\n              aria-label={`Cavi in corso: ${stats.inCorso} cavi`}\n            >\n              <Clock className=\"h-3.5 w-3.5 text-yellow-600\" />\n              <div>\n                <div className=\"font-bold text-yellow-700 text-sm\">{stats.inCorso}</div>\n                <div className=\"text-xs text-yellow-600\">in corso</div>\n              </div>\n            </div>\n          </KpiTooltip>\n\n          <KpiTooltip\n            type=\"to_install\"\n            count={stats.daInstallare}\n            percentage={(stats.daInstallare / stats.filteredCount) * 100}\n          >\n            <div\n              className=\"flex items-center space-x-1.5 bg-gray-50 px-1.5 py-1 rounded-lg\"\n              aria-label={`Cavi da installare: ${stats.daInstallare} cavi`}\n            >\n              <AlertTriangle className=\"h-3.5 w-3.5 text-gray-600\" />\n              <div>\n                <div className=\"font-bold text-gray-700 text-sm\">{stats.daInstallare}</div>\n                <div className=\"text-xs text-gray-600\">da installare</div>\n              </div>\n            </div>\n          </KpiTooltip>\n\n          {/* Connection status */}\n          <KpiTooltip\n            type=\"connected\"\n            count={stats.collegati}\n            percentage={(stats.collegati / stats.filteredCount) * 100}\n          >\n            <div\n              className=\"flex items-center space-x-1.5 bg-blue-50 px-1.5 py-1 rounded-lg\"\n              aria-label={`Cavi collegati: ${stats.collegati} cavi`}\n            >\n              <Zap className=\"h-3.5 w-3.5 text-blue-600\" />\n              <div>\n                <div className=\"font-bold text-blue-700 text-sm\">{stats.collegati}</div>\n                <div className=\"text-xs text-blue-600\">collegati</div>\n              </div>\n            </div>\n          </KpiTooltip>\n\n          {/* Certification status */}\n          <KpiTooltip\n            type=\"certified\"\n            count={stats.certificati}\n            percentage={(stats.certificati / stats.filteredCount) * 100}\n          >\n            <div\n              className=\"flex items-center space-x-1.5 bg-purple-50 px-1.5 py-1 rounded-lg\"\n              aria-label={`Cavi certificati: ${stats.certificati} cavi`}\n            >\n              <Package className=\"h-3.5 w-3.5 text-purple-600\" />\n              <div>\n                <div className=\"font-bold text-purple-700 text-sm\">{stats.certificati}</div>\n                <div className=\"text-xs text-purple-600\">certificati</div>\n              </div>\n            </div>\n          </KpiTooltip>\n\n          {/* Meters progress */}\n          <KpiTooltip\n            type=\"total\"\n            count={stats.metriInstallati}\n          >\n            <div className=\"flex items-center space-x-1.5 bg-indigo-50 px-1.5 py-1 rounded-lg\">\n              <div className=\"h-3.5 w-3.5 flex items-center justify-center\">\n                <div className=\"h-2 w-2 bg-indigo-600 rounded-full\"></div>\n              </div>\n              <div>\n                <div className=\"font-bold text-indigo-700 text-sm\">{stats.metriInstallati.toLocaleString()}m</div>\n                <div className=\"text-xs text-indigo-600\">di {stats.metriTotali.toLocaleString()}m</div>\n              </div>\n            </div>\n          </KpiTooltip>\n\n        </div>\n\n        {/* IAP Progress bar - Colori morbidi */}\n        {stats.filteredCount > 0 && (\n          <div className=\"mt-2 bg-gray-50 p-2 rounded-lg\">\n            <div className=\"flex justify-between text-xs font-medium text-gray-700 mb-1\">\n              <span>IAP - Indice Avanzamento Ponderato</span>\n              <span className={`font-bold ${\n                stats.percentualeInstallazione >= 80 ? 'text-emerald-700' :\n                stats.percentualeInstallazione >= 50 ? 'text-yellow-700' :\n                stats.percentualeInstallazione >= 25 ? 'text-orange-700' : 'text-amber-700'\n              }`}>\n                {stats.percentualeInstallazione.toFixed(1)}%\n              </span>\n            </div>\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <div\n                className={`h-2 rounded-full transition-all duration-500 ease-in-out ${\n                  stats.percentualeInstallazione >= 80 ? 'bg-gradient-to-r from-emerald-500 to-emerald-600' :\n                  stats.percentualeInstallazione >= 50 ? 'bg-gradient-to-r from-yellow-500 to-yellow-600' :\n                  stats.percentualeInstallazione >= 25 ? 'bg-gradient-to-r from-orange-500 to-orange-600' :\n                  'bg-gradient-to-r from-amber-500 to-amber-600'\n                }`}\n                style={{ width: `${Math.min(stats.percentualeInstallazione, 100)}%` }}\n              />\n            </div>\n            <div className=\"flex justify-between text-xs text-gray-500 mt-0.5\">\n              <span>Pesi: Posa(2.0) + Collegamento(1.5) + Certificazione(0.5)</span>\n              <span>{stats.installati}I + {stats.collegati}C + {stats.certificati}Cert</span>\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;AAlBA;;;;;;AA2Be,SAAS,eAAe,EACrC,IAAI,EACJ,YAAY,EACZ,SAAS,EACT,iBAAiB,EACG;;IACpB,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;yCAAE;YACpB,MAAM,YAAY,KAAK,MAAM;YAC7B,MAAM,gBAAgB,aAAa,MAAM;YAEzC,sBAAsB;YACtB,MAAM,aAAa,aAAa,MAAM;iDAAC,CAAA,IACrC,EAAE,mBAAmB,KAAK,gBACzB,EAAE,YAAY,IAAI,EAAE,YAAY,GAAG,KACnC,EAAE,eAAe,IAAI,EAAE,eAAe,GAAG;gDAC1C,MAAM;YAER,MAAM,UAAU,aAAa,MAAM;iDAAC,CAAA,IAClC,EAAE,mBAAmB,KAAK;gDAC1B,MAAM;YAER,MAAM,eAAe,gBAAgB,aAAa;YAElD,oBAAoB;YACpB,MAAM,YAAY,aAAa,MAAM;iDAAC,CAAA;oBACpC,MAAM,eAAe,EAAE,YAAY,IAAI,EAAE,YAAY,IAAI;oBACzD,OAAO,iBAAiB,EAAE,uBAAuB;;gBACnD;gDAAG,MAAM;YAET,MAAM,wBAAwB,aAAa,MAAM;iDAAC,CAAA;oBAChD,MAAM,eAAe,EAAE,YAAY,IAAI,EAAE,YAAY,IAAI;oBACzD,OAAO,iBAAiB,KAAK,iBAAiB,EAAE,qBAAqB;;gBACvE;gDAAG,MAAM;YAET,MAAM,eAAe,aAAa,MAAM;iDAAC,CAAA;oBACvC,MAAM,eAAe,EAAE,YAAY,IAAI,EAAE,YAAY,IAAI;oBACzD,OAAO,iBAAiB,KAAK,CAAC,EAAE,YAAY,GAAG,KAAK,EAAE,eAAe,GAAG,CAAC;gBAC3E;gDAAG,MAAM;YAET,uBAAuB;YACvB,MAAM,cAAc,aAAa,MAAM;iDAAC,CAAA,IACtC,EAAE,WAAW,KAAK,QAClB,EAAE,WAAW,KAAK,QAClB,EAAE,WAAW,KAAK;gDAClB,MAAM;YAER,qBAAqB;YACrB,MAAM,cAAc,aAAa,MAAM;6DAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,aAAa,IAAI,CAAC;4DAAG;YAClF,MAAM,kBAAkB,aAAa,MAAM;iEAAC,CAAC,KAAK;oBAChD,MAAM,QAAQ,EAAE,YAAY,IAAI,EAAE,eAAe,IAAI;oBACrD,OAAO,MAAM;gBACf;gEAAG;YAEH,4EAA4E;YAC5E,MAAM;8DAAe,CAAC,MAAc,OAAe,OAAe;oBAChE,gCAAgC;oBAChC,MAAM,KAAK,IAAK,iBAAiB;;oBACjC,MAAM,KAAK,IAAK,yBAAyB;;oBACzC,MAAM,KAAK,IAAK,2BAA2B;;oBAE3C,iCAAiC;oBACjC,IAAI,SAAS,GAAG,OAAO;oBAEvB,6CAA6C;oBAC7C,MAAM,uBAAuB,CAAC,QAAQ,KAAK,IAAI;oBAC/C,MAAM,sBAAsB,CAAC,QAAQ,KAAK,IAAI,CAAC,KAAK,EAAE;oBACtD,MAAM,oBAAoB,QAAQ,CAAC,KAAK,KAAK,EAAE;oBAC/C,MAAM,aAAa,uBAAuB,sBAAsB;oBAEhE,qDAAqD;oBACrD,MAAM,eAAe,OAAO,CAAC,KAAK,KAAK,EAAE;oBAEzC,yCAAyC;oBACzC,MAAM,MAAM,AAAC,aAAa,eAAgB;oBAE1C,OAAO,KAAK,KAAK,CAAC,MAAM,OAAO,IAAI,yBAAyB;;gBAC9D;;YAEA,MAAM,2BAA2B,aAAa,eAAe,YAAY,WAAW;YAEpF,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;QACF;wCAAG;QAAC;QAAM;KAAa;IAEvB,4DAA4D;IAE5D,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAW;kBACf,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;;8BAErB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,6LAAC;oCAAK,WAAU;8CAAyC;;;;;;;;;;;;sCAG3D,6LAAC;4BAAI,WAAU;sCAEZ,mCACC,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;;oCAAsC;oCACjE;;;;;;;;;;;;;;;;;;8BAOd,6LAAC;oBAAI,WAAU;;sCAGb,6LAAC,0JAAA,CAAA,aAAU;4BAAC,MAAK;4BAAQ,OAAO,MAAM,SAAS;sCAC7C,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAsC,MAAM,aAAa;;;;;;0DACxE,6LAAC;gDAAI,WAAU;;oDAA2B;oDAAI,MAAM,SAAS;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;sCAMpE,6LAAC,0JAAA,CAAA,aAAU;4BACT,MAAK;4BACL,OAAO,MAAM,UAAU;4BACvB,YAAY,AAAC,MAAM,UAAU,GAAG,MAAM,aAAa,GAAI;sCAEvD,cAAA,6LAAC;gCACC,WAAU;gCACV,cAAY,CAAC,iBAAiB,EAAE,MAAM,UAAU,CAAC,KAAK,CAAC;;kDAEvD,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAoC,MAAM,UAAU;;;;;;0DACnE,6LAAC;gDAAI,WAAU;0DAAyB;;;;;;;;;;;;;;;;;;;;;;;sCAK9C,6LAAC,0JAAA,CAAA,aAAU;4BACT,MAAK;4BACL,OAAO,MAAM,OAAO;4BACpB,YAAY,AAAC,MAAM,OAAO,GAAG,MAAM,aAAa,GAAI;sCAEpD,cAAA,6LAAC;gCACC,WAAU;gCACV,cAAY,CAAC,eAAe,EAAE,MAAM,OAAO,CAAC,KAAK,CAAC;;kDAElD,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAqC,MAAM,OAAO;;;;;;0DACjE,6LAAC;gDAAI,WAAU;0DAA0B;;;;;;;;;;;;;;;;;;;;;;;sCAK/C,6LAAC,0JAAA,CAAA,aAAU;4BACT,MAAK;4BACL,OAAO,MAAM,YAAY;4BACzB,YAAY,AAAC,MAAM,YAAY,GAAG,MAAM,aAAa,GAAI;sCAEzD,cAAA,6LAAC;gCACC,WAAU;gCACV,cAAY,CAAC,oBAAoB,EAAE,MAAM,YAAY,CAAC,KAAK,CAAC;;kDAE5D,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAmC,MAAM,YAAY;;;;;;0DACpE,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;sCAM7C,6LAAC,0JAAA,CAAA,aAAU;4BACT,MAAK;4BACL,OAAO,MAAM,SAAS;4BACtB,YAAY,AAAC,MAAM,SAAS,GAAG,MAAM,aAAa,GAAI;sCAEtD,cAAA,6LAAC;gCACC,WAAU;gCACV,cAAY,CAAC,gBAAgB,EAAE,MAAM,SAAS,CAAC,KAAK,CAAC;;kDAErD,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAmC,MAAM,SAAS;;;;;;0DACjE,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;sCAM7C,6LAAC,0JAAA,CAAA,aAAU;4BACT,MAAK;4BACL,OAAO,MAAM,WAAW;4BACxB,YAAY,AAAC,MAAM,WAAW,GAAG,MAAM,aAAa,GAAI;sCAExD,cAAA,6LAAC;gCACC,WAAU;gCACV,cAAY,CAAC,kBAAkB,EAAE,MAAM,WAAW,CAAC,KAAK,CAAC;;kDAEzD,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAqC,MAAM,WAAW;;;;;;0DACrE,6LAAC;gDAAI,WAAU;0DAA0B;;;;;;;;;;;;;;;;;;;;;;;sCAM/C,6LAAC,0JAAA,CAAA,aAAU;4BACT,MAAK;4BACL,OAAO,MAAM,eAAe;sCAE5B,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;;oDAAqC,MAAM,eAAe,CAAC,cAAc;oDAAG;;;;;;;0DAC3F,6LAAC;gDAAI,WAAU;;oDAA0B;oDAAI,MAAM,WAAW,CAAC,cAAc;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQvF,MAAM,aAAa,GAAG,mBACrB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAK;;;;;;8CACN,6LAAC;oCAAK,WAAW,CAAC,UAAU,EAC1B,MAAM,wBAAwB,IAAI,KAAK,qBACvC,MAAM,wBAAwB,IAAI,KAAK,oBACvC,MAAM,wBAAwB,IAAI,KAAK,oBAAoB,kBAC3D;;wCACC,MAAM,wBAAwB,CAAC,OAAO,CAAC;wCAAG;;;;;;;;;;;;;sCAG/C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAW,CAAC,yDAAyD,EACnE,MAAM,wBAAwB,IAAI,KAAK,qDACvC,MAAM,wBAAwB,IAAI,KAAK,mDACvC,MAAM,wBAAwB,IAAI,KAAK,mDACvC,gDACA;gCACF,OAAO;oCAAE,OAAO,GAAG,KAAK,GAAG,CAAC,MAAM,wBAAwB,EAAE,KAAK,CAAC,CAAC;gCAAC;;;;;;;;;;;sCAGxE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAK;;;;;;8CACN,6LAAC;;wCAAM,MAAM,UAAU;wCAAC;wCAAK,MAAM,SAAS;wCAAC;wCAAK,MAAM,WAAW;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlF;GA/QwB;KAAA", "debugId": null}}, {"offset": {"line": 5139, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 5337, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 5371, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/modals/CableActionModals.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport {\n  AlertTriangle,\n  FileText,\n  X,\n  Download,\n  AlertCircle,\n  CheckCircle,\n  Loader2,\n  Zap,\n  Lock,\n  HelpCircle\n} from 'lucide-react'\nimport { Cavo } from '@/types'\n\n// Types for modal props\ninterface BaseModalProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n}\n\ninterface DisconnectModalProps extends BaseModalProps {\n  onConfirm: (cavoId: string) => Promise<void>\n}\n\ninterface GeneratePdfModalProps extends BaseModalProps {\n  onGenerate: (cavoId: string, options: PdfGenerationOptions) => Promise<void>\n}\n\ninterface CertificationErrorModalProps extends BaseModalProps {\n  errorMessage?: string\n  missingRequirements?: string[]\n}\n\ninterface PdfGenerationOptions {\n  fileName: string\n  includeTestData: boolean\n  format: 'standard' | 'detailed'\n  emailRecipient?: string\n}\n\n// Enhanced Modal Header Component with prominent cable ID\ninterface EnhancedModalHeaderProps {\n  icon: React.ReactNode\n  title: string\n  cableId: string\n  description?: string\n}\n\nconst EnhancedModalHeader: React.FC<EnhancedModalHeaderProps> = ({\n  icon,\n  title,\n  cableId,\n  description\n}) => (\n  <DialogHeader>\n    <DialogTitle className=\"flex items-center gap-2\">\n      {icon}\n      <span className=\"flex items-center gap-2\">\n        {title}\n        <span className=\"px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-sm font-mono font-semibold\">\n          {cableId}\n        </span>\n      </span>\n    </DialogTitle>\n    {description && (\n      <DialogDescription className=\"text-sm text-muted-foreground\">\n        {description}\n      </DialogDescription>\n    )}\n  </DialogHeader>\n)\n\n// Enhanced Dialog Content with improved overlay behavior\ninterface EnhancedDialogContentProps {\n  children: React.ReactNode\n  className?: string\n  onKeyDown?: (e: React.KeyboardEvent) => void\n  ariaLabelledBy?: string\n  ariaDescribedBy?: string\n}\n\nconst EnhancedDialogContent: React.FC<EnhancedDialogContentProps> = ({\n  children,\n  className = \"sm:max-w-md\",\n  onKeyDown,\n  ariaLabelledBy,\n  ariaDescribedBy\n}) => (\n  <DialogContent\n    className={className}\n    onKeyDown={onKeyDown}\n    aria-labelledby={ariaLabelledBy}\n    aria-describedby={ariaDescribedBy}\n    onPointerDownOutside={(e) => e.preventDefault()} // Prevent closing on outside click\n    onEscapeKeyDown={(e) => {\n      // Allow ESC key to close\n      if (onKeyDown) {\n        onKeyDown(e as any)\n      }\n    }}\n  >\n    {children}\n  </DialogContent>\n)\n\n// Disconnect Confirmation Modal with enhanced UI\nexport const DisconnectCableModal: React.FC<DisconnectModalProps> = ({\n  open,\n  onClose,\n  cavo,\n  onConfirm\n}) => {\n  const [isLoading, setIsLoading] = useState(false)\n  const [showFinalConfirmation, setShowFinalConfirmation] = useState(false)\n\n  const handleInitialConfirm = () => {\n    setShowFinalConfirmation(true)\n  }\n\n  const handleFinalConfirm = async () => {\n    if (!cavo) return\n\n    setIsLoading(true)\n    try {\n      await onConfirm(cavo.id_cavo)\n      onClose()\n      setShowFinalConfirmation(false)\n    } catch (error) {\n      console.error('Error disconnecting cable:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleClose = () => {\n    setShowFinalConfirmation(false)\n    onClose()\n  }\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      handleClose()\n    }\n  }\n\n  if (!cavo) return null\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <EnhancedDialogContent\n        className=\"sm:max-w-md\"\n        onKeyDown={handleKeyDown}\n        ariaLabelledBy=\"disconnect-modal-title\"\n        ariaDescribedBy=\"disconnect-modal-description\"\n      >\n        <EnhancedModalHeader\n          icon={<Zap className=\"h-5 w-5 text-orange-500\" />}\n          title=\"Gestione Collegamenti\"\n          cableId={cavo.id_cavo}\n          description=\"Gestisci le connessioni del cavo selezionato\"\n        />\n\n        {!showFinalConfirmation ? (\n          <>\n            <div className=\"py-4\">\n              <div className=\"flex items-center gap-2 mb-4\">\n                <div className=\"flex items-center gap-1\">\n                  <div className=\"w-3 h-3 rounded-full bg-green-500\"></div>\n                  <div className=\"w-3 h-3 rounded-full bg-green-500\"></div>\n                </div>\n                <span className=\"text-sm font-medium text-green-700\">\n                  Completamente collegato\n                  <HelpCircle\n                    className=\"inline h-4 w-4 ml-1 cursor-help\"\n                    title=\"Cavo collegato sia all'origine che alla destinazione\"\n                  />\n                </span>\n              </div>\n\n              <div className=\"space-y-3\">\n                <div>\n                  <Label htmlFor=\"responsabile-collegamento\" className=\"text-sm font-medium\">\n                    Responsabile Collegamento\n                  </Label>\n                  <select\n                    id=\"responsabile-collegamento\"\n                    className=\"w-full mt-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                    defaultValue=\"\"\n                  >\n                    <option value=\"\" disabled>Seleziona responsabile...</option>\n                    <option value=\"cantiere\">Cantiere</option>\n                    <option value=\"tecnico1\">Tecnico 1</option>\n                    <option value=\"tecnico2\">Tecnico 2</option>\n                  </select>\n                </div>\n              </div>\n            </div>\n\n            <Alert className=\"my-4 bg-orange-50 border-orange-200\">\n              <AlertTriangle className=\"h-4 w-4 text-orange-600\" />\n              <AlertDescription className=\"text-orange-800\">\n                <strong>Attenzione:</strong> Lo scollegamento rimuoverà tutte le connessioni attive del cavo.\n                Questa azione potrebbe influenzare altri componenti collegati.\n              </AlertDescription>\n            </Alert>\n\n            <DialogFooter className=\"gap-2\">\n              <Button\n                variant=\"outline\"\n                onClick={handleClose}\n                disabled={isLoading}\n                className=\"flex-1 hover:bg-gray-50\"\n              >\n                Annulla\n              </Button>\n              <Button\n                variant=\"destructive\"\n                onClick={handleInitialConfirm}\n                disabled={isLoading}\n                className=\"flex-1 hover:bg-red-600\"\n              >\n                <AlertTriangle className=\"mr-2 h-4 w-4\" />\n                Scollega Completamente\n              </Button>\n            </DialogFooter>\n          </>\n        ) : (\n          <>\n            <div className=\"py-4 text-center\">\n              <div className=\"mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4\">\n                <AlertTriangle className=\"h-6 w-6 text-red-600\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                Conferma Scollegamento\n              </h3>\n              <p className=\"text-sm text-gray-600 mb-4\">\n                Sei veramente sicuro di voler scollegare completamente il cavo <strong>{cavo.id_cavo}</strong>?\n              </p>\n              <div className=\"bg-red-50 border border-red-200 rounded-md p-3\">\n                <p className=\"text-sm text-red-800 font-medium\">\n                  ⚠️ Questa azione è irreversibile\n                </p>\n              </div>\n            </div>\n\n            <DialogFooter className=\"gap-2\">\n              <Button\n                variant=\"outline\"\n                onClick={() => setShowFinalConfirmation(false)}\n                disabled={isLoading}\n                className=\"flex-1\"\n              >\n                No, Annulla\n              </Button>\n              <Button\n                variant=\"destructive\"\n                onClick={handleFinalConfirm}\n                disabled={isLoading}\n                className=\"flex-1\"\n              >\n                {isLoading ? (\n                  <>\n                    <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                    Scollegando...\n                  </>\n                ) : (\n                  'Sì, Scollega'\n                )}\n              </Button>\n            </DialogFooter>\n          </>\n        )}\n      </EnhancedDialogContent>\n    </Dialog>\n  )\n}\n\n// Enhanced PDF Generation Modal\nexport const GeneratePdfModal: React.FC<GeneratePdfModalProps> = ({\n  open,\n  onClose,\n  cavo,\n  onGenerate\n}) => {\n  const [isLoading, setIsLoading] = useState(false)\n  const [options, setOptions] = useState<PdfGenerationOptions>({\n    fileName: '',\n    includeTestData: true,\n    format: 'standard',\n    emailRecipient: ''\n  })\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})\n\n  // Set default filename when modal opens\n  useEffect(() => {\n    if (cavo && open) {\n      setOptions(prev => ({\n        ...prev,\n        fileName: `Certificato_${cavo.id_cavo}_${new Date().toISOString().split('T')[0]}.pdf`\n      }))\n      setValidationErrors({})\n    }\n  }, [cavo, open])\n\n  // Validate form fields\n  const validateForm = () => {\n    const errors: Record<string, string> = {}\n\n    if (!options.fileName.trim()) {\n      errors.fileName = 'Il nome del file è obbligatorio'\n    } else if (!/^[a-zA-Z0-9_\\-\\s]+\\.pdf$/i.test(options.fileName)) {\n      errors.fileName = 'Il nome del file deve terminare con .pdf e contenere solo caratteri validi'\n    }\n\n    if (options.emailRecipient && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(options.emailRecipient)) {\n      errors.emailRecipient = 'Inserisci un indirizzo email valido'\n    }\n\n    setValidationErrors(errors)\n    return Object.keys(errors).length === 0\n  }\n\n  const handleGenerate = async () => {\n    if (!cavo || !validateForm()) return\n\n    setIsLoading(true)\n    try {\n      await onGenerate(cavo.id_cavo, options)\n      onClose()\n    } catch (error) {\n      console.error('Error generating PDF:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      onClose()\n    }\n  }\n\n  const isFormValid = options.fileName.trim() && Object.keys(validationErrors).length === 0\n\n  if (!cavo) return null\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <EnhancedDialogContent\n        className=\"sm:max-w-lg\"\n        onKeyDown={handleKeyDown}\n        ariaLabelledBy=\"pdf-modal-title\"\n      >\n        <EnhancedModalHeader\n          icon={<FileText className=\"h-5 w-5 text-blue-500\" />}\n          title=\"Genera Certificato\"\n          cableId={cavo.id_cavo}\n          description=\"Configura le opzioni per la generazione del certificato PDF\"\n        />\n\n        <div className=\"space-y-4 py-4\">\n          {/* File Name */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"fileName\" className=\"text-sm font-medium\">\n              Nome File *\n            </Label>\n            <Input\n              id=\"fileName\"\n              value={options.fileName}\n              onChange={(e) => {\n                setOptions(prev => ({ ...prev, fileName: e.target.value }))\n                if (validationErrors.fileName) {\n                  setValidationErrors(prev => ({ ...prev, fileName: '' }))\n                }\n              }}\n              onBlur={validateForm}\n              placeholder=\"Certificato_C001_2025-06-29.pdf\"\n              className={validationErrors.fileName ? 'border-red-500 focus:ring-red-500' : ''}\n            />\n            {validationErrors.fileName && (\n              <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                <AlertCircle className=\"h-3 w-3\" />\n                {validationErrors.fileName}\n              </p>\n            )}\n          </div>\n\n          {/* Format Selection */}\n          <div className=\"space-y-3\">\n            <Label className=\"text-sm font-medium\">Formato Certificato</Label>\n            <div className=\"space-y-2\">\n              <label className=\"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors\">\n                <input\n                  type=\"radio\"\n                  name=\"format\"\n                  value=\"standard\"\n                  checked={options.format === 'standard'}\n                  onChange={(e) => setOptions(prev => ({ ...prev, format: e.target.value as 'standard' | 'detailed' }))}\n                  className=\"text-blue-600 focus:ring-blue-500\"\n                />\n                <div>\n                  <span className=\"text-sm font-medium\">Standard</span>\n                  <p className=\"text-xs text-gray-500\">Certificato con informazioni essenziali</p>\n                </div>\n              </label>\n              <label className=\"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors\">\n                <input\n                  type=\"radio\"\n                  name=\"format\"\n                  value=\"detailed\"\n                  checked={options.format === 'detailed'}\n                  onChange={(e) => setOptions(prev => ({ ...prev, format: e.target.value as 'standard' | 'detailed' }))}\n                  className=\"text-blue-600 focus:ring-blue-500\"\n                />\n                <div>\n                  <span className=\"text-sm font-medium\">Dettagliato</span>\n                  <p className=\"text-xs text-gray-500\">Certificato con tutti i dati tecnici</p>\n                </div>\n              </label>\n            </div>\n          </div>\n\n          {/* Include Test Data */}\n          <div className=\"flex items-center space-x-3 p-2 border rounded-md\">\n            <Checkbox\n              id=\"includeTestData\"\n              checked={options.includeTestData}\n              onCheckedChange={(checked) => setOptions(prev => ({ ...prev, includeTestData: checked as boolean }))}\n            />\n            <div>\n              <Label htmlFor=\"includeTestData\" className=\"text-sm font-medium cursor-pointer\">\n                Includi Dati di Collaudo\n              </Label>\n              <p className=\"text-xs text-gray-500\">Aggiunge i risultati dei test al certificato</p>\n            </div>\n          </div>\n\n          {/* Email Recipient (Optional) */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"emailRecipient\" className=\"text-sm font-medium\">\n              Email Destinatario (Opzionale)\n            </Label>\n            <Input\n              id=\"emailRecipient\"\n              type=\"email\"\n              value={options.emailRecipient}\n              onChange={(e) => {\n                setOptions(prev => ({ ...prev, emailRecipient: e.target.value }))\n                if (validationErrors.emailRecipient) {\n                  setValidationErrors(prev => ({ ...prev, emailRecipient: '' }))\n                }\n              }}\n              onBlur={validateForm}\n              placeholder=\"<EMAIL>\"\n              className={validationErrors.emailRecipient ? 'border-red-500 focus:ring-red-500' : ''}\n            />\n            {validationErrors.emailRecipient && (\n              <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                <AlertCircle className=\"h-3 w-3\" />\n                {validationErrors.emailRecipient}\n              </p>\n            )}\n          </div>\n        </div>\n\n        <DialogFooter className=\"gap-2\">\n          <Button\n            variant=\"outline\"\n            onClick={onClose}\n            disabled={isLoading}\n            className=\"flex-1 hover:bg-gray-50\"\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleGenerate}\n            disabled={isLoading || !isFormValid}\n            className={`flex-1 ${!isFormValid ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-600'}`}\n          >\n            {isLoading ? (\n              <>\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                Generando...\n              </>\n            ) : (\n              <>\n                <Download className=\"mr-2 h-4 w-4\" />\n                Genera PDF\n              </>\n            )}\n          </Button>\n        </DialogFooter>\n      </EnhancedDialogContent>\n    </Dialog>\n  )\n}\n\n// Enhanced Certification Error Modal\nexport const CertificationErrorModal: React.FC<CertificationErrorModalProps> = ({\n  open,\n  onClose,\n  cavo,\n  errorMessage,\n  missingRequirements = []\n}) => {\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      onClose()\n    }\n  }\n\n  const defaultRequirements = [\n    'Il cavo deve essere nello stato \"Installato\"',\n    'Il cavo deve essere completamente collegato',\n    'Tutti i dati di collaudo devono essere presenti'\n  ]\n\n  const requirements = missingRequirements.length > 0 ? missingRequirements : defaultRequirements\n\n  if (!cavo) return null\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <EnhancedDialogContent\n        className=\"sm:max-w-md\"\n        onKeyDown={handleKeyDown}\n        ariaLabelledBy=\"certification-error-title\"\n      >\n        <EnhancedModalHeader\n          icon={<AlertCircle className=\"h-5 w-5 text-red-500\" />}\n          title=\"Impossibile Certificare Cavo\"\n          cableId={cavo.id_cavo}\n          description=\"Il cavo non può essere certificato nel suo stato attuale\"\n        />\n\n        <div className=\"py-4\">\n          {errorMessage && (\n            <Alert className=\"mb-4 bg-red-50 border-red-200\">\n              <AlertCircle className=\"h-4 w-4 text-red-600\" />\n              <AlertDescription className=\"text-red-800\">{errorMessage}</AlertDescription>\n            </Alert>\n          )}\n\n          <div className=\"space-y-4\">\n            <div>\n              <h4 className=\"text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2\">\n                <AlertTriangle className=\"h-4 w-4 text-amber-500\" />\n                Requisiti mancanti:\n              </h4>\n              <ul className=\"space-y-3\">\n                {requirements.map((requirement, index) => (\n                  <li key={index} className=\"flex items-start gap-3 p-2 bg-red-50 border border-red-200 rounded-md\">\n                    <div className=\"flex-shrink-0 w-5 h-5 bg-red-100 rounded-full flex items-center justify-center mt-0.5\">\n                      <X className=\"h-3 w-3 text-red-600\" />\n                    </div>\n                    <span className=\"text-sm text-red-800\">{requirement}</span>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <Alert className=\"bg-blue-50 border-blue-200\">\n              <CheckCircle className=\"h-4 w-4 text-blue-600\" />\n              <AlertDescription className=\"text-blue-800\">\n                <strong>Prossimi passi:</strong> Completa tutti i requisiti sopra elencati per abilitare la certificazione del cavo.\n              </AlertDescription>\n            </Alert>\n          </div>\n        </div>\n\n        <DialogFooter>\n          <Button\n            onClick={onClose}\n            className=\"w-full hover:bg-blue-600 focus:ring-2 focus:ring-blue-500\"\n          >\n            <CheckCircle className=\"mr-2 h-4 w-4\" />\n            Ho Capito\n          </Button>\n        </DialogFooter>\n      </EnhancedDialogContent>\n    </Dialog>\n  )\n}\n\n// Cable Certification Modal\ninterface CertificationModalProps extends BaseModalProps {\n  onCertify: (cavoId: string, certificationData: CertificationData) => Promise<void>\n}\n\ninterface CertificationData {\n  responsabile: string\n  dataCertificazione: string\n  esitoCertificazione: 'CONFORME' | 'NON_CONFORME' | 'PARZIALMENTE_CONFORME'\n  note?: string\n}\n\nexport const CertificationModal: React.FC<CertificationModalProps> = ({\n  open,\n  onClose,\n  cavo,\n  onCertify\n}) => {\n  const [isLoading, setIsLoading] = useState(false)\n  const [formData, setFormData] = useState<CertificationData>({\n    responsabile: '',\n    dataCertificazione: new Date().toISOString().split('T')[0],\n    esitoCertificazione: 'CONFORME',\n    note: ''\n  })\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})\n\n  // Reset form when modal opens\n  useEffect(() => {\n    if (open && cavo) {\n      setFormData({\n        responsabile: '',\n        dataCertificazione: new Date().toISOString().split('T')[0],\n        esitoCertificazione: 'CONFORME',\n        note: ''\n      })\n      setValidationErrors({})\n    }\n  }, [open, cavo])\n\n  const validateForm = () => {\n    const errors: Record<string, string> = {}\n\n    if (!formData.responsabile.trim()) {\n      errors.responsabile = 'Il responsabile è obbligatorio'\n    }\n\n    if (!formData.dataCertificazione) {\n      errors.dataCertificazione = 'La data di certificazione è obbligatoria'\n    } else {\n      const selectedDate = new Date(formData.dataCertificazione)\n      const today = new Date()\n      today.setHours(0, 0, 0, 0)\n\n      if (selectedDate > today) {\n        errors.dataCertificazione = 'La data non può essere futura'\n      }\n    }\n\n    setValidationErrors(errors)\n    return Object.keys(errors).length === 0\n  }\n\n  const handleCertify = async () => {\n    if (!cavo || !validateForm()) return\n\n    setIsLoading(true)\n    try {\n      await onCertify(cavo.id_cavo, formData)\n      onClose()\n    } catch (error) {\n      console.error('Error certifying cable:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      onClose()\n    }\n  }\n\n  const isFormValid = formData.responsabile.trim() && formData.dataCertificazione && Object.keys(validationErrors).length === 0\n\n  if (!cavo) return null\n\n  // Mock cable status for demonstration\n  const cableStatus = {\n    installato: true,\n    collegato: true,\n    certificato: false\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <EnhancedDialogContent\n        className=\"sm:max-w-lg\"\n        onKeyDown={handleKeyDown}\n        ariaLabelledBy=\"certification-modal-title\"\n      >\n        <EnhancedModalHeader\n          icon={<Lock className=\"h-5 w-5 text-blue-500\" />}\n          title=\"Gestione Certificazione\"\n          cableId={cavo.id_cavo}\n          description=\"Certifica il cavo dopo aver completato tutti i controlli\"\n        />\n\n        <div className=\"space-y-4 py-4\">\n          {/* Cable Status Indicators */}\n          <div className=\"space-y-3\">\n            <h4 className=\"text-sm font-semibold text-gray-900\">Stato Cavo:</h4>\n            <div className=\"grid grid-cols-3 gap-3\">\n              <div className=\"flex items-center gap-2 p-2 bg-gray-50 rounded-md\">\n                <div className={`w-3 h-3 rounded-full ${cableStatus.installato ? 'bg-green-500' : 'bg-red-500'}`}></div>\n                <span className=\"text-sm font-medium\">\n                  {cableStatus.installato ? '✓' : '✗'} Installato\n                </span>\n              </div>\n              <div className=\"flex items-center gap-2 p-2 bg-gray-50 rounded-md\">\n                <div className={`w-3 h-3 rounded-full ${cableStatus.collegato ? 'bg-green-500' : 'bg-red-500'}`}></div>\n                <span className=\"text-sm font-medium\">\n                  {cableStatus.collegato ? '✓' : '✗'} Collegato\n                </span>\n              </div>\n              <div className=\"flex items-center gap-2 p-2 bg-gray-50 rounded-md\">\n                <div className={`w-3 h-3 rounded-full ${cableStatus.certificato ? 'bg-green-500' : 'bg-orange-500'}`}></div>\n                <span className=\"text-sm font-medium\">\n                  {cableStatus.certificato ? '✓' : '⚠'} Non certificato\n                </span>\n              </div>\n            </div>\n          </div>\n\n          {/* Responsabile Certificazione */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"responsabile\" className=\"text-sm font-medium\">\n              Responsabile Certificazione *\n            </Label>\n            <select\n              id=\"responsabile\"\n              value={formData.responsabile}\n              onChange={(e) => {\n                setFormData(prev => ({ ...prev, responsabile: e.target.value }))\n                if (validationErrors.responsabile) {\n                  setValidationErrors(prev => ({ ...prev, responsabile: '' }))\n                }\n              }}\n              onBlur={validateForm}\n              className={`w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${\n                validationErrors.responsabile ? 'border-red-500' : 'border-gray-300'\n              }`}\n            >\n              <option value=\"\" disabled>Seleziona responsabile...</option>\n              <option value=\"cantiere\">Cantiere</option>\n              <option value=\"tecnico_1\">Tecnico 1</option>\n              <option value=\"tecnico_2\">Tecnico 2</option>\n              <option value=\"supervisore\">Supervisore</option>\n            </select>\n            {validationErrors.responsabile && (\n              <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                <AlertCircle className=\"h-3 w-3\" />\n                {validationErrors.responsabile}\n              </p>\n            )}\n          </div>\n\n          {/* Data Certificazione */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"dataCertificazione\" className=\"text-sm font-medium\">\n              Data Certificazione *\n            </Label>\n            <Input\n              id=\"dataCertificazione\"\n              type=\"date\"\n              value={formData.dataCertificazione}\n              onChange={(e) => {\n                setFormData(prev => ({ ...prev, dataCertificazione: e.target.value }))\n                if (validationErrors.dataCertificazione) {\n                  setValidationErrors(prev => ({ ...prev, dataCertificazione: '' }))\n                }\n              }}\n              onBlur={validateForm}\n              max={new Date().toISOString().split('T')[0]}\n              className={validationErrors.dataCertificazione ? 'border-red-500 focus:ring-red-500' : ''}\n            />\n            {validationErrors.dataCertificazione && (\n              <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                <AlertCircle className=\"h-3 w-3\" />\n                {validationErrors.dataCertificazione}\n              </p>\n            )}\n          </div>\n\n          {/* Esito Certificazione */}\n          <div className=\"space-y-2\">\n            <Label className=\"text-sm font-medium\">Esito Certificazione</Label>\n            <select\n              value={formData.esitoCertificazione}\n              onChange={(e) => setFormData(prev => ({ ...prev, esitoCertificazione: e.target.value as any }))}\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n            >\n              <option value=\"CONFORME\">CONFORME</option>\n              <option value=\"NON_CONFORME\">NON CONFORME</option>\n              <option value=\"PARZIALMENTE_CONFORME\">PARZIALMENTE CONFORME</option>\n            </select>\n          </div>\n\n          {/* Note (opzionale) */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"note\" className=\"text-sm font-medium\">\n              Note (opzionale)\n            </Label>\n            <textarea\n              id=\"note\"\n              value={formData.note}\n              onChange={(e) => setFormData(prev => ({ ...prev, note: e.target.value }))}\n              placeholder=\"Inserisci eventuali note sulla certificazione...\"\n              rows={3}\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none\"\n            />\n          </div>\n        </div>\n\n        <DialogFooter className=\"gap-2\">\n          <Button\n            variant=\"outline\"\n            onClick={onClose}\n            disabled={isLoading}\n            className=\"flex-1 hover:bg-gray-50\"\n          >\n            Chiudi\n          </Button>\n          <Button\n            onClick={handleCertify}\n            disabled={isLoading || !isFormValid}\n            className={`flex-1 ${!isFormValid ? 'opacity-50 cursor-not-allowed bg-gray-400' : 'bg-blue-600 hover:bg-blue-700'}`}\n          >\n            {isLoading ? (\n              <>\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                Certificando...\n              </>\n            ) : (\n              <>\n                <Lock className=\"mr-2 h-4 w-4\" />\n                Certifica Cavo\n              </>\n            )}\n          </Button>\n        </DialogFooter>\n      </EnhancedDialogContent>\n    </Dialog>\n  )\n}\n\n// Success Toast Component (for feedback after actions)\ninterface SuccessToastProps {\n  message: string\n  visible: boolean\n  onClose: () => void\n}\n\nexport const SuccessToast: React.FC<SuccessToastProps> = ({\n  message,\n  visible,\n  onClose\n}) => {\n  useEffect(() => {\n    if (visible) {\n      const timer = setTimeout(() => {\n        onClose()\n      }, 3000)\n      return () => clearTimeout(timer)\n    }\n  }, [visible, onClose])\n\n  if (!visible) return null\n\n  return (\n    <div className=\"fixed top-4 right-4 z-50 animate-in slide-in-from-top-2\">\n      <Alert className=\"bg-green-50 border-green-200 text-green-800 shadow-lg\">\n        <CheckCircle className=\"h-4 w-4\" />\n        <AlertDescription className=\"font-medium\">\n          {message}\n        </AlertDescription>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"absolute top-2 right-2 h-6 w-6 p-0 hover:bg-green-100\"\n          onClick={onClose}\n        >\n          <X className=\"h-3 w-3\" />\n        </Button>\n      </Alert>\n    </div>\n  )\n}\n\n// Export all modal types for easy importing\nexport type {\n  DisconnectModalProps,\n  GeneratePdfModalProps,\n  CertificationErrorModalProps,\n  CertificationModalProps,\n  PdfGenerationOptions,\n  CertificationData\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAhBA;;;;;;;;;AAiEA,MAAM,sBAA0D,CAAC,EAC/D,IAAI,EACJ,KAAK,EACL,OAAO,EACP,WAAW,EACZ,iBACC,6LAAC,qIAAA,CAAA,eAAY;;0BACX,6LAAC,qIAAA,CAAA,cAAW;gBAAC,WAAU;;oBACpB;kCACD,6LAAC;wBAAK,WAAU;;4BACb;0CACD,6LAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;;;;;;;YAIN,6BACC,6LAAC,qIAAA,CAAA,oBAAiB;gBAAC,WAAU;0BAC1B;;;;;;;;;;;;KAlBH;AAiCN,MAAM,wBAA8D,CAAC,EACnE,QAAQ,EACR,YAAY,aAAa,EACzB,SAAS,EACT,cAAc,EACd,eAAe,EAChB,iBACC,6LAAC,qIAAA,CAAA,gBAAa;QACZ,WAAW;QACX,WAAW;QACX,mBAAiB;QACjB,oBAAkB;QAClB,sBAAsB,CAAC,IAAM,EAAE,cAAc;QAC7C,iBAAiB,CAAC;YAChB,yBAAyB;YACzB,IAAI,WAAW;gBACb,UAAU;YACZ;QACF;kBAEC;;;;;;MApBC;AAyBC,MAAM,uBAAuD,CAAC,EACnE,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,SAAS,EACV;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,MAAM,uBAAuB;QAC3B,yBAAyB;IAC3B;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,MAAM;QAEX,aAAa;QACb,IAAI;YACF,MAAM,UAAU,KAAK,OAAO;YAC5B;YACA,yBAAyB;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,cAAc;QAClB,yBAAyB;QACzB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC;YACC,WAAU;YACV,WAAW;YACX,gBAAe;YACf,iBAAgB;;8BAEhB,6LAAC;oBACC,oBAAM,6LAAC,mMAAA,CAAA,MAAG;wBAAC,WAAU;;;;;;oBACrB,OAAM;oBACN,SAAS,KAAK,OAAO;oBACrB,aAAY;;;;;;gBAGb,CAAC,sCACA;;sCACE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;;;;;;;sDAEjB,6LAAC;4CAAK,WAAU;;gDAAqC;8DAEnD,6LAAC,qNAAA,CAAA,aAAU;oDACT,WAAU;oDACV,OAAM;;;;;;;;;;;;;;;;;;8CAKZ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;;0DACC,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAA4B,WAAU;0DAAsB;;;;;;0DAG3E,6LAAC;gDACC,IAAG;gDACH,WAAU;gDACV,cAAa;;kEAEb,6LAAC;wDAAO,OAAM;wDAAG,QAAQ;kEAAC;;;;;;kEAC1B,6LAAC;wDAAO,OAAM;kEAAW;;;;;;kEACzB,6LAAC;wDAAO,OAAM;kEAAW;;;;;;kEACzB,6LAAC;wDAAO,OAAM;kEAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMjC,6LAAC,oIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,6LAAC,oIAAA,CAAA,mBAAgB;oCAAC,WAAU;;sDAC1B,6LAAC;sDAAO;;;;;;wCAAoB;;;;;;;;;;;;;sCAKhC,6LAAC,qIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;oCACV,WAAU;8CACX;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;oCACV,WAAU;;sDAEV,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;iDAMhD;;sCACE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;8CAE3B,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,6LAAC;oCAAE,WAAU;;wCAA6B;sDACuB,6LAAC;sDAAQ,KAAK,OAAO;;;;;;wCAAU;;;;;;;8CAEhG,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;;;;;;sCAMpD,6LAAC,qIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,yBAAyB;oCACxC,UAAU;oCACV,WAAU;8CACX;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,0BACC;;0DACE,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA8B;;uDAInD;;;;;;;;;;;;;;;;;;;;;;;;;AASlB;GAzKa;MAAA;AA4KN,MAAM,mBAAoD,CAAC,EAChE,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,UAAU,EACX;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;QAC3D,UAAU;QACV,iBAAiB;QACjB,QAAQ;QACR,gBAAgB;IAClB;IACA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAElF,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,QAAQ,MAAM;gBAChB;kDAAW,CAAA,OAAQ,CAAC;4BAClB,GAAG,IAAI;4BACP,UAAU,CAAC,YAAY,EAAE,KAAK,OAAO,CAAC,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;wBACvF,CAAC;;gBACD,oBAAoB,CAAC;YACvB;QACF;qCAAG;QAAC;QAAM;KAAK;IAEf,uBAAuB;IACvB,MAAM,eAAe;QACnB,MAAM,SAAiC,CAAC;QAExC,IAAI,CAAC,QAAQ,QAAQ,CAAC,IAAI,IAAI;YAC5B,OAAO,QAAQ,GAAG;QACpB,OAAO,IAAI,CAAC,4BAA4B,IAAI,CAAC,QAAQ,QAAQ,GAAG;YAC9D,OAAO,QAAQ,GAAG;QACpB;QAEA,IAAI,QAAQ,cAAc,IAAI,CAAC,6BAA6B,IAAI,CAAC,QAAQ,cAAc,GAAG;YACxF,OAAO,cAAc,GAAG;QAC1B;QAEA,oBAAoB;QACpB,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;IACxC;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,QAAQ,CAAC,gBAAgB;QAE9B,aAAa;QACb,IAAI;YACF,MAAM,WAAW,KAAK,OAAO,EAAE;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF;IACF;IAEA,MAAM,cAAc,QAAQ,QAAQ,CAAC,IAAI,MAAM,OAAO,IAAI,CAAC,kBAAkB,MAAM,KAAK;IAExF,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC;YACC,WAAU;YACV,WAAW;YACX,gBAAe;;8BAEf,6LAAC;oBACC,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAC1B,OAAM;oBACN,SAAS,KAAK,OAAO;oBACrB,aAAY;;;;;;8BAGd,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAW,WAAU;8CAAsB;;;;;;8CAG1D,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,QAAQ,QAAQ;oCACvB,UAAU,CAAC;wCACT,WAAW,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;wCACzD,IAAI,iBAAiB,QAAQ,EAAE;4CAC7B,oBAAoB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,UAAU;gDAAG,CAAC;wCACxD;oCACF;oCACA,QAAQ;oCACR,aAAY;oCACZ,WAAW,iBAAiB,QAAQ,GAAG,sCAAsC;;;;;;gCAE9E,iBAAiB,QAAQ,kBACxB,6LAAC;oCAAE,WAAU;;sDACX,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCACtB,iBAAiB,QAAQ;;;;;;;;;;;;;sCAMhC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAM;oDACN,SAAS,QAAQ,MAAM,KAAK;oDAC5B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4DAA4B,CAAC;oDACnG,WAAU;;;;;;8DAEZ,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAsB;;;;;;sEACtC,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAGzC,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAM;oDACN,SAAS,QAAQ,MAAM,KAAK;oDAC5B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4DAA4B,CAAC;oDACnG,WAAU;;;;;;8DAEZ,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAsB;;;;;;sEACtC,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO7C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,SAAS,QAAQ,eAAe;oCAChC,iBAAiB,CAAC,UAAY,WAAW,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,iBAAiB;4CAAmB,CAAC;;;;;;8CAEpG,6LAAC;;sDACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAkB,WAAU;sDAAqC;;;;;;sDAGhF,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAKzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAiB,WAAU;8CAAsB;;;;;;8CAGhE,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,OAAO,QAAQ,cAAc;oCAC7B,UAAU,CAAC;wCACT,WAAW,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;wCAC/D,IAAI,iBAAiB,cAAc,EAAE;4CACnC,oBAAoB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,gBAAgB;gDAAG,CAAC;wCAC9D;oCACF;oCACA,QAAQ;oCACR,aAAY;oCACZ,WAAW,iBAAiB,cAAc,GAAG,sCAAsC;;;;;;gCAEpF,iBAAiB,cAAc,kBAC9B,6LAAC;oCAAE,WAAU;;sDACX,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCACtB,iBAAiB,cAAc;;;;;;;;;;;;;;;;;;;8BAMxC,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,aAAa,CAAC;4BACxB,WAAW,CAAC,OAAO,EAAE,CAAC,cAAc,kCAAkC,qBAAqB;sCAE1F,0BACC;;kDACE,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;6DAInD;;kDACE,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AASrD;IAzNa;MAAA;AA4NN,MAAM,0BAAkE,CAAC,EAC9E,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,YAAY,EACZ,sBAAsB,EAAE,EACzB;IACC,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF;IACF;IAEA,MAAM,sBAAsB;QAC1B;QACA;QACA;KACD;IAED,MAAM,eAAe,oBAAoB,MAAM,GAAG,IAAI,sBAAsB;IAE5E,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC;YACC,WAAU;YACV,WAAW;YACX,gBAAe;;8BAEf,6LAAC;oBACC,oBAAM,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;oBAC7B,OAAM;oBACN,SAAS,KAAK,OAAO;oBACrB,aAAY;;;;;;8BAGd,6LAAC;oBAAI,WAAU;;wBACZ,8BACC,6LAAC,oIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC,oIAAA,CAAA,mBAAgB;oCAAC,WAAU;8CAAgB;;;;;;;;;;;;sCAIhD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;gDAA2B;;;;;;;sDAGtD,6LAAC;4CAAG,WAAU;sDACX,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,6LAAC;oDAAe,WAAU;;sEACxB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,+LAAA,CAAA,IAAC;gEAAC,WAAU;;;;;;;;;;;sEAEf,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;;mDAJjC;;;;;;;;;;;;;;;;8CAUf,6LAAC,oIAAA,CAAA,QAAK;oCAAC,WAAU;;sDACf,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC,oIAAA,CAAA,mBAAgB;4CAAC,WAAU;;8DAC1B,6LAAC;8DAAO;;;;;;gDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;8BAMxC,6LAAC,qIAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,WAAU;;0CAEV,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;;;;;AAOpD;MApFa;AAkGN,MAAM,qBAAwD,CAAC,EACpE,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,SAAS,EACV;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;QAC1D,cAAc;QACd,oBAAoB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC1D,qBAAqB;QACrB,MAAM;IACR;IACA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAElF,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,QAAQ,MAAM;gBAChB,YAAY;oBACV,cAAc;oBACd,oBAAoB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBAC1D,qBAAqB;oBACrB,MAAM;gBACR;gBACA,oBAAoB,CAAC;YACvB;QACF;uCAAG;QAAC;QAAM;KAAK;IAEf,MAAM,eAAe;QACnB,MAAM,SAAiC,CAAC;QAExC,IAAI,CAAC,SAAS,YAAY,CAAC,IAAI,IAAI;YACjC,OAAO,YAAY,GAAG;QACxB;QAEA,IAAI,CAAC,SAAS,kBAAkB,EAAE;YAChC,OAAO,kBAAkB,GAAG;QAC9B,OAAO;YACL,MAAM,eAAe,IAAI,KAAK,SAAS,kBAAkB;YACzD,MAAM,QAAQ,IAAI;YAClB,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG;YAExB,IAAI,eAAe,OAAO;gBACxB,OAAO,kBAAkB,GAAG;YAC9B;QACF;QAEA,oBAAoB;QACpB,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;IACxC;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,QAAQ,CAAC,gBAAgB;QAE9B,aAAa;QACb,IAAI;YACF,MAAM,UAAU,KAAK,OAAO,EAAE;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF;IACF;IAEA,MAAM,cAAc,SAAS,YAAY,CAAC,IAAI,MAAM,SAAS,kBAAkB,IAAI,OAAO,IAAI,CAAC,kBAAkB,MAAM,KAAK;IAE5H,IAAI,CAAC,MAAM,OAAO;IAElB,sCAAsC;IACtC,MAAM,cAAc;QAClB,YAAY;QACZ,WAAW;QACX,aAAa;IACf;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC;YACC,WAAU;YACV,WAAW;YACX,gBAAe;;8BAEf,6LAAC;oBACC,oBAAM,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBACtB,OAAM;oBACN,SAAS,KAAK,OAAO;oBACrB,aAAY;;;;;;8BAGd,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,CAAC,qBAAqB,EAAE,YAAY,UAAU,GAAG,iBAAiB,cAAc;;;;;;8DAChG,6LAAC;oDAAK,WAAU;;wDACb,YAAY,UAAU,GAAG,MAAM;wDAAI;;;;;;;;;;;;;sDAGxC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,CAAC,qBAAqB,EAAE,YAAY,SAAS,GAAG,iBAAiB,cAAc;;;;;;8DAC/F,6LAAC;oDAAK,WAAU;;wDACb,YAAY,SAAS,GAAG,MAAM;wDAAI;;;;;;;;;;;;;sDAGvC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,CAAC,qBAAqB,EAAE,YAAY,WAAW,GAAG,iBAAiB,iBAAiB;;;;;;8DACpG,6LAAC;oDAAK,WAAU;;wDACb,YAAY,WAAW,GAAG,MAAM;wDAAI;;;;;;;;;;;;;;;;;;;;;;;;;sCAO7C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAe,WAAU;8CAAsB;;;;;;8CAG9D,6LAAC;oCACC,IAAG;oCACH,OAAO,SAAS,YAAY;oCAC5B,UAAU,CAAC;wCACT,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,cAAc,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;wCAC9D,IAAI,iBAAiB,YAAY,EAAE;4CACjC,oBAAoB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,cAAc;gDAAG,CAAC;wCAC5D;oCACF;oCACA,QAAQ;oCACR,WAAW,CAAC,oFAAoF,EAC9F,iBAAiB,YAAY,GAAG,mBAAmB,mBACnD;;sDAEF,6LAAC;4CAAO,OAAM;4CAAG,QAAQ;sDAAC;;;;;;sDAC1B,6LAAC;4CAAO,OAAM;sDAAW;;;;;;sDACzB,6LAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,6LAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,6LAAC;4CAAO,OAAM;sDAAc;;;;;;;;;;;;gCAE7B,iBAAiB,YAAY,kBAC5B,6LAAC;oCAAE,WAAU;;sDACX,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCACtB,iBAAiB,YAAY;;;;;;;;;;;;;sCAMpC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAqB,WAAU;8CAAsB;;;;;;8CAGpE,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,kBAAkB;oCAClC,UAAU,CAAC;wCACT,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,oBAAoB,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;wCACpE,IAAI,iBAAiB,kBAAkB,EAAE;4CACvC,oBAAoB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,oBAAoB;gDAAG,CAAC;wCAClE;oCACF;oCACA,QAAQ;oCACR,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oCAC3C,WAAW,iBAAiB,kBAAkB,GAAG,sCAAsC;;;;;;gCAExF,iBAAiB,kBAAkB,kBAClC,6LAAC;oCAAE,WAAU;;sDACX,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCACtB,iBAAiB,kBAAkB;;;;;;;;;;;;;sCAM1C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,6LAAC;oCACC,OAAO,SAAS,mBAAmB;oCACnC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,qBAAqB,EAAE,MAAM,CAAC,KAAK;4CAAQ,CAAC;oCAC7F,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAW;;;;;;sDACzB,6LAAC;4CAAO,OAAM;sDAAe;;;;;;sDAC7B,6LAAC;4CAAO,OAAM;sDAAwB;;;;;;;;;;;;;;;;;;sCAK1C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAO,WAAU;8CAAsB;;;;;;8CAGtD,6LAAC;oCACC,IAAG;oCACH,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACvE,aAAY;oCACZ,MAAM;oCACN,WAAU;;;;;;;;;;;;;;;;;;8BAKhB,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,aAAa,CAAC;4BACxB,WAAW,CAAC,OAAO,EAAE,CAAC,cAAc,8CAA8C,iCAAiC;sCAElH,0BACC;;kDACE,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;6DAInD;;kDACE,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AASjD;IAlPa;MAAA;AA2PN,MAAM,eAA4C,CAAC,EACxD,OAAO,EACP,OAAO,EACP,OAAO,EACR;;IACC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,SAAS;gBACX,MAAM,QAAQ;oDAAW;wBACvB;oBACF;mDAAG;gBACH;8CAAO,IAAM,aAAa;;YAC5B;QACF;iCAAG;QAAC;QAAS;KAAQ;IAErB,IAAI,CAAC,SAAS,OAAO;IAErB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;YAAC,WAAU;;8BACf,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,6LAAC,oIAAA,CAAA,mBAAgB;oBAAC,WAAU;8BACzB;;;;;;8BAEH,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS;8BAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKvB;IAlCa;MAAA", "debugId": null}}, {"offset": {"line": 7120, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/modals/BobinaManagementModals.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\nimport {\n  <PERSON>alog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport {\n  Search,\n  CheckCircle,\n  AlertTriangle,\n  Ruler,\n  Package,\n  X,\n  Loader2,\n  HelpCircle\n} from 'lucide-react'\nimport { Cavo } from '@/types'\nimport { parcoCaviApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\n// Enhanced Modal Components (imported from CableActionModals)\ninterface EnhancedModalHeaderProps {\n  icon: React.ReactNode\n  title: string\n  cableId: string\n  description?: string\n}\n\nconst EnhancedModalHeader: React.FC<EnhancedModalHeaderProps> = ({\n  icon,\n  title,\n  cableId,\n  description\n}) => (\n  <DialogHeader>\n    <DialogTitle className=\"flex items-center gap-2\">\n      {icon}\n      <span className=\"flex items-center gap-2\">\n        {title}\n        <span className=\"px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-sm font-mono font-semibold\">\n          {cableId}\n        </span>\n      </span>\n    </DialogTitle>\n    {description && (\n      <DialogDescription className=\"text-sm text-muted-foreground\">\n        {description}\n      </DialogDescription>\n    )}\n  </DialogHeader>\n)\n\ninterface EnhancedDialogContentProps {\n  children: React.ReactNode\n  className?: string\n  onKeyDown?: (e: React.KeyboardEvent) => void\n  ariaLabelledBy?: string\n  ariaDescribedBy?: string\n}\n\nconst EnhancedDialogContent: React.FC<EnhancedDialogContentProps> = ({\n  children,\n  className = \"sm:max-w-md\",\n  onKeyDown,\n  ariaLabelledBy,\n  ariaDescribedBy\n}) => (\n  <DialogContent \n    className={className}\n    onKeyDown={onKeyDown}\n    aria-labelledby={ariaLabelledBy}\n    aria-describedby={ariaDescribedBy}\n    onPointerDownOutside={(e) => e.preventDefault()}\n    onEscapeKeyDown={(e) => {\n      if (onKeyDown) {\n        onKeyDown(e as any)\n      }\n    }}\n  >\n    {children}\n  </DialogContent>\n)\n\n// Types\ninterface Bobina {\n  id_bobina: string\n  numero_bobina: string\n  tipologia: string\n  n_conduttori: string\n  sezione: string\n  metri_totali: number\n  metri_residui: number\n  stato_bobina: string\n  fornitore?: string\n  compatible?: boolean\n}\n\ninterface ModificaBobinaModalProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  onSave: (cavoId: string, bobinaId: string, option: string) => Promise<void>\n}\n\ninterface InserisciMetriModalProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  onSave: (cavoId: string, metriPosati: number) => Promise<void>\n}\n\n// Cable Info Card Component\ninterface CableInfoCardProps {\n  cavo: Cavo\n}\n\nconst CableInfoCard: React.FC<CableInfoCardProps> = ({ cavo }) => (\n  <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-3\">\n    <div className=\"flex items-center gap-2 mb-3\">\n      <Package className=\"h-5 w-5 text-blue-600\" />\n      <h3 className=\"font-semibold text-blue-800\">Informazioni Cavo {cavo.id_cavo}</h3>\n    </div>\n\n    <div className=\"grid grid-cols-2 gap-3 text-sm\">\n      <div className=\"flex flex-col\">\n        <span className=\"text-gray-600 text-xs font-medium uppercase tracking-wide\">Tipologia</span>\n        <span className=\"text-gray-900 font-medium\">{cavo.tipologia || 'N/A'}</span>\n      </div>\n\n      <div className=\"flex flex-col\">\n        <span className=\"text-gray-600 text-xs font-medium uppercase tracking-wide\">Formazione</span>\n        <span className=\"text-gray-900 font-medium\">{cavo.sezione || 'N/A'}</span>\n      </div>\n\n      <div className=\"flex flex-col\">\n        <span className=\"text-gray-600 text-xs font-medium uppercase tracking-wide\">Da</span>\n        <span className=\"text-gray-900 font-medium\">{cavo.ubicazione_partenza || 'N/A'}</span>\n      </div>\n\n      <div className=\"flex flex-col\">\n        <span className=\"text-gray-600 text-xs font-medium uppercase tracking-wide\">A</span>\n        <span className=\"text-gray-900 font-medium\">{cavo.ubicazione_arrivo || 'N/A'}</span>\n      </div>\n\n      <div className=\"flex flex-col col-span-2\">\n        <span className=\"text-gray-600 text-xs font-medium uppercase tracking-wide\">Metri Posati</span>\n        <span className=\"text-blue-600 font-bold text-lg\">{cavo.metratura_reale || 0} m</span>\n      </div>\n    </div>\n  </div>\n)\n\n// Modifica Bobina Modal\nexport const ModificaBobinaModal: React.FC<ModificaBobinaModalProps> = ({\n  open,\n  onClose,\n  cavo,\n  onSave\n}) => {\n  const { cantiere } = useAuth()\n  const [isLoading, setIsLoading] = useState(false)\n  const [loadingBobine, setLoadingBobine] = useState(false)\n  const [selectedOption, setSelectedOption] = useState('')\n  const [searchTerm, setSearchTerm] = useState('')\n  const [activeTab, setActiveTab] = useState<'compatible' | 'incompatible'>('compatible')\n  const [selectedBobina, setSelectedBobina] = useState<string>('')\n  const [bobine, setBobine] = useState<Bobina[]>([])\n  const [error, setError] = useState('')\n\n  // Carica bobine reali dall'API\n  const loadBobine = async () => {\n    console.log('🔄 ModificaBobinaModal: loadBobine chiamata', {\n      cantiere: cantiere?.id_cantiere,\n      cavo: cavo?.id_cavo,\n      open\n    })\n\n    if (!cantiere?.id_cantiere || !cavo) {\n      console.log('❌ ModificaBobinaModal: Mancano cantiere o cavo')\n      return\n    }\n\n    setLoadingBobine(true)\n    setError('')\n\n    try {\n      console.log('🔄 ModificaBobinaModal: Caricamento bobine per cantiere:', cantiere.id_cantiere)\n\n      // Usa la stessa logica di InserisciMetriDialog - SENZA parametri aggiuntivi\n      const response = await parcoCaviApi.getBobine(cantiere.id_cantiere)\n      console.log('📡 ModificaBobinaModal: Risposta API:', response)\n\n      if (response && Array.isArray(response)) {\n        // Filtra solo per stato (disponibile o in uso) e metri residui > 0 - STESSA LOGICA DI InserisciMetriDialog\n        const bobineUtilizzabili = response.filter((bobina: any) =>\n          bobina.stato_bobina !== 'Terminata' &&\n          bobina.stato_bobina !== 'Over' &&\n          bobina.metri_residui > 0\n        )\n\n        // Determina compatibilità basata su tipologia e sezione\n        const bobineWithCompatibility = bobineUtilizzabili.map((bobina: any) => ({\n          ...bobina,\n          compatible: bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione\n        }))\n\n        setBobine(bobineWithCompatibility)\n        console.log('✅ ModificaBobinaModal: Bobine caricate:', bobineWithCompatibility.length)\n        console.log('📋 ModificaBobinaModal: Dettaglio bobine:', bobineWithCompatibility.map(b => ({\n          id: b.id_bobina,\n          tipologia: b.tipologia,\n          sezione: b.sezione,\n          metri_residui: b.metri_residui,\n          stato: b.stato_bobina,\n          compatible: b.compatible\n        })))\n      } else {\n        setBobine([])\n        console.log('⚠️ ModificaBobinaModal: Nessuna bobina trovata')\n      }\n    } catch (error: any) {\n      console.error('Errore caricamento bobine:', error)\n      setError('Errore durante il caricamento delle bobine')\n    } finally {\n      setLoadingBobine(false)\n    }\n  }\n\n  // Carica bobine quando si apre il modal\n  useEffect(() => {\n    console.log('🔄 ModificaBobinaModal: useEffect triggered', {\n      open,\n      cavo: cavo?.id_cavo,\n      cantiere: cantiere?.id_cantiere,\n      cavoCompleto: cavo,\n      cantiereCompleto: cantiere\n    })\n\n    if (open && cavo && cantiere) {\n      console.log('✅ ModificaBobinaModal: Condizioni soddisfatte, chiamando loadBobine')\n      loadBobine()\n    } else {\n      console.log('❌ ModificaBobinaModal: Condizioni non soddisfatte', {\n        open: !!open,\n        cavo: !!cavo,\n        cantiere: !!cantiere,\n        'cavo.id_cavo': cavo?.id_cavo,\n        'cantiere.id_cantiere': cantiere?.id_cantiere\n      })\n    }\n  }, [open, cavo, cantiere])\n\n  const filteredBobine = bobine.filter(bobina => {\n    const matchesSearch = bobina.numero_bobina?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         bobina.tipologia?.toLowerCase().includes(searchTerm.toLowerCase())\n    const matchesTab = activeTab === 'compatible' ? bobina.compatible : !bobina.compatible\n    return matchesSearch && matchesTab\n  })\n\n  const compatibleCount = bobine.filter(b => b.compatible).length\n  const incompatibleCount = bobine.filter(b => !b.compatible).length\n\n  console.log('🔍 ModificaBobinaModal: Stato filtri', {\n    totaleBobine: bobine.length,\n    compatibili: compatibleCount,\n    incompatibili: incompatibleCount,\n    filtrate: filteredBobine.length,\n    activeTab,\n    searchTerm\n  })\n\n  const handleSave = async () => {\n    if (!cavo || !selectedOption) return\n\n    setIsLoading(true)\n    try {\n      await onSave(cavo.id_cavo, selectedBobina, selectedOption)\n      handleClose()\n    } catch (error) {\n      console.error('Error saving bobina modification:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      handleClose()\n    }\n  }\n\n  const handleClose = () => {\n    // Reset stati quando si chiude il modal\n    setSelectedOption('')\n    setSelectedBobina('')\n    setSearchTerm('')\n    setActiveTab('compatible')\n    setError('')\n    setBobine([])\n    onClose()\n  }\n\n  if (!cavo) return null\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <EnhancedDialogContent\n        className=\"sm:max-w-3xl max-h-[85vh] overflow-hidden\"\n        onKeyDown={handleKeyDown}\n        ariaLabelledBy=\"modifica-bobina-title\"\n      >\n        <EnhancedModalHeader\n          icon={<Package className=\"h-5 w-5 text-blue-500\" />}\n          title=\"Modifica Bobina Cavo\"\n          cableId={cavo.id_cavo}\n          description=\"Seleziona una nuova bobina per il cavo o modifica i parametri\"\n        />\n\n        <div className=\"space-y-4 py-4 overflow-y-auto max-h-[calc(85vh-200px)]\">\n          <CableInfoCard cavo={cavo} />\n\n          {/* Messaggio di errore */}\n          {error && (\n            <Alert className=\"bg-red-50 border-red-200\">\n              <AlertTriangle className=\"h-4 w-4 text-red-600\" />\n              <AlertDescription className=\"text-red-800\">\n                {error}\n              </AlertDescription>\n            </Alert>\n          )}\n\n          {/* Opzioni di modifica */}\n          <div className=\"space-y-3\">\n            <Label className=\"text-sm font-semibold\">Opzioni di modifica</Label>\n            <div className=\"grid grid-cols-1 gap-2\">\n              <label className=\"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors\">\n                <input\n                  type=\"radio\"\n                  name=\"modifica-option\"\n                  value=\"cambia-bobina\"\n                  checked={selectedOption === 'cambia-bobina'}\n                  onChange={(e) => setSelectedOption(e.target.value)}\n                  className=\"text-blue-600 focus:ring-blue-500\"\n                />\n                <div>\n                  <span className=\"text-sm font-medium\">Cambia bobina</span>\n                  <p className=\"text-xs text-gray-500\">Assegna una bobina diversa al cavo</p>\n                </div>\n              </label>\n              <label className=\"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors\">\n                <input\n                  type=\"radio\"\n                  name=\"modifica-option\"\n                  value=\"bobina-vuota\"\n                  checked={selectedOption === 'bobina-vuota'}\n                  onChange={(e) => setSelectedOption(e.target.value)}\n                  className=\"text-blue-600 focus:ring-blue-500\"\n                />\n                <div>\n                  <span className=\"text-sm font-medium\">Bobina vuota</span>\n                  <p className=\"text-xs text-gray-500\">Rimuovi l'associazione con la bobina attuale</p>\n                </div>\n              </label>\n              <label className=\"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors\">\n                <input\n                  type=\"radio\"\n                  name=\"modifica-option\"\n                  value=\"annulla-posa\"\n                  checked={selectedOption === 'annulla-posa'}\n                  onChange={(e) => setSelectedOption(e.target.value)}\n                  className=\"text-red-600 focus:ring-red-500\"\n                />\n                <div>\n                  <span className=\"text-sm font-medium text-red-700\">Annulla posa</span>\n                  <p className=\"text-xs text-red-500\">Annulla l'installazione e restituisci i metri alla bobina</p>\n                </div>\n              </label>\n            </div>\n          </div>\n\n          {selectedOption === 'cambia-bobina' && (\n            <>\n              {/* Campo di ricerca */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"search-bobina\" className=\"text-sm font-medium\">\n                  Cerca bobina\n                </Label>\n                <div className=\"relative\">\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                  <Input\n                    id=\"search-bobina\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    placeholder=\"Cerca bobina per ID, tipologia o numero...\"\n                    className=\"pl-10 h-8\"\n                  />\n                </div>\n              </div>\n\n              {/* Tabs per bobine compatibili/incompatibili */}\n              <div className=\"space-y-2\">\n                <div className=\"flex border-b\">\n                  <button\n                    onClick={() => setActiveTab('compatible')}\n                    className={`flex items-center gap-2 px-3 py-1.5 text-sm font-medium border-b-2 transition-colors ${\n                      activeTab === 'compatible'\n                        ? 'border-green-500 text-green-700 bg-green-50'\n                        : 'border-transparent text-gray-500 hover:text-gray-700'\n                    }`}\n                  >\n                    <CheckCircle className=\"h-3 w-3 text-green-500\" />\n                    Compatibili ({compatibleCount})\n                  </button>\n                  <button\n                    onClick={() => setActiveTab('incompatible')}\n                    className={`flex items-center gap-2 px-3 py-1.5 text-sm font-medium border-b-2 transition-colors ${\n                      activeTab === 'incompatible'\n                        ? 'border-yellow-500 text-yellow-700 bg-yellow-50'\n                        : 'border-transparent text-gray-500 hover:text-gray-700'\n                    }`}\n                  >\n                    <AlertTriangle className=\"h-3 w-3 text-yellow-500\" />\n                    Incompatibili ({incompatibleCount})\n                  </button>\n                </div>\n\n                {/* Lista bobine - Ridotta altezza per mostrare almeno 3 bobine */}\n                <div className=\"h-32 overflow-y-auto border rounded-md bg-gray-50\">\n                  {loadingBobine ? (\n                    <div className=\"p-3 text-center\">\n                      <Loader2 className=\"h-6 w-6 text-blue-500 mx-auto mb-2 animate-spin\" />\n                      <p className=\"text-xs text-gray-600\">Caricamento bobine...</p>\n                    </div>\n                  ) : filteredBobine.length === 0 ? (\n                    <div className=\"p-3 text-center\">\n                      <div className=\"bg-yellow-50 border border-yellow-200 rounded-md p-3\">\n                        <AlertTriangle className=\"h-6 w-6 text-yellow-500 mx-auto mb-1\" />\n                        <p className=\"text-xs text-yellow-800 font-medium mb-1\">\n                          Nessuna bobina {activeTab === 'compatible' ? 'compatibile' : 'incompatibile'} trovata\n                        </p>\n                        <p className=\"text-xs text-yellow-700\">\n                          Prova a modificare i criteri di ricerca\n                        </p>\n                      </div>\n                    </div>\n                  ) : (\n                    <div className=\"space-y-1 p-1\">\n                      {filteredBobine.map((bobina) => (\n                        <label\n                          key={bobina.id_bobina}\n                          className=\"flex items-center space-x-2 p-2 border rounded-md hover:bg-white cursor-pointer transition-colors bg-white\"\n                        >\n                          <input\n                            type=\"radio\"\n                            name=\"selected-bobina\"\n                            value={bobina.id_bobina}\n                            checked={selectedBobina === bobina.id_bobina}\n                            onChange={(e) => setSelectedBobina(e.target.value)}\n                            className=\"text-blue-600 focus:ring-blue-500 w-3 h-3\"\n                          />\n                          <div className=\"flex-1 min-w-0\">\n                            <div className=\"flex items-center gap-1\">\n                              <span className=\"font-medium text-xs truncate\">{bobina.numero_bobina}</span>\n                              {bobina.compatible ? (\n                                <CheckCircle className=\"h-3 w-3 text-green-500 flex-shrink-0\" />\n                              ) : (\n                                <AlertTriangle className=\"h-3 w-3 text-yellow-500 flex-shrink-0\" />\n                              )}\n                            </div>\n                            <p className=\"text-xs text-gray-500 truncate\">\n                              {bobina.tipologia} - {bobina.sezione} - {bobina.metri_residui}m\n                            </p>\n                          </div>\n                        </label>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              </div>\n            </>\n          )}\n\n          {/* Messaggio di conferma per annulla posa */}\n          {selectedOption === 'annulla-posa' && (\n            <Alert className=\"bg-red-50 border-red-200\">\n              <AlertTriangle className=\"h-4 w-4 text-red-600\" />\n              <AlertDescription className=\"text-red-800\">\n                <strong>ATTENZIONE:</strong> Questa operazione annullerà completamente l'installazione del cavo.\n                Tutti i metri posati saranno restituiti alla bobina originale e lo stato del cavo sarà resettato a \"Da installare\".\n              </AlertDescription>\n            </Alert>\n          )}\n        </div>\n\n        <DialogFooter className=\"gap-2 pt-4 border-t\">\n          <Button\n            variant=\"outline\"\n            onClick={handleClose}\n            disabled={isLoading}\n            className=\"px-6 py-2 hover:bg-gray-50\"\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSave}\n            disabled={isLoading || !selectedOption || (selectedOption === 'cambia-bobina' && !selectedBobina)}\n            className={`px-6 py-2 ${\n              selectedOption === 'annulla-posa'\n                ? 'bg-red-600 hover:bg-red-700 text-white'\n                : 'bg-blue-600 hover:bg-blue-700 text-white'\n            }`}\n            variant={selectedOption === 'annulla-posa' ? 'destructive' : 'default'}\n          >\n            {isLoading ? (\n              <>\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                Salvando...\n              </>\n            ) : selectedOption === 'annulla-posa' ? (\n              <>\n                <AlertTriangle className=\"mr-2 h-4 w-4\" />\n                Annulla Posa\n              </>\n            ) : (\n              'Salva Modifiche'\n            )}\n          </Button>\n        </DialogFooter>\n      </EnhancedDialogContent>\n    </Dialog>\n  )\n}\n\n// Inserisci Metri Posati Modal\nexport const InserisciMetriModal: React.FC<InserisciMetriModalProps> = ({\n  open,\n  onClose,\n  cavo,\n  onSave\n}) => {\n  const [isLoading, setIsLoading] = useState(false)\n  const [metriPosati, setMetriPosati] = useState('')\n  const [validationError, setValidationError] = useState('')\n\n  // Mock data for demonstration\n  const metriDaInstallare = 150\n  const metriGiaInstallati = 75\n\n  useEffect(() => {\n    if (open) {\n      setMetriPosati('')\n      setValidationError('')\n    }\n  }, [open])\n\n  const validateInput = (value: string) => {\n    const numValue = parseFloat(value)\n\n    if (!value.trim()) {\n      return 'Il campo metri posati è obbligatorio'\n    }\n\n    if (isNaN(numValue) || numValue <= 0) {\n      return 'Inserisci un valore numerico valido maggiore di 0'\n    }\n\n    if (numValue > metriDaInstallare) {\n      return `I metri posati non possono superare i metri da installare (${metriDaInstallare}m)`\n    }\n\n    return ''\n  }\n\n  const handleInputChange = (value: string) => {\n    setMetriPosati(value)\n    const error = validateInput(value)\n    setValidationError(error)\n  }\n\n  const handleSave = async () => {\n    if (!cavo) return\n\n    const error = validateInput(metriPosati)\n    if (error) {\n      setValidationError(error)\n      return\n    }\n\n    setIsLoading(true)\n    try {\n      await onSave(cavo.id_cavo, parseFloat(metriPosati))\n      onClose()\n    } catch (error) {\n      console.error('Error saving metri posati:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      onClose()\n    }\n    if (e.key === 'Enter' && !validationError && metriPosati.trim()) {\n      handleSave()\n    }\n  }\n\n  const isFormValid = metriPosati.trim() && !validationError\n\n  if (!cavo) return null\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <EnhancedDialogContent\n        className=\"sm:max-w-lg\"\n        onKeyDown={handleKeyDown}\n        ariaLabelledBy=\"inserisci-metri-title\"\n      >\n        <EnhancedModalHeader\n          icon={<Ruler className=\"h-5 w-5 text-green-500\" />}\n          title=\"Inserisci Metri Posati\"\n          cableId={cavo.id_cavo}\n          description=\"Registra i metri di cavo effettivamente posati\"\n        />\n\n        <div className=\"space-y-4 py-4\">\n          <CableInfoCard cavo={cavo} />\n\n          {/* Box informativi */}\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <div className=\"flex items-center gap-2 mb-2\">\n                <Ruler className=\"h-4 w-4 text-blue-600\" />\n                <span className=\"text-sm font-semibold text-blue-900\">Metri da Installare</span>\n              </div>\n              <p className=\"text-2xl font-bold text-blue-700\">{metriDaInstallare}m</p>\n              <p className=\"text-xs text-blue-600\">Lunghezza totale prevista</p>\n            </div>\n\n            <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n              <div className=\"flex items-center gap-2 mb-2\">\n                <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                <span className=\"text-sm font-semibold text-green-900\">Già Installati</span>\n              </div>\n              <p className=\"text-2xl font-bold text-green-700\">{metriGiaInstallati}m</p>\n              <p className=\"text-xs text-green-600\">Precedentemente registrati</p>\n            </div>\n          </div>\n\n          {/* Campo input metri posati */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"metri-posati\" className=\"text-sm font-medium flex items-center gap-2\">\n              <Ruler className=\"h-4 w-4 text-gray-600\" />\n              Metri Posati *\n            </Label>\n            <div className=\"relative\">\n              <Input\n                id=\"metri-posati\"\n                type=\"number\"\n                value={metriPosati}\n                onChange={(e) => handleInputChange(e.target.value)}\n                placeholder=\"Inserisci i metri posati...\"\n                min=\"0\"\n                max={metriDaInstallare}\n                step=\"0.1\"\n                className={`pr-12 ${validationError ? 'border-red-500 focus:ring-red-500' : 'focus:ring-green-500'}`}\n              />\n              <span className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-gray-500\">\n                m\n              </span>\n            </div>\n            {validationError && (\n              <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                <AlertTriangle className=\"h-3 w-3\" />\n                {validationError}\n              </p>\n            )}\n            <p className=\"text-xs text-gray-500\">\n              Rimanenti da installare: {Math.max(0, metriDaInstallare - metriGiaInstallati - (parseFloat(metriPosati) || 0))}m\n            </p>\n          </div>\n\n          {/* Informazioni aggiuntive */}\n          <Alert className=\"bg-blue-50 border-blue-200\">\n            <HelpCircle className=\"h-4 w-4 text-blue-600\" />\n            <AlertDescription className=\"text-blue-800\">\n              <strong>Suggerimento:</strong> Assicurati di misurare accuratamente i metri posati.\n              Questo valore verrà utilizzato per calcolare il progresso dell'installazione.\n            </AlertDescription>\n          </Alert>\n        </div>\n\n        <DialogFooter className=\"gap-2\">\n          <Button\n            variant=\"outline\"\n            onClick={onClose}\n            disabled={isLoading}\n            className=\"flex-1 hover:bg-gray-50\"\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSave}\n            disabled={isLoading || !isFormValid}\n            className={`flex-1 ${!isFormValid ? 'opacity-50 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700'}`}\n          >\n            {isLoading ? (\n              <>\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                Salvando...\n              </>\n            ) : (\n              <>\n                <CheckCircle className=\"mr-2 h-4 w-4\" />\n                Salva Metri\n              </>\n            )}\n          </Button>\n        </DialogFooter>\n      </EnhancedDialogContent>\n    </Dialog>\n  )\n}\n\n// Export types\nexport type {\n  ModificaBobinaModalProps,\n  InserisciMetriModalProps,\n  Bobina\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;;;AA3BA;;;;;;;;;;AAqCA,MAAM,sBAA0D,CAAC,EAC/D,IAAI,EACJ,KAAK,EACL,OAAO,EACP,WAAW,EACZ,iBACC,6LAAC,qIAAA,CAAA,eAAY;;0BACX,6LAAC,qIAAA,CAAA,cAAW;gBAAC,WAAU;;oBACpB;kCACD,6LAAC;wBAAK,WAAU;;4BACb;0CACD,6LAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;;;;;;;YAIN,6BACC,6LAAC,qIAAA,CAAA,oBAAiB;gBAAC,WAAU;0BAC1B;;;;;;;;;;;;KAlBH;AAgCN,MAAM,wBAA8D,CAAC,EACnE,QAAQ,EACR,YAAY,aAAa,EACzB,SAAS,EACT,cAAc,EACd,eAAe,EAChB,iBACC,6LAAC,qIAAA,CAAA,gBAAa;QACZ,WAAW;QACX,WAAW;QACX,mBAAiB;QACjB,oBAAkB;QAClB,sBAAsB,CAAC,IAAM,EAAE,cAAc;QAC7C,iBAAiB,CAAC;YAChB,IAAI,WAAW;gBACb,UAAU;YACZ;QACF;kBAEC;;;;;;MAnBC;AAwDN,MAAM,gBAA8C,CAAC,EAAE,IAAI,EAAE,iBAC3D,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,2MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAG,WAAU;;4BAA8B;4BAAmB,KAAK,OAAO;;;;;;;;;;;;;0BAG7E,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAA4D;;;;;;0CAC5E,6LAAC;gCAAK,WAAU;0CAA6B,KAAK,SAAS,IAAI;;;;;;;;;;;;kCAGjE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAA4D;;;;;;0CAC5E,6LAAC;gCAAK,WAAU;0CAA6B,KAAK,OAAO,IAAI;;;;;;;;;;;;kCAG/D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAA4D;;;;;;0CAC5E,6LAAC;gCAAK,WAAU;0CAA6B,KAAK,mBAAmB,IAAI;;;;;;;;;;;;kCAG3E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAA4D;;;;;;0CAC5E,6LAAC;gCAAK,WAAU;0CAA6B,KAAK,iBAAiB,IAAI;;;;;;;;;;;;kCAGzE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAA4D;;;;;;0CAC5E,6LAAC;gCAAK,WAAU;;oCAAmC,KAAK,eAAe,IAAI;oCAAE;;;;;;;;;;;;;;;;;;;;;;;;;MA9B/E;AAqCC,MAAM,sBAA0D,CAAC,EACtE,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,MAAM,EACP;;IACC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiC;IAC1E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,+BAA+B;IAC/B,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC,+CAA+C;YACzD,UAAU,UAAU;YACpB,MAAM,MAAM;YACZ;QACF;QAEA,IAAI,CAAC,UAAU,eAAe,CAAC,MAAM;YACnC,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,iBAAiB;QACjB,SAAS;QAET,IAAI;YACF,QAAQ,GAAG,CAAC,4DAA4D,SAAS,WAAW;YAE5F,4EAA4E;YAC5E,MAAM,WAAW,MAAM,oHAAA,CAAA,eAAY,CAAC,SAAS,CAAC,SAAS,WAAW;YAClE,QAAQ,GAAG,CAAC,yCAAyC;YAErD,IAAI,YAAY,MAAM,OAAO,CAAC,WAAW;gBACvC,2GAA2G;gBAC3G,MAAM,qBAAqB,SAAS,MAAM,CAAC,CAAC,SAC1C,OAAO,YAAY,KAAK,eACxB,OAAO,YAAY,KAAK,UACxB,OAAO,aAAa,GAAG;gBAGzB,wDAAwD;gBACxD,MAAM,0BAA0B,mBAAmB,GAAG,CAAC,CAAC,SAAgB,CAAC;wBACvE,GAAG,MAAM;wBACT,YAAY,OAAO,SAAS,KAAK,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,KAAK,OAAO;oBACpF,CAAC;gBAED,UAAU;gBACV,QAAQ,GAAG,CAAC,2CAA2C,wBAAwB,MAAM;gBACrF,QAAQ,GAAG,CAAC,6CAA6C,wBAAwB,GAAG,CAAC,CAAA,IAAK,CAAC;wBACzF,IAAI,EAAE,SAAS;wBACf,WAAW,EAAE,SAAS;wBACtB,SAAS,EAAE,OAAO;wBAClB,eAAe,EAAE,aAAa;wBAC9B,OAAO,EAAE,YAAY;wBACrB,YAAY,EAAE,UAAU;oBAC1B,CAAC;YACH,OAAO;gBACL,UAAU,EAAE;gBACZ,QAAQ,GAAG,CAAC;YACd;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,SAAS;QACX,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,QAAQ,GAAG,CAAC,+CAA+C;gBACzD;gBACA,MAAM,MAAM;gBACZ,UAAU,UAAU;gBACpB,cAAc;gBACd,kBAAkB;YACpB;YAEA,IAAI,QAAQ,QAAQ,UAAU;gBAC5B,QAAQ,GAAG,CAAC;gBACZ;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC,qDAAqD;oBAC/D,MAAM,CAAC,CAAC;oBACR,MAAM,CAAC,CAAC;oBACR,UAAU,CAAC,CAAC;oBACZ,gBAAgB,MAAM;oBACtB,wBAAwB,UAAU;gBACpC;YACF;QACF;wCAAG;QAAC;QAAM;QAAM;KAAS;IAEzB,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA;QACnC,MAAM,gBAAgB,OAAO,aAAa,EAAE,cAAc,SAAS,WAAW,WAAW,OACpE,OAAO,SAAS,EAAE,cAAc,SAAS,WAAW,WAAW;QACpF,MAAM,aAAa,cAAc,eAAe,OAAO,UAAU,GAAG,CAAC,OAAO,UAAU;QACtF,OAAO,iBAAiB;IAC1B;IAEA,MAAM,kBAAkB,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,MAAM;IAC/D,MAAM,oBAAoB,OAAO,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,UAAU,EAAE,MAAM;IAElE,QAAQ,GAAG,CAAC,wCAAwC;QAClD,cAAc,OAAO,MAAM;QAC3B,aAAa;QACb,eAAe;QACf,UAAU,eAAe,MAAM;QAC/B;QACA;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,QAAQ,CAAC,gBAAgB;QAE9B,aAAa;QACb,IAAI;YACF,MAAM,OAAO,KAAK,OAAO,EAAE,gBAAgB;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF;IACF;IAEA,MAAM,cAAc;QAClB,wCAAwC;QACxC,kBAAkB;QAClB,kBAAkB;QAClB,cAAc;QACd,aAAa;QACb,SAAS;QACT,UAAU,EAAE;QACZ;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC;YACC,WAAU;YACV,WAAW;YACX,gBAAe;;8BAEf,6LAAC;oBACC,oBAAM,6LAAC,2MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBACzB,OAAM;oBACN,SAAS,KAAK,OAAO;oBACrB,aAAY;;;;;;8BAGd,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAc,MAAM;;;;;;wBAGpB,uBACC,6LAAC,oIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,6LAAC,oIAAA,CAAA,mBAAgB;oCAAC,WAAU;8CACzB;;;;;;;;;;;;sCAMP,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAwB;;;;;;8CACzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAM;oDACN,SAAS,mBAAmB;oDAC5B,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oDACjD,WAAU;;;;;;8DAEZ,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAsB;;;;;;sEACtC,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAGzC,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAM;oDACN,SAAS,mBAAmB;oDAC5B,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oDACjD,WAAU;;;;;;8DAEZ,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAsB;;;;;;sEACtC,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAGzC,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAM;oDACN,SAAS,mBAAmB;oDAC5B,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oDACjD,WAAU;;;;;;8DAEZ,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAmC;;;;;;sEACnD,6LAAC;4DAAE,WAAU;sEAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAM3C,mBAAmB,iCAClB;;8CAEE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAgB,WAAU;sDAAsB;;;;;;sDAG/D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,aAAY;oDACZ,WAAU;;;;;;;;;;;;;;;;;;8CAMhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS,IAAM,aAAa;oDAC5B,WAAW,CAAC,qFAAqF,EAC/F,cAAc,eACV,gDACA,wDACJ;;sEAEF,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDAA2B;wDACpC;wDAAgB;;;;;;;8DAEhC,6LAAC;oDACC,SAAS,IAAM,aAAa;oDAC5B,WAAW,CAAC,qFAAqF,EAC/F,cAAc,iBACV,mDACA,wDACJ;;sEAEF,6LAAC,2NAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDAA4B;wDACrC;wDAAkB;;;;;;;;;;;;;sDAKtC,6LAAC;4CAAI,WAAU;sDACZ,8BACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;uDAErC,eAAe,MAAM,KAAK,kBAC5B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,2NAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;sEACzB,6LAAC;4DAAE,WAAU;;gEAA2C;gEACtC,cAAc,eAAe,gBAAgB;gEAAgB;;;;;;;sEAE/E,6LAAC;4DAAE,WAAU;sEAA0B;;;;;;;;;;;;;;;;qEAM3C,6LAAC;gDAAI,WAAU;0DACZ,eAAe,GAAG,CAAC,CAAC,uBACnB,6LAAC;wDAEC,WAAU;;0EAEV,6LAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,OAAO,SAAS;gEACvB,SAAS,mBAAmB,OAAO,SAAS;gEAC5C,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gEACjD,WAAU;;;;;;0EAEZ,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAAgC,OAAO,aAAa;;;;;;4EACnE,OAAO,UAAU,iBAChB,6LAAC,8NAAA,CAAA,cAAW;gFAAC,WAAU;;;;;qGAEvB,6LAAC,2NAAA,CAAA,gBAAa;gFAAC,WAAU;;;;;;;;;;;;kFAG7B,6LAAC;wEAAE,WAAU;;4EACV,OAAO,SAAS;4EAAC;4EAAI,OAAO,OAAO;4EAAC;4EAAI,OAAO,aAAa;4EAAC;;;;;;;;;;;;;;uDArB7D,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;wBAkCpC,mBAAmB,gCAClB,6LAAC,oIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,6LAAC,oIAAA,CAAA,mBAAgB;oCAAC,WAAU;;sDAC1B,6LAAC;sDAAO;;;;;;wCAAoB;;;;;;;;;;;;;;;;;;;8BAOpC,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,aAAa,CAAC,kBAAmB,mBAAmB,mBAAmB,CAAC;4BAClF,WAAW,CAAC,UAAU,EACpB,mBAAmB,iBACf,2CACA,4CACJ;4BACF,SAAS,mBAAmB,iBAAiB,gBAAgB;sCAE5D,0BACC;;kDACE,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;+CAGjD,mBAAmB,+BACrB;;kDACE,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAiB;;+CAI5C;;;;;;;;;;;;;;;;;;;;;;;AAOd;GA1Xa;;QAMU,kIAAA,CAAA,UAAO;;;MANjB;AA6XN,MAAM,sBAA0D,CAAC,EACtE,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,MAAM,EACP;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,8BAA8B;IAC9B,MAAM,oBAAoB;IAC1B,MAAM,qBAAqB;IAE3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,MAAM;gBACR,eAAe;gBACf,mBAAmB;YACrB;QACF;wCAAG;QAAC;KAAK;IAET,MAAM,gBAAgB,CAAC;QACrB,MAAM,WAAW,WAAW;QAE5B,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,OAAO;QACT;QAEA,IAAI,MAAM,aAAa,YAAY,GAAG;YACpC,OAAO;QACT;QAEA,IAAI,WAAW,mBAAmB;YAChC,OAAO,CAAC,2DAA2D,EAAE,kBAAkB,EAAE,CAAC;QAC5F;QAEA,OAAO;IACT;IAEA,MAAM,oBAAoB,CAAC;QACzB,eAAe;QACf,MAAM,QAAQ,cAAc;QAC5B,mBAAmB;IACrB;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,MAAM;QAEX,MAAM,QAAQ,cAAc;QAC5B,IAAI,OAAO;YACT,mBAAmB;YACnB;QACF;QAEA,aAAa;QACb,IAAI;YACF,MAAM,OAAO,KAAK,OAAO,EAAE,WAAW;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF;QACA,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,mBAAmB,YAAY,IAAI,IAAI;YAC/D;QACF;IACF;IAEA,MAAM,cAAc,YAAY,IAAI,MAAM,CAAC;IAE3C,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC;YACC,WAAU;YACV,WAAW;YACX,gBAAe;;8BAEf,6LAAC;oBACC,oBAAM,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;oBACvB,OAAM;oBACN,SAAS,KAAK,OAAO;oBACrB,aAAY;;;;;;8BAGd,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAc,MAAM;;;;;;sCAGrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAK,WAAU;8DAAsC;;;;;;;;;;;;sDAExD,6LAAC;4CAAE,WAAU;;gDAAoC;gDAAkB;;;;;;;sDACnE,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,6LAAC;oDAAK,WAAU;8DAAuC;;;;;;;;;;;;sDAEzD,6LAAC;4CAAE,WAAU;;gDAAqC;gDAAmB;;;;;;;sDACrE,6LAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;sCAK1C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAe,WAAU;;sDACtC,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAA0B;;;;;;;8CAG7C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4CACjD,aAAY;4CACZ,KAAI;4CACJ,KAAK;4CACL,MAAK;4CACL,WAAW,CAAC,MAAM,EAAE,kBAAkB,sCAAsC,wBAAwB;;;;;;sDAEtG,6LAAC;4CAAK,WAAU;sDAA4E;;;;;;;;;;;;gCAI7F,iCACC,6LAAC;oCAAE,WAAU;;sDACX,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCACxB;;;;;;;8CAGL,6LAAC;oCAAE,WAAU;;wCAAwB;wCACT,KAAK,GAAG,CAAC,GAAG,oBAAoB,qBAAqB,CAAC,WAAW,gBAAgB,CAAC;wCAAG;;;;;;;;;;;;;sCAKnH,6LAAC,oIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,6LAAC,oIAAA,CAAA,mBAAgB;oCAAC,WAAU;;sDAC1B,6LAAC;sDAAO;;;;;;wCAAsB;;;;;;;;;;;;;;;;;;;8BAMpC,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,aAAa,CAAC;4BACxB,WAAW,CAAC,OAAO,EAAE,CAAC,cAAc,kCAAkC,mCAAmC;sCAExG,0BACC;;kDACE,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;6DAInD;;kDACE,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AASxD;IA7La;MAAA", "debugId": null}}, {"offset": {"line": 8526, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/ModificaBobinaDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>ontent,\n  <PERSON><PERSON><PERSON>ooter,\n  <PERSON><PERSON>Header,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Badge } from '@/components/ui/badge'\nimport { Loader2, AlertCircle, Package, Search, CheckCircle, AlertTriangle } from 'lucide-react'\nimport { Cavo, Bobina } from '@/types'\nimport { parcoCaviApi, caviApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface ModificaBobinaDialogProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  cantiere?: { id_cantiere: string; nome_cantiere: string } | null\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function ModificaBobinaD<PERSON>og({\n  open,\n  onClose,\n  cavo,\n  cantiere: cantiereProp,\n  onSuccess,\n  onError\n}: ModificaBobinaDialogProps) {\n  const { cantiere: cantiereFromContext } = useAuth()\n  const cantiere = cantiereProp || cantiereFromContext\n\n  const [selectedOption, setSelectedOption] = useState<string>('assegna_nuova')\n  const [selectedBobina, setSelectedBobina] = useState<string>('')\n  const [bobine, setBobine] = useState<Bobina[]>([])\n  const [loadingBobine, setLoadingBobine] = useState(false)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState<string>('')\n  const [searchText, setSearchText] = useState('')\n  const [activeTab, setActiveTab] = useState<'compatibili' | 'incompatibili'>('compatibili')\n\n  // Reset form quando il dialog si apre/chiude\n  useEffect(() => {\n    if (open) {\n      setSelectedOption('assegna_nuova')\n      setSelectedBobina('')\n      setSearchText('')\n      setActiveTab('compatibili')\n      setError('')\n      if (cantiere?.id_cantiere) {\n        loadBobine()\n      }\n    }\n  }, [open, cantiere?.id_cantiere])\n\n  const loadBobine = async () => {\n    if (!cantiere?.id_cantiere) {\n      setError('Cantiere non disponibile')\n      return\n    }\n\n    try {\n      setLoadingBobine(true)\n      setError('')\n\n      console.log('🔄 ModificaBobinaDialog: Caricamento bobine per cantiere:', cantiere.id_cantiere)\n\n      // Usa la stessa logica di InserisciMetriDialog\n      const response = await parcoCaviApi.getBobine(cantiere.id_cantiere)\n\n      if (response && Array.isArray(response)) {\n        // Filtra solo per stato (disponibile o in uso) e metri residui > 0 - STESSA LOGICA DI InserisciMetriDialog\n        const bobineUtilizzabili = response.filter((bobina) =>\n          bobina.stato_bobina !== 'Terminata' &&\n          bobina.stato_bobina !== 'Over' &&\n          bobina.metri_residui > 0\n        )\n\n        setBobine(bobineUtilizzabili)\n        console.log('✅ ModificaBobinaDialog: Bobine caricate:', bobineUtilizzabili.length)\n        console.log('📋 ModificaBobinaDialog: Dettaglio bobine:', bobineUtilizzabili.map(b => ({\n          id: b.id_bobina,\n          tipologia: b.tipologia,\n          sezione: b.sezione,\n          metri_residui: b.metri_residui,\n          stato: b.stato_bobina\n        })))\n      } else {\n        setBobine([])\n        console.log('⚠️ ModificaBobinaDialog: Nessuna bobina trovata')\n      }\n    } catch (error) {\n      console.error('❌ ModificaBobinaDialog: Errore caricamento bobine:', error)\n      setError('Errore nel caricamento delle bobine')\n      setBobine([])\n    } finally {\n      setLoadingBobine(false)\n    }\n  }\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = (idBobina: string) => {\n    if (!idBobina || idBobina === 'BOBINA_VUOTA') return 'VUOTA'\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1]\n    }\n    const bobina = bobine.find(b => b.id_bobina === idBobina)\n    return bobina ? bobina.numero_bobina || idBobina : idBobina\n  }\n\n  // Filtra le bobine compatibili\n  const getBobineCompatibili = () => {\n    if (!cavo) return []\n\n    const compatibili = bobine.filter(bobina => {\n      const isCompatible = bobina.tipologia === cavo.tipologia &&\n                          bobina.sezione === cavo.sezione\n      const matchesSearch = searchText === '' ||\n                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) ||\n                           (bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase())) ||\n                           (bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase()))\n      return isCompatible && matchesSearch && bobina.metri_residui > 0\n    })\n\n    console.log('🔍 ModificaBobinaDialog: Filtro compatibili:', {\n      cavoTipologia: cavo.tipologia,\n      cavoSezione: cavo.sezione,\n      totaleBobine: bobine.length,\n      bobineCompatibili: compatibili.length,\n      searchText\n    })\n\n    return compatibili\n  }\n\n  // Filtra le bobine incompatibili\n  const getBobineIncompatibili = () => {\n    if (!cavo) return []\n\n    return bobine.filter(bobina => {\n      const isIncompatible = bobina.tipologia !== cavo.tipologia ||\n                            bobina.sezione !== cavo.sezione\n      const matchesSearch = searchText === '' ||\n                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) ||\n                           (bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase())) ||\n                           (bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase()))\n      return isIncompatible && matchesSearch && bobina.metri_residui > 0\n    })\n  }\n\n  const bobineCompatibili = getBobineCompatibili()\n  const bobineIncompatibili = getBobineIncompatibili()\n\n  const handleClose = () => {\n    setSelectedOption('assegna_nuova')\n    setSelectedBobina('')\n    setSearchText('')\n    setError('')\n    onClose()\n  }\n\n  const handleSave = async () => {\n    console.log('🔄 ModificaBobinaDialog: Salvataggio:', {\n      selectedOption,\n      selectedBobina,\n      cavoId: cavo?.id_cavo,\n      cantiereId: cantiere?.id_cantiere\n    })\n\n    if (!cavo) {\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n\n      if (selectedOption === 'assegna_nuova') {\n        if (!selectedBobina) {\n          onError('Selezionare una bobina')\n          return\n        }\n\n        // Usa updateMetriPosati come in InserisciMetriDialog\n        const result = await caviApi.updateMetriPosati({\n          id_cavo: cavo.id_cavo,\n          metri_posati: cavo.metratura_reale || 0, // Mantieni i metri attuali\n          id_bobina: selectedBobina,\n          force_over: true // Permetti operazioni incompatibili\n        })\n\n        if (result.success) {\n          onSuccess(`Bobina aggiornata con successo per il cavo ${cavo.id_cavo}`)\n          handleClose()\n        } else {\n          onError(result.message || 'Errore durante l\\'aggiornamento della bobina')\n        }\n      } else if (selectedOption === 'rimuovi_bobina') {\n        // Assegna BOBINA_VUOTA\n        const result = await caviApi.updateMetriPosati({\n          id_cavo: cavo.id_cavo,\n          metri_posati: cavo.metratura_reale || 0,\n          id_bobina: 'BOBINA_VUOTA',\n          force_over: false\n        })\n\n        if (result.success) {\n          onSuccess(`Bobina rimossa dal cavo ${cavo.id_cavo}`)\n          handleClose()\n        } else {\n          onError(result.message || 'Errore durante la rimozione della bobina')\n        }\n      } else if (selectedOption === 'annulla_installazione') {\n        // Reset completo del cavo\n        const result = await caviApi.updateMetriPosati({\n          id_cavo: cavo.id_cavo,\n          metri_posati: 0,\n          id_bobina: 'BOBINA_VUOTA',\n          force_over: false\n        })\n\n        if (result.success) {\n          onSuccess(`Installazione annullata per il cavo ${cavo.id_cavo}`)\n          handleClose()\n        } else {\n          onError(result.message || 'Errore durante l\\'annullamento dell\\'installazione')\n        }\n      }\n    } catch (error) {\n      console.error('❌ ModificaBobinaDialog: Errore salvataggio:', error)\n      onError('Errore durante il salvataggio')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (!cavo) return null\n\n  return (\n    <>\n      <Dialog open={open} onOpenChange={handleClose}>\n        <DialogContent className=\"max-w-4xl max-h-[90vh] flex flex-col\">\n          <DialogHeader>\n            <DialogTitle>\n              Modifica Bobina Cavo {cavo.id_cavo}\n            </DialogTitle>\n          </DialogHeader>\n\n          <div className=\"flex-1 overflow-hidden space-y-6\">\n            {/* Sezione Cavo Selezionato */}\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center space-x-2\">\n                <Package className=\"h-5 w-5 text-blue-600\" />\n                <h3 className=\"font-medium text-gray-900\">Cavo Selezionato</h3>\n              </div>\n\n              {/* Informazioni Cavo - Formato esatto come richiesto dall'utente */}\n              <div className=\"p-4 bg-blue-50 rounded-lg border-2 border-blue-200\">\n                <div className=\"text-sm font-medium text-blue-800\">\n                  Informazioni Cavo / Tipologia: {cavo.tipologia || 'N/A'} / Da: {cavo.ubicazione_partenza || 'N/A'} / Formazione: {cavo.sezione || 'N/A'} / A: {cavo.ubicazione_arrivo || 'N/A'} / Metri Posati: {cavo.metratura_reale || 0} m\n                </div>\n              </div>\n            </div>\n\n            {/* Sezione Opzioni di modifica */}\n            <div className=\"space-y-3\">\n              <h3 className=\"font-medium\">Opzioni di modifica</h3>\n\n              <div className=\"space-y-2\">\n                <label className=\"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50\">\n                  <input\n                    type=\"radio\"\n                    name=\"operazione\"\n                    value=\"assegna_nuova\"\n                    checked={selectedOption === 'assegna_nuova'}\n                    onChange={(e) => setSelectedOption(e.target.value)}\n                    className=\"w-4 h-4 text-blue-600\"\n                  />\n                  <span className=\"text-sm\">Cambia bobina</span>\n                </label>\n\n                <label className=\"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50\">\n                  <input\n                    type=\"radio\"\n                    name=\"operazione\"\n                    value=\"rimuovi_bobina\"\n                    checked={selectedOption === 'rimuovi_bobina'}\n                    onChange={(e) => setSelectedOption(e.target.value)}\n                    className=\"w-4 h-4 text-blue-600\"\n                  />\n                  <span className=\"text-sm\">Bobina vuota</span>\n                </label>\n\n                <label className=\"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50\">\n                  <input\n                    type=\"radio\"\n                    name=\"operazione\"\n                    value=\"annulla_installazione\"\n                    checked={selectedOption === 'annulla_installazione'}\n                    onChange={(e) => setSelectedOption(e.target.value)}\n                    className=\"w-4 h-4 text-blue-600\"\n                  />\n                  <span className=\"text-sm text-red-600\">Annulla posa</span>\n                </label>\n              </div>\n            </div>\n\n            {/* Sezione Seleziona bobina - solo se \"Assegna nuova bobina\" è selezionato */}\n            {selectedOption === 'assegna_nuova' && (\n              <div className=\"space-y-3\">\n                <h3 className=\"font-medium\">Seleziona bobina</h3>\n\n                {/* Campo ricerca */}\n                <div className=\"relative\">\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n                  <Input\n                    placeholder=\"Cerca bobina per ID, tipologia o numero...\"\n                    value={searchText}\n                    onChange={(e) => setSearchText(e.target.value)}\n                    className=\"pl-10\"\n                  />\n                </div>\n\n                {/* Tab per bobine compatibili/incompatibili */}\n                <div className=\"flex space-x-1 border-b\">\n                  <button\n                    onClick={() => setActiveTab('compatibili')}\n                    className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${\n                      activeTab === 'compatibili'\n                        ? 'border-green-500 text-green-600 bg-green-50'\n                        : 'border-transparent text-gray-500 hover:text-gray-700'\n                    }`}\n                  >\n                    <div className=\"flex items-center space-x-2\">\n                      <CheckCircle className=\"h-4 w-4\" />\n                      <span>Bobine Compatibili ({bobineCompatibili.length})</span>\n                    </div>\n                  </button>\n                  <button\n                    onClick={() => setActiveTab('incompatibili')}\n                    className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${\n                      activeTab === 'incompatibili'\n                        ? 'border-orange-500 text-orange-600 bg-orange-50'\n                        : 'border-transparent text-gray-500 hover:text-gray-700'\n                    }`}\n                  >\n                    <div className=\"flex items-center space-x-2\">\n                      <AlertCircle className=\"h-4 w-4\" />\n                      <span>Bobine Incompatibili ({bobineIncompatibili.length})</span>\n                    </div>\n                  </button>\n                </div>\n\n                {/* Lista bobine */}\n                <div className=\"border rounded-lg h-64 overflow-y-auto\">\n                  {loadingBobine ? (\n                    <div className=\"flex items-center justify-center h-full\">\n                      <div className=\"flex items-center space-x-2\">\n                        <Loader2 className=\"h-4 w-4 animate-spin\" />\n                        <span className=\"text-sm text-gray-600\">Caricamento bobine...</span>\n                      </div>\n                    </div>\n                  ) : (\n                    <div className=\"p-2\">\n                      {activeTab === 'compatibili' ? (\n                        bobineCompatibili.length === 0 ? (\n                          <div className=\"text-center py-8\">\n                            <div className=\"text-gray-500 text-sm mb-2\">\n                              Nessuna bobina compatibile trovata\n                            </div>\n                            <div className=\"text-xs text-gray-400\">\n                              Cercando bobine con tipologia <strong>{cavo.tipologia}</strong> e formazione <strong>{cavo.sezione}</strong>\n                            </div>\n                          </div>\n                        ) : (\n                          <div className=\"space-y-2\">\n                            {bobineCompatibili.map((bobina) => (\n                              <div\n                                key={bobina.id_bobina}\n                                onClick={() => setSelectedBobina(bobina.id_bobina)}\n                                className={`p-3 rounded-lg cursor-pointer transition-all duration-200 ${\n                                  selectedBobina === bobina.id_bobina\n                                    ? 'bg-blue-100 border-2 border-blue-300 shadow-md'\n                                    : 'hover:bg-gray-50 border border-gray-200 hover:border-gray-300'\n                                }`}\n                              >\n                                <div className=\"flex justify-between items-start\">\n                                  <div className=\"flex-1\">\n                                    <div className=\"flex items-center space-x-2 mb-1\">\n                                      <div className=\"font-medium text-sm text-gray-900\">{bobina.id_bobina}</div>\n                                      {bobina.stato_bobina && (\n                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                                          bobina.stato_bobina === 'Disponibile' ? 'bg-green-100 text-green-800' :\n                                          bobina.stato_bobina === 'In uso' ? 'bg-blue-100 text-blue-800' :\n                                          'bg-yellow-100 text-yellow-800'\n                                        }`}>\n                                          {bobina.stato_bobina}\n                                        </span>\n                                      )}\n                                    </div>\n                                    <div className=\"text-xs text-gray-500 mb-1\">\n                                      <span className=\"font-medium\">{bobina.tipologia}</span> • <span>{bobina.sezione}</span>\n                                    </div>\n                                  </div>\n                                  <div className=\"text-right ml-3\">\n                                    <div className={`text-sm font-medium ${\n                                      bobina.metri_residui > 0 ? 'text-green-600' : 'text-gray-500'\n                                    }`}>\n                                      {bobina.metri_residui}m\n                                    </div>\n                                    <div className=\"text-xs text-gray-400\">\n                                      {bobina.metri_residui > 0 ? 'disponibili' : 'esaurita'}\n                                    </div>\n                                  </div>\n                                </div>\n                              </div>\n                            ))}\n                          </div>\n                        )\n                      ) : (\n                        bobineIncompatibili.length === 0 ? (\n                          <div className=\"text-center py-8 text-gray-500 text-sm\">\n                            Nessuna bobina incompatibile trovata\n                          </div>\n                        ) : (\n                          <div className=\"space-y-2\">\n                            {/* Avviso per bobine incompatibili */}\n                            <div className=\"p-3 bg-orange-50 border border-orange-200 rounded-lg\">\n                              <div className=\"flex items-start space-x-2\">\n                                <AlertCircle className=\"h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0\" />\n                                <div className=\"text-sm text-orange-800\">\n                                  <div className=\"font-medium mb-1\">Bobine Incompatibili</div>\n                                  <div className=\"text-xs\">\n                                    Selezionando una bobina incompatibile, le caratteristiche del cavo verranno aggiornate.\n                                  </div>\n                                </div>\n                              </div>\n                            </div>\n\n                            {bobineIncompatibili.map((bobina) => (\n                              <div\n                                key={bobina.id_bobina}\n                                onClick={() => setSelectedBobina(bobina.id_bobina)}\n                                className={`p-3 rounded-lg cursor-pointer transition-all duration-200 ${\n                                  selectedBobina === bobina.id_bobina\n                                    ? 'bg-orange-100 border-2 border-orange-300 shadow-md'\n                                    : 'hover:bg-gray-50 border border-gray-200 hover:border-gray-300'\n                                }`}\n                              >\n                                <div className=\"flex justify-between items-start\">\n                                  <div className=\"flex-1\">\n                                    <div className=\"flex items-center space-x-2 mb-1\">\n                                      <div className=\"font-medium text-sm text-gray-900\">{bobina.id_bobina}</div>\n                                      <span className=\"px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800\">\n                                        INCOMPATIBILE\n                                      </span>\n                                    </div>\n                                    <div className=\"text-xs text-gray-500 mb-1\">\n                                      <span className=\"font-medium\">{bobina.tipologia}</span> • <span>{bobina.sezione}</span>\n                                    </div>\n                                  </div>\n                                  <div className=\"text-right ml-3\">\n                                    <div className={`text-sm font-medium ${\n                                      bobina.metri_residui > 0 ? 'text-orange-600' : 'text-gray-500'\n                                    }`}>\n                                      {bobina.metri_residui}m\n                                    </div>\n                                    <div className=\"text-xs text-gray-400\">\n                                      {bobina.metri_residui > 0 ? 'disponibili' : 'esaurita'}\n                                    </div>\n                                  </div>\n                                </div>\n                              </div>\n                            ))}\n                          </div>\n                        )\n                      )}\n                    </div>\n                  )}\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Errori */}\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          <DialogFooter className=\"flex justify-end space-x-2\">\n            <Button variant=\"outline\" onClick={handleClose} disabled={loading}>\n              Annulla\n            </Button>\n            <Button \n              onClick={handleSave} \n              disabled={loading || (selectedOption === 'assegna_nuova' && !selectedBobina)}\n            >\n              {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n              Salva\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;;;AAjBA;;;;;;;;;AA4Be,SAAS,qBAAqB,EAC3C,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,UAAU,YAAY,EACtB,SAAS,EACT,OAAO,EACmB;;IAC1B,MAAM,EAAE,UAAU,mBAAmB,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChD,MAAM,WAAW,gBAAgB;IAEjC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;IAE5E,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,MAAM;gBACR,kBAAkB;gBAClB,kBAAkB;gBAClB,cAAc;gBACd,aAAa;gBACb,SAAS;gBACT,IAAI,UAAU,aAAa;oBACzB;gBACF;YACF;QACF;yCAAG;QAAC;QAAM,UAAU;KAAY;IAEhC,MAAM,aAAa;QACjB,IAAI,CAAC,UAAU,aAAa;YAC1B,SAAS;YACT;QACF;QAEA,IAAI;YACF,iBAAiB;YACjB,SAAS;YAET,QAAQ,GAAG,CAAC,6DAA6D,SAAS,WAAW;YAE7F,+CAA+C;YAC/C,MAAM,WAAW,MAAM,oHAAA,CAAA,eAAY,CAAC,SAAS,CAAC,SAAS,WAAW;YAElE,IAAI,YAAY,MAAM,OAAO,CAAC,WAAW;gBACvC,2GAA2G;gBAC3G,MAAM,qBAAqB,SAAS,MAAM,CAAC,CAAC,SAC1C,OAAO,YAAY,KAAK,eACxB,OAAO,YAAY,KAAK,UACxB,OAAO,aAAa,GAAG;gBAGzB,UAAU;gBACV,QAAQ,GAAG,CAAC,4CAA4C,mBAAmB,MAAM;gBACjF,QAAQ,GAAG,CAAC,8CAA8C,mBAAmB,GAAG,CAAC,CAAA,IAAK,CAAC;wBACrF,IAAI,EAAE,SAAS;wBACf,WAAW,EAAE,SAAS;wBACtB,SAAS,EAAE,OAAO;wBAClB,eAAe,EAAE,aAAa;wBAC9B,OAAO,EAAE,YAAY;oBACvB,CAAC;YACH,OAAO;gBACL,UAAU,EAAE;gBACZ,QAAQ,GAAG,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sDAAsD;YACpE,SAAS;YACT,UAAU,EAAE;QACd,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,gEAAgE;IAChE,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,YAAY,aAAa,gBAAgB,OAAO;QACrD,IAAI,YAAY,SAAS,QAAQ,CAAC,OAAO;YACvC,OAAO,SAAS,KAAK,CAAC,KAAK,CAAC,EAAE;QAChC;QACA,MAAM,SAAS,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;QAChD,OAAO,SAAS,OAAO,aAAa,IAAI,WAAW;IACrD;IAEA,+BAA+B;IAC/B,MAAM,uBAAuB;QAC3B,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,MAAM,cAAc,OAAO,MAAM,CAAC,CAAA;YAChC,MAAM,eAAe,OAAO,SAAS,KAAK,KAAK,SAAS,IACpC,OAAO,OAAO,KAAK,KAAK,OAAO;YACnD,MAAM,gBAAgB,eAAe,MAChB,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC7D,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAClF,OAAO,aAAa,IAAI,OAAO,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;YAChH,OAAO,gBAAgB,iBAAiB,OAAO,aAAa,GAAG;QACjE;QAEA,QAAQ,GAAG,CAAC,gDAAgD;YAC1D,eAAe,KAAK,SAAS;YAC7B,aAAa,KAAK,OAAO;YACzB,cAAc,OAAO,MAAM;YAC3B,mBAAmB,YAAY,MAAM;YACrC;QACF;QAEA,OAAO;IACT;IAEA,iCAAiC;IACjC,MAAM,yBAAyB;QAC7B,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,OAAO,OAAO,MAAM,CAAC,CAAA;YACnB,MAAM,iBAAiB,OAAO,SAAS,KAAK,KAAK,SAAS,IACpC,OAAO,OAAO,KAAK,KAAK,OAAO;YACrD,MAAM,gBAAgB,eAAe,MAChB,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC7D,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAClF,OAAO,aAAa,IAAI,OAAO,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;YAChH,OAAO,kBAAkB,iBAAiB,OAAO,aAAa,GAAG;QACnE;IACF;IAEA,MAAM,oBAAoB;IAC1B,MAAM,sBAAsB;IAE5B,MAAM,cAAc;QAClB,kBAAkB;QAClB,kBAAkB;QAClB,cAAc;QACd,SAAS;QACT;IACF;IAEA,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC,yCAAyC;YACnD;YACA;YACA,QAAQ,MAAM;YACd,YAAY,UAAU;QACxB;QAEA,IAAI,CAAC,MAAM;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,IAAI,mBAAmB,iBAAiB;gBACtC,IAAI,CAAC,gBAAgB;oBACnB,QAAQ;oBACR;gBACF;gBAEA,qDAAqD;gBACrD,MAAM,SAAS,MAAM,oHAAA,CAAA,UAAO,CAAC,iBAAiB,CAAC;oBAC7C,SAAS,KAAK,OAAO;oBACrB,cAAc,KAAK,eAAe,IAAI;oBACtC,WAAW;oBACX,YAAY,KAAK,oCAAoC;gBACvD;gBAEA,IAAI,OAAO,OAAO,EAAE;oBAClB,UAAU,CAAC,2CAA2C,EAAE,KAAK,OAAO,EAAE;oBACtE;gBACF,OAAO;oBACL,QAAQ,OAAO,OAAO,IAAI;gBAC5B;YACF,OAAO,IAAI,mBAAmB,kBAAkB;gBAC9C,uBAAuB;gBACvB,MAAM,SAAS,MAAM,oHAAA,CAAA,UAAO,CAAC,iBAAiB,CAAC;oBAC7C,SAAS,KAAK,OAAO;oBACrB,cAAc,KAAK,eAAe,IAAI;oBACtC,WAAW;oBACX,YAAY;gBACd;gBAEA,IAAI,OAAO,OAAO,EAAE;oBAClB,UAAU,CAAC,wBAAwB,EAAE,KAAK,OAAO,EAAE;oBACnD;gBACF,OAAO;oBACL,QAAQ,OAAO,OAAO,IAAI;gBAC5B;YACF,OAAO,IAAI,mBAAmB,yBAAyB;gBACrD,0BAA0B;gBAC1B,MAAM,SAAS,MAAM,oHAAA,CAAA,UAAO,CAAC,iBAAiB,CAAC;oBAC7C,SAAS,KAAK,OAAO;oBACrB,cAAc;oBACd,WAAW;oBACX,YAAY;gBACd;gBAEA,IAAI,OAAO,OAAO,EAAE;oBAClB,UAAU,CAAC,oCAAoC,EAAE,KAAK,OAAO,EAAE;oBAC/D;gBACF,OAAO;oBACL,QAAQ,OAAO,OAAO,IAAI;gBAC5B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE;kBACE,cAAA,6LAAC,qIAAA,CAAA,SAAM;YAAC,MAAM;YAAM,cAAc;sBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,6LAAC,qIAAA,CAAA,eAAY;kCACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;;gCAAC;gCACW,KAAK,OAAO;;;;;;;;;;;;kCAItC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,6LAAC;gDAAG,WAAU;0DAA4B;;;;;;;;;;;;kDAI5C,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;gDAAoC;gDACjB,KAAK,SAAS,IAAI;gDAAM;gDAAQ,KAAK,mBAAmB,IAAI;gDAAM;gDAAgB,KAAK,OAAO,IAAI;gDAAM;gDAAO,KAAK,iBAAiB,IAAI;gDAAM;gDAAkB,KAAK,eAAe,IAAI;gDAAE;;;;;;;;;;;;;;;;;;0CAMjO,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAc;;;;;;kDAE5B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAM;wDACN,SAAS,mBAAmB;wDAC5B,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDACjD,WAAU;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;0DAG5B,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAM;wDACN,SAAS,mBAAmB;wDAC5B,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDACjD,WAAU;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;0DAG5B,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAM;wDACN,SAAS,mBAAmB;wDAC5B,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDACjD,WAAU;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEAAuB;;;;;;;;;;;;;;;;;;;;;;;;4BAM5C,mBAAmB,iCAClB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAc;;;;;;kDAG5B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;kDAKd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAC,2DAA2D,EACrE,cAAc,gBACV,gDACA,wDACJ;0DAEF,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6LAAC;;gEAAK;gEAAqB,kBAAkB,MAAM;gEAAC;;;;;;;;;;;;;;;;;;0DAGxD,6LAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAC,2DAA2D,EACrE,cAAc,kBACV,mDACA,wDACJ;0DAEF,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6LAAC;;gEAAK;gEAAuB,oBAAoB,MAAM;gEAAC;;;;;;;;;;;;;;;;;;;;;;;;kDAM9D,6LAAC;wCAAI,WAAU;kDACZ,8BACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;;;;;iEAI5C,6LAAC;4CAAI,WAAU;sDACZ,cAAc,gBACb,kBAAkB,MAAM,KAAK,kBAC3B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAA6B;;;;;;kEAG5C,6LAAC;wDAAI,WAAU;;4DAAwB;0EACP,6LAAC;0EAAQ,KAAK,SAAS;;;;;;4DAAU;0EAAc,6LAAC;0EAAQ,KAAK,OAAO;;;;;;;;;;;;;;;;;qEAItG,6LAAC;gDAAI,WAAU;0DACZ,kBAAkB,GAAG,CAAC,CAAC,uBACtB,6LAAC;wDAEC,SAAS,IAAM,kBAAkB,OAAO,SAAS;wDACjD,WAAW,CAAC,0DAA0D,EACpE,mBAAmB,OAAO,SAAS,GAC/B,mDACA,iEACJ;kEAEF,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FAAqC,OAAO,SAAS;;;;;;gFACnE,OAAO,YAAY,kBAClB,6LAAC;oFAAK,WAAW,CAAC,2CAA2C,EAC3D,OAAO,YAAY,KAAK,gBAAgB,gCACxC,OAAO,YAAY,KAAK,WAAW,8BACnC,iCACA;8FACC,OAAO,YAAY;;;;;;;;;;;;sFAI1B,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAK,WAAU;8FAAe,OAAO,SAAS;;;;;;gFAAQ;8FAAG,6LAAC;8FAAM,OAAO,OAAO;;;;;;;;;;;;;;;;;;8EAGnF,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAW,CAAC,oBAAoB,EACnC,OAAO,aAAa,GAAG,IAAI,mBAAmB,iBAC9C;;gFACC,OAAO,aAAa;gFAAC;;;;;;;sFAExB,6LAAC;4EAAI,WAAU;sFACZ,OAAO,aAAa,GAAG,IAAI,gBAAgB;;;;;;;;;;;;;;;;;;uDAjC7C,OAAO,SAAS;;;;;;;;;uDA0C7B,oBAAoB,MAAM,KAAK,kBAC7B,6LAAC;gDAAI,WAAU;0DAAyC;;;;;qEAIxD,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;8EACvB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFAAmB;;;;;;sFAClC,6LAAC;4EAAI,WAAU;sFAAU;;;;;;;;;;;;;;;;;;;;;;;oDAO9B,oBAAoB,GAAG,CAAC,CAAC,uBACxB,6LAAC;4DAEC,SAAS,IAAM,kBAAkB,OAAO,SAAS;4DACjD,WAAW,CAAC,0DAA0D,EACpE,mBAAmB,OAAO,SAAS,GAC/B,uDACA,iEACJ;sEAEF,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAI,WAAU;kGAAqC,OAAO,SAAS;;;;;;kGACpE,6LAAC;wFAAK,WAAU;kGAA2E;;;;;;;;;;;;0FAI7F,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAK,WAAU;kGAAe,OAAO,SAAS;;;;;;oFAAQ;kGAAG,6LAAC;kGAAM,OAAO,OAAO;;;;;;;;;;;;;;;;;;kFAGnF,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAW,CAAC,oBAAoB,EACnC,OAAO,aAAa,GAAG,IAAI,oBAAoB,iBAC/C;;oFACC,OAAO,aAAa;oFAAC;;;;;;;0FAExB,6LAAC;gFAAI,WAAU;0FACZ,OAAO,aAAa,GAAG,IAAI,gBAAgB;;;;;;;;;;;;;;;;;;2DA3B7C,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBA4C1C,uBACC,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;;0CACb,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC,oIAAA,CAAA,mBAAgB;0CAAE;;;;;;;;;;;;kCAIvB,6LAAC,qIAAA,CAAA,eAAY;wBAAC,WAAU;;0CACtB,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;gCAAa,UAAU;0CAAS;;;;;;0CAGnE,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU,WAAY,mBAAmB,mBAAmB,CAAC;;oCAE5D,yBAAW,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA+B;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5E;GAvewB;;QAQoB,kIAAA,CAAA,UAAO;;;KAR3B", "debugId": null}}, {"offset": {"line": 9510, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/InserisciMetriDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  <PERSON>alogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Badge } from '@/components/ui/badge'\nimport { Loader2, AlertCircle, Calculator, Search, CheckCircle, AlertTriangle, X } from 'lucide-react'\nimport { Cavo } from '@/types'\nimport { parcoCaviApi, caviApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\nimport ModificaBobinaDialog from './ModificaBobinaDialog'\n\ninterface Bobina {\n  id_bobina: string\n  numero_bobina?: string\n  tipologia: string\n  formazione: string\n  metri_residui: number\n  fornitore?: string\n  stato_bobina?: string\n}\n\ninterface FormData {\n  metri_posati: string\n  id_bobina: string\n}\n\ninterface FormErrors {\n  metri_posati?: string\n  id_bobina?: string\n}\n\ninterface FormWarnings {\n  metri_posati?: string\n}\n\ninterface InserisciMetriDialogProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  cantiere?: { id_cantiere: number; commessa: string } | null\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function InserisciMetriDialog({\n  open,\n  onClose,\n  cavo,\n  cantiere: cantiereProp,\n  onSuccess,\n  onError\n}: InserisciMetriDialogProps) {\n  const { cantiere: cantiereAuth } = useAuth()\n\n  // Usa il cantiere passato come prop o quello dall'auth come fallback\n  const cantiere = cantiereProp || cantiereAuth\n  \n  // Stati per il form\n  const [formData, setFormData] = useState<FormData>({\n    metri_posati: '',\n    id_bobina: ''\n  })\n\n  // Debug formData changes\n  useEffect(() => {\n    console.log('📊 InserisciMetriDialog: FormData aggiornato:', {\n      hasMetri: !!formData.metri_posati,\n      hasBobina: !!formData.id_bobina,\n      metri_posati: formData.metri_posati,\n      id_bobina: formData.id_bobina\n    })\n  }, [formData])\n  const [formErrors, setFormErrors] = useState<FormErrors>({})\n  const [formWarnings, setFormWarnings] = useState<FormWarnings>({})\n  const [saving, setSaving] = useState(false)\n  \n  // Stati per bobine\n  const [bobine, setBobine] = useState<Bobina[]>([])\n  const [bobineLoading, setBobineLoading] = useState(false)\n\n  // Stati per la ricerca delle bobine\n  const [searchText, setSearchText] = useState('')\n\n  // Stati per dialoghi\n  const [showModificaBobinaDialog, setShowModificaBobinaDialog] = useState(false)\n\n  // Carica bobine quando si apre il dialog\n  useEffect(() => {\n    if (open && cavo) {\n\n      if (cantiere) {\n        loadBobine()\n      } else {\n      }\n\n      setFormData({\n        metri_posati: '0', // Default a 0 come nell'originale\n        id_bobina: ''\n      })\n      setFormErrors({})\n      setFormWarnings({})\n      setSearchText('')\n    }\n  }, [open, cavo, cantiere])\n\n  // Validazione real-time dei metri posati\n  useEffect(() => {\n    if (formData.metri_posati && cavo) {\n      validateMetriPosati(parseFloat(formData.metri_posati))\n    } else {\n      setFormErrors(prev => ({ ...prev, metri_posati: undefined }))\n      setFormWarnings(prev => ({ ...prev, metri_posati: undefined }))\n    }\n  }, [formData.metri_posati, cavo])\n\n  const validateMetriPosati = (metri: number) => {\n    if (!cavo) return\n\n    let errors: FormErrors = { ...formErrors }\n    let warnings: FormWarnings = { ...formWarnings }\n\n    // Rimuovi errori/warning precedenti per metri_posati\n    delete errors.metri_posati\n    delete warnings.metri_posati\n\n    // NESSUN ERRORE BLOCCANTE - Solo warning informativi\n    // I warning non impediscono il salvataggio\n\n    if (metri > (cavo.metri_teorici || 0) * 1.1) {\n      warnings.metri_posati = `Attenzione: i metri posati superano del 10% i metri teorici (${cavo.metri_teorici}m)`\n    } else if (metri > (cavo.metri_teorici || 0)) {\n      warnings.metri_posati = 'Metratura superiore ai metri teorici'\n    }\n\n    setFormErrors(errors)\n    setFormWarnings(warnings)\n  }\n\n  const loadBobine = async () => {\n    console.log('🎯 InserisciMetriDialog: Caricamento bobine:', {\n      cavo: !!cavo,\n      cantiere: !!cantiere,\n      cavoId: cavo?.id_cavo,\n      cantiereId: cantiere?.id_cantiere\n    })\n\n    if (!cavo || !cantiere) {\n      return\n    }\n\n    try {\n      setBobineLoading(true)\n\n      // Carica tutte le bobine disponibili\n      const response = await parcoCaviApi.getBobine(cantiere.id_cantiere)\n\n      // Gestisce diversi formati di risposta\n      let bobineData = []\n      if (Array.isArray(response)) {\n        bobineData = response\n      } else if (response && Array.isArray(response.data)) {\n        bobineData = response.data\n      } else if (response && response.bobine && Array.isArray(response.bobine)) {\n        bobineData = response.bobine\n      } else {\n        throw new Error('Formato risposta API non valido')\n      }\n\n      // Filtra solo per stato (disponibile o in uso) e metri residui > 0\n      const bobineUtilizzabili = bobineData.filter((bobina: Bobina) =>\n        bobina.stato_bobina !== 'Terminata' &&\n        bobina.stato_bobina !== 'Over' &&\n        bobina.metri_residui > 0\n      )\n\n      if (cavo) {\n        console.log('🔍 InserisciMetriDialog: Filtro per cavo:', {\n          tipologia: cavo.tipologia,\n          sezione: cavo.sezione\n        })\n\n        // Separa bobine compatibili e incompatibili\n        const bobineCompatibili = bobineUtilizzabili.filter(bobina => {\n          const isCompatible = bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione\n          return isCompatible\n        })\n        const bobineNonCompatibili = bobineUtilizzabili.filter(bobina =>\n          !(bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione)\n        )\n\n        // Ordina entrambi gli array per metri residui (decrescente)\n        bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui)\n        bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui)\n\n        // Concatena gli array: prima le compatibili, poi le non compatibili\n        const bobineOrdinate = [...bobineCompatibili, ...bobineNonCompatibili]\n        setBobine(bobineOrdinate)\n      } else {\n        // Se non c'è un cavo selezionato, mostra tutte le bobine ordinate per metri residui\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui)\n        setBobine(bobineUtilizzabili)\n      }\n    } catch (error: any) {\n      console.error('❌ InserisciMetriDialog: Errore caricamento bobine:', {\n        message: error.message,\n        response: error.response,\n        status: error.response?.status,\n        data: error.response?.data\n      })\n\n      // Non mostrare errore se è solo un problema di rete, permetti di usare BOBINA_VUOTA\n      if (error.response?.status !== 404) {\n        onError('Errore nel caricamento delle bobine. Puoi comunque usare BOBINA VUOTA.')\n      }\n      setBobine([])\n    } finally {\n      setBobineLoading(false)\n    }\n  }\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = (idBobina: string) => {\n    if (!idBobina || idBobina === 'BOBINA_VUOTA') return 'VUOTA'\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1]\n    }\n    // Cerca nella lista bobine per ottenere il numero_bobina\n    const bobina = bobine.find(b => b.id_bobina === idBobina)\n    return bobina ? bobina.numero_bobina || idBobina : idBobina\n  }\n\n  // Filtra le bobine compatibili\n  const getBobineCompatibili = () => {\n    if (!cavo) return []\n\n    return bobine.filter(bobina => {\n      const isCompatible = bobina.tipologia === cavo.tipologia &&\n                          bobina.sezione === cavo.sezione\n      const matchesSearch = searchText === '' ||\n                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) ||\n                           (bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase())) ||\n                           (bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase()))\n      return isCompatible && matchesSearch && bobina.metri_residui > 0\n    })\n  }\n\n  // Filtra le bobine incompatibili\n  const getBobineIncompatibili = () => {\n    if (!cavo) return []\n\n    return bobine.filter(bobina => {\n      const isIncompatible = bobina.tipologia !== cavo.tipologia ||\n                            bobina.sezione !== cavo.sezione\n      const matchesSearch = searchText === '' ||\n                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) ||\n                           (bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase())) ||\n                           (bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase()))\n      return isIncompatible && matchesSearch && bobina.metri_residui > 0\n    })\n  }\n\n  const bobineCompatibili = getBobineCompatibili()\n  const bobineIncompatibili = getBobineIncompatibili()\n\n  const handleBobinaSelect = (bobina: Bobina) => {\n    // Selezione diretta senza popup - sia compatibili che incompatibili\n    setFormData(prev => ({ ...prev, id_bobina: bobina.id_bobina }))\n\n    // Reset completo degli errori - rimuovi la chiave invece di impostarla vuota\n    setFormErrors(prev => {\n      const newErrors = { ...prev }\n      delete newErrors.id_bobina  // Rimuovi completamente la chiave\n      return newErrors\n    })\n  }\n\n  const handleBobinaVuotaSelect = () => {\n    setFormData(prev => {\n      const newData = { ...prev, id_bobina: 'BOBINA_VUOTA' }\n      return newData\n    })\n\n    // Reset completo degli errori - rimuovi la chiave invece di impostarla vuota\n    setFormErrors(prev => {\n      const newErrors = { ...prev }\n      delete newErrors.id_bobina  // Rimuovi completamente la chiave\n      return newErrors\n    })\n\n    console.log('🔄 InserisciMetriDialog: Usando BOBINA_VUOTA:', {\n      saving: false,\n      metri_posati: formData.metri_posati,\n      id_bobina: 'BOBINA_VUOTA',\n      errorsCount: Object.keys(formErrors).length\n    })\n  }\n\n  const handleSave = async () => {\n    console.log('💾 InserisciMetriDialog: Salvataggio metri:', {\n      cavo: cavo?.id_cavo,\n      metri_posati: formData.metri_posati,\n      id_bobina: formData.id_bobina\n    })\n\n    if (!cavo) {\n      return\n    }\n\n    // Validazioni di base (solo controlli essenziali)\n    if (!formData.metri_posati || parseFloat(formData.metri_posati) < 0) {\n      onError('Inserire metri posati validi (≥ 0)')\n      return\n    }\n\n    if (!formData.id_bobina) {\n      onError('Selezionare una bobina o BOBINA VUOTA')\n      return\n    }\n\n    const metri = parseFloat(formData.metri_posati)\n\n    // Gestione stato OVER per bobine reali (NON BLOCCANTE)\n    if (formData.id_bobina !== 'BOBINA_VUOTA') {\n      const bobina = bobine.find(b => b.id_bobina === formData.id_bobina)\n      if (bobina && metri > bobina.metri_residui) {\n        // OVER state - salva comunque ma avvisa\n        // Il salvataggio continua - lo stato OVER viene gestito dal backend\n      }\n    }\n\n    try {\n      setSaving(true)\n\n      if (!cantiere) {\n        throw new Error('Cantiere non selezionato')\n      }\n\n      // Aggiorna metri posati tramite API\n      console.log('🚀 InserisciMetriDialog: Chiamata API updateMetriPosati:', {\n        cantiere: cantiere.id_cantiere,\n        cavo: cavo.id_cavo,\n        metri: metri,\n        bobina: formData.id_bobina,  // Mostra il valore reale che viene passato\n        isBobinaVuota: formData.id_bobina === 'BOBINA_VUOTA'\n      })\n\n      await caviApi.updateMetriPosati(\n        cantiere.id_cantiere,\n        cavo.id_cavo,\n        metri,\n        formData.id_bobina,  // Passa sempre il valore, incluso 'BOBINA_VUOTA'\n        true  // force_over: true per permettere bobine incompatibili e OVER state\n      )\n\n      onSuccess(`Metri posati aggiornati con successo per il cavo ${cavo.id_cavo}: ${metri}m`)\n      onClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il salvataggio dei metri posati'\n      onError(errorMessage)\n    } finally {\n      setSaving(false)\n    }\n  }\n\n  const handleClose = () => {\n    if (!saving) {\n      setFormData({ metri_posati: '', id_bobina: '' })\n      setFormErrors({})\n      setFormWarnings({})\n      setSearchText('')\n      onClose()\n    }\n  }\n\n  if (!cavo) return null\n\n  return (\n    <>\n      <Dialog open={open} onOpenChange={handleClose}>\n        <DialogContent className=\"max-w-7xl h-[90vh] flex flex-col\">\n          <DialogHeader className=\"flex-shrink-0\">\n            <DialogTitle className=\"flex items-center gap-2\">\n              <Calculator className=\"h-5 w-5\" />\n              Inserisci Metri Posati - {cavo.id_cavo}\n            </DialogTitle>\n            <DialogDescription>\n              Inserisci i metri effettivamente posati per il cavo e seleziona una bobina o usa BOBINA VUOTA\n            </DialogDescription>\n          </DialogHeader>\n\n          <div className=\"flex-1 overflow-y-auto space-y-6\">\n            {/* Sezione informazioni cavo e metri posati */}\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-4\">\n              {/* Informazioni cavo - 2/3 della larghezza */}\n              <div className=\"lg:col-span-2\">\n                <div className=\"p-4 bg-blue-50 rounded-lg border-2 border-blue-200\">\n                  <h3 className=\"font-semibold text-blue-800 mb-3\">Informazioni Cavo</h3>\n                  <div className=\"grid grid-cols-2 gap-3 text-sm\">\n                    <div><strong>Tipologia:</strong> {cavo.tipologia || 'N/A'}</div>\n                    <div><strong>Da:</strong> {cavo.ubicazione_partenza || 'N/A'}</div>\n                    <div><strong>Formazione:</strong> {cavo.sezione || 'N/A'}</div>\n                    <div><strong>A:</strong> {cavo.ubicazione_arrivo || 'N/A'}</div>\n                    <div><strong>Metri teorici:</strong> {cavo.metri_teorici || 'N/A'} m</div>\n                    <div><strong>Già posati:</strong> {cavo.metratura_reale || 0} m</div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Campo metri posati - 1/3 della larghezza */}\n              <div className=\"lg:col-span-1\">\n                <div className=\"p-4 bg-blue-50 rounded-lg border-2 border-blue-300 h-full\">\n                  <h3 className=\"font-semibold text-blue-800 mb-3\">Metri da Installare</h3>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"metri\" className=\"text-sm font-medium\">\n                      Metri Posati\n                    </Label>\n                    <div className=\"relative\">\n                      <Input\n                        id=\"metri\"\n                        type=\"number\"\n                        value={formData.metri_posati}\n                        onChange={(e) => setFormData(prev => ({ ...prev, metri_posati: e.target.value }))}\n                        placeholder=\"Inserisci metri posati\"\n                        disabled={saving}\n                        step=\"0.1\"\n                        min=\"0\"\n                        className=\"text-lg font-bold text-center border-2 border-blue-400 focus:border-blue-600\"\n                        autoFocus\n                      />\n                      <span className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-lg font-bold text-blue-600\">\n                        m\n                      </span>\n                    </div>\n                    {formErrors.metri_posati && (\n                      <p className=\"text-sm text-red-600\">{formErrors.metri_posati}</p>\n                    )}\n                    {formWarnings.metri_posati && (\n                      <p className=\"text-sm text-amber-600\">{formWarnings.metri_posati}</p>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Selezione bobina */}\n            <div className=\"space-y-4\">\n              <h3 className=\"font-semibold text-blue-800 text-lg\">Selezione Bobina</h3>\n\n              {/* Controlli di ricerca e BOBINA VUOTA */}\n              <div className=\"p-4 bg-gray-50 rounded-lg\">\n                <div className=\"grid grid-cols-1 sm:grid-cols-12 gap-3 items-center\">\n                  {/* Campo di ricerca - 5 colonne */}\n                  <div className=\"sm:col-span-5\">\n                    <div className=\"relative\">\n                      <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                      <Input\n                        placeholder=\"ID, tipologia, formazione...\"\n                        value={searchText}\n                        onChange={(e) => setSearchText(e.target.value)}\n                        className=\"pl-10\"\n                        disabled={saving}\n                      />\n                      {searchText && (\n                        <Button\n                          type=\"button\"\n                          variant=\"ghost\"\n                          size=\"sm\"\n                          className=\"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0\"\n                          onClick={() => setSearchText('')}\n                        >\n                          <X className=\"h-4 w-4\" />\n                        </Button>\n                      )}\n                    </div>\n                  </div>\n\n                  {/* Pulsante BOBINA VUOTA - 7 colonne */}\n                  <div className=\"sm:col-span-7\">\n                    <Button\n                      type=\"button\"\n                      variant={formData.id_bobina === 'BOBINA_VUOTA' ? 'default' : 'outline'}\n                      className={`w-full h-10 font-bold flex items-center justify-center gap-2 ${\n                        formData.id_bobina === 'BOBINA_VUOTA'\n                          ? 'bg-green-600 hover:bg-green-700 text-white'\n                          : 'border-blue-400 text-blue-700 hover:bg-blue-50'\n                      }`}\n                      onClick={handleBobinaVuotaSelect}\n                      disabled={saving}\n                    >\n                      {formData.id_bobina === 'BOBINA_VUOTA' && (\n                        <CheckCircle className=\"h-5 w-5\" />\n                      )}\n                      BOBINA VUOTA\n                    </Button>\n                  </div>\n                </div>\n              </div>\n\n              {bobineLoading ? (\n                <div className=\"flex items-center justify-center p-8\">\n                  <Loader2 className=\"h-6 w-6 animate-spin mr-2\" />\n                  <span>Caricamento bobine...</span>\n                </div>\n              ) : (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  {/* Bobine compatibili */}\n                  <div>\n                    <h4 className=\"font-medium text-green-700 mb-2 flex items-center gap-2\">\n                      <CheckCircle className=\"h-4 w-4\" />\n                      Bobine Compatibili ({bobineCompatibili.length})\n                    </h4>\n                    <div className=\"max-h-72 overflow-y-auto border rounded-lg\">\n                      {bobineCompatibili.length === 0 ? (\n                        <div className=\"p-4 text-center text-gray-500\">\n                          Nessuna bobina compatibile trovata\n                        </div>\n                      ) : (\n                        <div className=\"divide-y\">\n                          {bobineCompatibili.map((bobina) => (\n                            <div\n                              key={bobina.id_bobina}\n                              className={`p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ${\n                                formData.id_bobina === bobina.id_bobina\n                                  ? 'bg-green-100 border-green-500 shadow-md'\n                                  : 'border-gray-200 hover:bg-green-50 hover:border-green-300'\n                              }`}\n                              onClick={() => handleBobinaSelect(bobina)}\n                            >\n                              <div className=\"flex justify-between items-center\">\n                                <div className=\"flex items-center gap-3 flex-1 min-w-0\">\n                                  {formData.id_bobina === bobina.id_bobina && (\n                                    <CheckCircle className=\"h-5 w-5 text-green-600 flex-shrink-0\" />\n                                  )}\n                                  <div className=\"font-bold text-base min-w-fit\">\n                                    {getBobinaNumber(bobina.id_bobina)}\n                                  </div>\n                                  <div className=\"text-sm text-gray-600 truncate\">\n                                    {bobina.tipologia} - {bobina.sezione}\n                                  </div>\n                                </div>\n                                <Badge variant=\"outline\" className=\"bg-green-100 text-green-800 border-green-300 font-medium min-w-fit\">\n                                  {bobina.metri_residui}m\n                                </Badge>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n\n                  {/* Bobine incompatibili */}\n                  <div>\n                    <h4 className=\"font-medium text-amber-700 mb-2 flex items-center gap-2\">\n                      <AlertTriangle className=\"h-4 w-4\" />\n                      Bobine Incompatibili ({bobineIncompatibili.length})\n                    </h4>\n                    <div className=\"max-h-72 overflow-y-auto border rounded-lg\">\n                      {bobineIncompatibili.length === 0 ? (\n                        <div className=\"p-4 text-center text-gray-500\">\n                          Nessuna bobina incompatibile trovata\n                        </div>\n                      ) : (\n                        <div className=\"divide-y\">\n                          {bobineIncompatibili.map((bobina) => (\n                            <div\n                              key={bobina.id_bobina}\n                              className={`p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ${\n                                formData.id_bobina === bobina.id_bobina\n                                  ? 'bg-amber-100 border-amber-500 shadow-md'\n                                  : 'border-gray-200 hover:bg-amber-50 hover:border-amber-300'\n                              }`}\n                              onClick={() => handleBobinaSelect(bobina)}\n                            >\n                              <div className=\"flex justify-between items-center\">\n                                <div className=\"flex items-center gap-3 flex-1 min-w-0\">\n                                  {formData.id_bobina === bobina.id_bobina && (\n                                    <CheckCircle className=\"h-5 w-5 text-amber-600 flex-shrink-0\" />\n                                  )}\n                                  <div className=\"font-bold text-base min-w-fit\">\n                                    {getBobinaNumber(bobina.id_bobina)}\n                                  </div>\n                                  <div className=\"text-sm text-gray-600 truncate\">\n                                    {bobina.tipologia} - {bobina.sezione}\n                                  </div>\n                                </div>\n                                <Badge variant=\"outline\" className=\"bg-amber-100 text-amber-800 border-amber-300 font-medium min-w-fit\">\n                                  {bobina.metri_residui}m\n                                </Badge>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {bobine.length === 0 && !bobineLoading && (\n                <Alert className=\"border-amber-200 bg-amber-50\">\n                  <AlertTriangle className=\"h-4 w-4 text-amber-600\" />\n                  <AlertDescription className=\"text-amber-800\">\n                    Non ci sono bobine disponibili. Puoi procedere con BOBINA VUOTA o aggiungere prima una nuova bobina.\n                  </AlertDescription>\n                </Alert>\n              )}\n\n              {formErrors.id_bobina && (\n                <Alert variant=\"destructive\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  <AlertDescription>{formErrors.id_bobina}</AlertDescription>\n                </Alert>\n              )}\n            </div>\n          </div>\n\n          <DialogFooter className=\"flex-shrink-0 border-t pt-4 mt-4 flex justify-between items-center\">\n            {/* Pulsante Modifica Bobina a sinistra (solo se cavo è installato) */}\n            <div>\n              {cavo.stato_installazione === 'installato' && cavo.id_bobina && (\n                <Button\n                  variant=\"outline\"\n                  onClick={() => {\n                    setShowModificaBobinaDialog(true)\n                  }}\n                  disabled={saving}\n                  className=\"text-blue-600 border-blue-300 hover:bg-blue-50\"\n                >\n                  Modifica Bobina\n                </Button>\n              )}\n            </div>\n\n            {/* Pulsanti principali a destra */}\n            <div className=\"flex gap-2\">\n              <Button variant=\"outline\" onClick={handleClose} disabled={saving}>\n                Annulla\n              </Button>\n              <Button\n                onClick={handleSave}\n                disabled={\n                  saving ||\n                  !formData.metri_posati ||\n                  parseFloat(formData.metri_posati) < 0 ||\n                  !formData.id_bobina\n                }\n                className=\"bg-mariner-600 hover:bg-mariner-700 text-white disabled:bg-gray-400 disabled:text-gray-200\"\n              >\n                {saving && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n                Salva\n              </Button>\n            </div>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* Dialog per modifica bobina */}\n      <ModificaBobinaDialog\n        open={showModificaBobinaDialog}\n        onClose={() => setShowModificaBobinaDialog(false)}\n        cavo={cavo}\n        onSuccess={(message) => {\n          onSuccess(message)\n          setShowModificaBobinaDialog(false)\n          onClose() // Chiudi anche il dialog principale dopo il successo\n        }}\n        onError={onError}\n      />\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAEA;;;AArBA;;;;;;;;;;;;AAwDe,SAAS,qBAAqB,EAC3C,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,UAAU,YAAY,EACtB,SAAS,EACT,OAAO,EACmB;;IAC1B,MAAM,EAAE,UAAU,YAAY,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEzC,qEAAqE;IACrE,MAAM,WAAW,gBAAgB;IAEjC,oBAAoB;IACpB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,cAAc;QACd,WAAW;IACb;IAEA,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,QAAQ,GAAG,CAAC,iDAAiD;gBAC3D,UAAU,CAAC,CAAC,SAAS,YAAY;gBACjC,WAAW,CAAC,CAAC,SAAS,SAAS;gBAC/B,cAAc,SAAS,YAAY;gBACnC,WAAW,SAAS,SAAS;YAC/B;QACF;yCAAG;QAAC;KAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,CAAC;IAChE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,mBAAmB;IACnB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,oCAAoC;IACpC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBAAqB;IACrB,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzE,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,QAAQ,MAAM;gBAEhB,IAAI,UAAU;oBACZ;gBACF,OAAO,CACP;gBAEA,YAAY;oBACV,cAAc;oBACd,WAAW;gBACb;gBACA,cAAc,CAAC;gBACf,gBAAgB,CAAC;gBACjB,cAAc;YAChB;QACF;yCAAG;QAAC;QAAM;QAAM;KAAS;IAEzB,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,SAAS,YAAY,IAAI,MAAM;gBACjC,oBAAoB,WAAW,SAAS,YAAY;YACtD,OAAO;gBACL;sDAAc,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,cAAc;wBAAU,CAAC;;gBAC3D;sDAAgB,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,cAAc;wBAAU,CAAC;;YAC/D;QACF;yCAAG;QAAC,SAAS,YAAY;QAAE;KAAK;IAEhC,MAAM,sBAAsB,CAAC;QAC3B,IAAI,CAAC,MAAM;QAEX,IAAI,SAAqB;YAAE,GAAG,UAAU;QAAC;QACzC,IAAI,WAAyB;YAAE,GAAG,YAAY;QAAC;QAE/C,qDAAqD;QACrD,OAAO,OAAO,YAAY;QAC1B,OAAO,SAAS,YAAY;QAE5B,qDAAqD;QACrD,2CAA2C;QAE3C,IAAI,QAAQ,CAAC,KAAK,aAAa,IAAI,CAAC,IAAI,KAAK;YAC3C,SAAS,YAAY,GAAG,CAAC,6DAA6D,EAAE,KAAK,aAAa,CAAC,EAAE,CAAC;QAChH,OAAO,IAAI,QAAQ,CAAC,KAAK,aAAa,IAAI,CAAC,GAAG;YAC5C,SAAS,YAAY,GAAG;QAC1B;QAEA,cAAc;QACd,gBAAgB;IAClB;IAEA,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC,gDAAgD;YAC1D,MAAM,CAAC,CAAC;YACR,UAAU,CAAC,CAAC;YACZ,QAAQ,MAAM;YACd,YAAY,UAAU;QACxB;QAEA,IAAI,CAAC,QAAQ,CAAC,UAAU;YACtB;QACF;QAEA,IAAI;YACF,iBAAiB;YAEjB,qCAAqC;YACrC,MAAM,WAAW,MAAM,oHAAA,CAAA,eAAY,CAAC,SAAS,CAAC,SAAS,WAAW;YAElE,uCAAuC;YACvC,IAAI,aAAa,EAAE;YACnB,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,aAAa;YACf,OAAO,IAAI,YAAY,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBACnD,aAAa,SAAS,IAAI;YAC5B,OAAO,IAAI,YAAY,SAAS,MAAM,IAAI,MAAM,OAAO,CAAC,SAAS,MAAM,GAAG;gBACxE,aAAa,SAAS,MAAM;YAC9B,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;YAEA,mEAAmE;YACnE,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAC,SAC5C,OAAO,YAAY,KAAK,eACxB,OAAO,YAAY,KAAK,UACxB,OAAO,aAAa,GAAG;YAGzB,IAAI,MAAM;gBACR,QAAQ,GAAG,CAAC,6CAA6C;oBACvD,WAAW,KAAK,SAAS;oBACzB,SAAS,KAAK,OAAO;gBACvB;gBAEA,4CAA4C;gBAC5C,MAAM,oBAAoB,mBAAmB,MAAM,CAAC,CAAA;oBAClD,MAAM,eAAe,OAAO,SAAS,KAAK,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,KAAK,OAAO;oBAC3F,OAAO;gBACT;gBACA,MAAM,uBAAuB,mBAAmB,MAAM,CAAC,CAAA,SACrD,CAAC,CAAC,OAAO,SAAS,KAAK,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,KAAK,OAAO;gBAG1E,4DAA4D;gBAC5D,kBAAkB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,aAAa,GAAG,EAAE,aAAa;gBAClE,qBAAqB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,aAAa,GAAG,EAAE,aAAa;gBAErE,oEAAoE;gBACpE,MAAM,iBAAiB;uBAAI;uBAAsB;iBAAqB;gBACtE,UAAU;YACZ,OAAO;gBACL,oFAAoF;gBACpF,mBAAmB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,aAAa,GAAG,EAAE,aAAa;gBACnE,UAAU;YACZ;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,sDAAsD;gBAClE,SAAS,MAAM,OAAO;gBACtB,UAAU,MAAM,QAAQ;gBACxB,QAAQ,MAAM,QAAQ,EAAE;gBACxB,MAAM,MAAM,QAAQ,EAAE;YACxB;YAEA,oFAAoF;YACpF,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,QAAQ;YACV;YACA,UAAU,EAAE;QACd,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,gEAAgE;IAChE,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,YAAY,aAAa,gBAAgB,OAAO;QACrD,8DAA8D;QAC9D,IAAI,YAAY,SAAS,QAAQ,CAAC,OAAO;YACvC,OAAO,SAAS,KAAK,CAAC,KAAK,CAAC,EAAE;QAChC;QACA,yDAAyD;QACzD,MAAM,SAAS,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;QAChD,OAAO,SAAS,OAAO,aAAa,IAAI,WAAW;IACrD;IAEA,+BAA+B;IAC/B,MAAM,uBAAuB;QAC3B,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,OAAO,OAAO,MAAM,CAAC,CAAA;YACnB,MAAM,eAAe,OAAO,SAAS,KAAK,KAAK,SAAS,IACpC,OAAO,OAAO,KAAK,KAAK,OAAO;YACnD,MAAM,gBAAgB,eAAe,MAChB,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC7D,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAClF,OAAO,aAAa,IAAI,OAAO,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;YAChH,OAAO,gBAAgB,iBAAiB,OAAO,aAAa,GAAG;QACjE;IACF;IAEA,iCAAiC;IACjC,MAAM,yBAAyB;QAC7B,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,OAAO,OAAO,MAAM,CAAC,CAAA;YACnB,MAAM,iBAAiB,OAAO,SAAS,KAAK,KAAK,SAAS,IACpC,OAAO,OAAO,KAAK,KAAK,OAAO;YACrD,MAAM,gBAAgB,eAAe,MAChB,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC7D,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAClF,OAAO,aAAa,IAAI,OAAO,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;YAChH,OAAO,kBAAkB,iBAAiB,OAAO,aAAa,GAAG;QACnE;IACF;IAEA,MAAM,oBAAoB;IAC1B,MAAM,sBAAsB;IAE5B,MAAM,qBAAqB,CAAC;QAC1B,oEAAoE;QACpE,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,WAAW,OAAO,SAAS;YAAC,CAAC;QAE7D,6EAA6E;QAC7E,cAAc,CAAA;YACZ,MAAM,YAAY;gBAAE,GAAG,IAAI;YAAC;YAC5B,OAAO,UAAU,SAAS,CAAE,kCAAkC;;YAC9D,OAAO;QACT;IACF;IAEA,MAAM,0BAA0B;QAC9B,YAAY,CAAA;YACV,MAAM,UAAU;gBAAE,GAAG,IAAI;gBAAE,WAAW;YAAe;YACrD,OAAO;QACT;QAEA,6EAA6E;QAC7E,cAAc,CAAA;YACZ,MAAM,YAAY;gBAAE,GAAG,IAAI;YAAC;YAC5B,OAAO,UAAU,SAAS,CAAE,kCAAkC;;YAC9D,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,iDAAiD;YAC3D,QAAQ;YACR,cAAc,SAAS,YAAY;YACnC,WAAW;YACX,aAAa,OAAO,IAAI,CAAC,YAAY,MAAM;QAC7C;IACF;IAEA,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC,+CAA+C;YACzD,MAAM,MAAM;YACZ,cAAc,SAAS,YAAY;YACnC,WAAW,SAAS,SAAS;QAC/B;QAEA,IAAI,CAAC,MAAM;YACT;QACF;QAEA,kDAAkD;QAClD,IAAI,CAAC,SAAS,YAAY,IAAI,WAAW,SAAS,YAAY,IAAI,GAAG;YACnE,QAAQ;YACR;QACF;QAEA,IAAI,CAAC,SAAS,SAAS,EAAE;YACvB,QAAQ;YACR;QACF;QAEA,MAAM,QAAQ,WAAW,SAAS,YAAY;QAE9C,uDAAuD;QACvD,IAAI,SAAS,SAAS,KAAK,gBAAgB;YACzC,MAAM,SAAS,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK,SAAS,SAAS;YAClE,IAAI,UAAU,QAAQ,OAAO,aAAa,EAAE;YAC1C,wCAAwC;YACxC,oEAAoE;YACtE;QACF;QAEA,IAAI;YACF,UAAU;YAEV,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,oCAAoC;YACpC,QAAQ,GAAG,CAAC,4DAA4D;gBACtE,UAAU,SAAS,WAAW;gBAC9B,MAAM,KAAK,OAAO;gBAClB,OAAO;gBACP,QAAQ,SAAS,SAAS;gBAC1B,eAAe,SAAS,SAAS,KAAK;YACxC;YAEA,MAAM,oHAAA,CAAA,UAAO,CAAC,iBAAiB,CAC7B,SAAS,WAAW,EACpB,KAAK,OAAO,EACZ,OACA,SAAS,SAAS,EAClB,KAAM,oEAAoE;;YAG5E,UAAU,CAAC,iDAAiD,EAAE,KAAK,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;YACvF;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,QAAQ;YACX,YAAY;gBAAE,cAAc;gBAAI,WAAW;YAAG;YAC9C,cAAc,CAAC;YACf,gBAAgB,CAAC;YACjB,cAAc;YACd;QACF;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE;;0BACE,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAM,cAAc;0BAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,6LAAC,qIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC,iNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAY;wCACR,KAAK,OAAO;;;;;;;8CAExC,6LAAC,qIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAKrB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFAAI,6LAAC;kFAAO;;;;;;oEAAmB;oEAAE,KAAK,SAAS,IAAI;;;;;;;0EACpD,6LAAC;;kFAAI,6LAAC;kFAAO;;;;;;oEAAY;oEAAE,KAAK,mBAAmB,IAAI;;;;;;;0EACvD,6LAAC;;kFAAI,6LAAC;kFAAO;;;;;;oEAAoB;oEAAE,KAAK,OAAO,IAAI;;;;;;;0EACnD,6LAAC;;kFAAI,6LAAC;kFAAO;;;;;;oEAAW;oEAAE,KAAK,iBAAiB,IAAI;;;;;;;0EACpD,6LAAC;;kFAAI,6LAAC;kFAAO;;;;;;oEAAuB;oEAAE,KAAK,aAAa,IAAI;oEAAM;;;;;;;0EAClE,6LAAC;;kFAAI,6LAAC;kFAAO;;;;;;oEAAoB;oEAAE,KAAK,eAAe,IAAI;oEAAE;;;;;;;;;;;;;;;;;;;;;;;;sDAMnE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAQ,WAAU;0EAAsB;;;;;;0EAGvD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,oIAAA,CAAA,QAAK;wEACJ,IAAG;wEACH,MAAK;wEACL,OAAO,SAAS,YAAY;wEAC5B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oFAAE,GAAG,IAAI;oFAAE,cAAc,EAAE,MAAM,CAAC,KAAK;gFAAC,CAAC;wEAC/E,aAAY;wEACZ,UAAU;wEACV,MAAK;wEACL,KAAI;wEACJ,WAAU;wEACV,SAAS;;;;;;kFAEX,6LAAC;wEAAK,WAAU;kFAAsF;;;;;;;;;;;;4DAIvG,WAAW,YAAY,kBACtB,6LAAC;gEAAE,WAAU;0EAAwB,WAAW,YAAY;;;;;;4DAE7D,aAAa,YAAY,kBACxB,6LAAC;gEAAE,WAAU;0EAA0B,aAAa,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQ1E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAsC;;;;;;sDAGpD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,6LAAC,oIAAA,CAAA,QAAK;oEACJ,aAAY;oEACZ,OAAO;oEACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oEAC7C,WAAU;oEACV,UAAU;;;;;;gEAEX,4BACC,6LAAC,qIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,WAAU;oEACV,SAAS,IAAM,cAAc;8EAE7B,cAAA,6LAAC,+LAAA,CAAA,IAAC;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kEAOrB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAS,SAAS,SAAS,KAAK,iBAAiB,YAAY;4DAC7D,WAAW,CAAC,6DAA6D,EACvE,SAAS,SAAS,KAAK,iBACnB,+CACA,kDACJ;4DACF,SAAS;4DACT,UAAU;;gEAET,SAAS,SAAS,KAAK,gCACtB,6LAAC,8NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEACvB;;;;;;;;;;;;;;;;;;;;;;;wCAOT,8BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,6LAAC;8DAAK;;;;;;;;;;;iEAGR,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC,8NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAAY;gEACd,kBAAkB,MAAM;gEAAC;;;;;;;sEAEhD,6LAAC;4DAAI,WAAU;sEACZ,kBAAkB,MAAM,KAAK,kBAC5B,6LAAC;gEAAI,WAAU;0EAAgC;;;;;qFAI/C,6LAAC;gEAAI,WAAU;0EACZ,kBAAkB,GAAG,CAAC,CAAC,uBACtB,6LAAC;wEAEC,WAAW,CAAC,2DAA2D,EACrE,SAAS,SAAS,KAAK,OAAO,SAAS,GACnC,4CACA,4DACJ;wEACF,SAAS,IAAM,mBAAmB;kFAElC,cAAA,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;;wFACZ,SAAS,SAAS,KAAK,OAAO,SAAS,kBACtC,6LAAC,8NAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;sGAEzB,6LAAC;4FAAI,WAAU;sGACZ,gBAAgB,OAAO,SAAS;;;;;;sGAEnC,6LAAC;4FAAI,WAAU;;gGACZ,OAAO,SAAS;gGAAC;gGAAI,OAAO,OAAO;;;;;;;;;;;;;8FAGxC,6LAAC,oIAAA,CAAA,QAAK;oFAAC,SAAQ;oFAAU,WAAU;;wFAChC,OAAO,aAAa;wFAAC;;;;;;;;;;;;;uEArBrB,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;8DAgCjC,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC,2NAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;gEAAY;gEACd,oBAAoB,MAAM;gEAAC;;;;;;;sEAEpD,6LAAC;4DAAI,WAAU;sEACZ,oBAAoB,MAAM,KAAK,kBAC9B,6LAAC;gEAAI,WAAU;0EAAgC;;;;;qFAI/C,6LAAC;gEAAI,WAAU;0EACZ,oBAAoB,GAAG,CAAC,CAAC,uBACxB,6LAAC;wEAEC,WAAW,CAAC,2DAA2D,EACrE,SAAS,SAAS,KAAK,OAAO,SAAS,GACnC,4CACA,4DACJ;wEACF,SAAS,IAAM,mBAAmB;kFAElC,cAAA,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;;wFACZ,SAAS,SAAS,KAAK,OAAO,SAAS,kBACtC,6LAAC,8NAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;sGAEzB,6LAAC;4FAAI,WAAU;sGACZ,gBAAgB,OAAO,SAAS;;;;;;sGAEnC,6LAAC;4FAAI,WAAU;;gGACZ,OAAO,SAAS;gGAAC;gGAAI,OAAO,OAAO;;;;;;;;;;;;;8FAGxC,6LAAC,oIAAA,CAAA,QAAK;oFAAC,SAAQ;oFAAU,WAAU;;wFAChC,OAAO,aAAa;wFAAC;;;;;;;;;;;;;uEArBrB,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;wCAiCpC,OAAO,MAAM,KAAK,KAAK,CAAC,+BACvB,6LAAC,oIAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,6LAAC,oIAAA,CAAA,mBAAgB;oDAAC,WAAU;8DAAiB;;;;;;;;;;;;wCAMhD,WAAW,SAAS,kBACnB,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;;8DACb,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,6LAAC,oIAAA,CAAA,mBAAgB;8DAAE,WAAW,SAAS;;;;;;;;;;;;;;;;;;;;;;;;sCAM/C,6LAAC,qIAAA,CAAA,eAAY;4BAAC,WAAU;;8CAEtB,6LAAC;8CACE,KAAK,mBAAmB,KAAK,gBAAgB,KAAK,SAAS,kBAC1D,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;4CACP,4BAA4B;wCAC9B;wCACA,UAAU;wCACV,WAAU;kDACX;;;;;;;;;;;8CAOL,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS;4CAAa,UAAU;sDAAQ;;;;;;sDAGlE,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UACE,UACA,CAAC,SAAS,YAAY,IACtB,WAAW,SAAS,YAAY,IAAI,KACpC,CAAC,SAAS,SAAS;4CAErB,WAAU;;gDAET,wBAAU,6LAAC,oNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASvE,6LAAC,qJAAA,CAAA,UAAoB;gBACnB,MAAM;gBACN,SAAS,IAAM,4BAA4B;gBAC3C,MAAM;gBACN,WAAW,CAAC;oBACV,UAAU;oBACV,4BAA4B;oBAC5B,UAAU,qDAAqD;;gBACjE;gBACA,SAAS;;;;;;;;AAIjB;GApnBwB;;QAQa,kIAAA,CAAA,UAAO;;;KARpB", "debugId": null}}, {"offset": {"line": 10668, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 10703, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/CreaComandaDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, ClipboardList, Users } from 'lucide-react'\nimport { comandeApi, responsabiliApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface CreaComandaDialogProps {\n  open: boolean\n  onClose: () => void\n  caviSelezionati: string[]\n  tipoComanda?: 'POSA' | 'COLLEGAMENTO_PARTENZA' | 'COLLEGAMENTO_ARRIVO' | 'CERTIFICAZIONE'\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\ninterface Responsabile {\n  id: number\n  nome_responsabile: string\n  numero_telefono?: string\n  mail?: string\n}\n\nexport default function CreaComandaDialog({\n  open,\n  onClose,\n  caviSelezionati,\n  tipoComanda,\n  onSuccess,\n  onError\n}: CreaComandaDialogProps) {\n  const { cantiere } = useAuth()\n  const [formData, setFormData] = useState({\n    tipo_comanda: tipoComanda || 'POSA',\n    responsabile: '',\n    note: ''\n  })\n  const [responsabili, setResponsabili] = useState<Responsabile[]>([])\n  const [loading, setLoading] = useState(false)\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false)\n  const [error, setError] = useState('')\n\n  // Reset form quando si apre il dialog\n  useEffect(() => {\n    if (open) {\n      setFormData({\n        tipo_comanda: tipoComanda || 'POSA',\n        responsabile: '',\n        note: ''\n      })\n      setError('')\n      loadResponsabili()\n    }\n  }, [open, tipoComanda])\n\n  const loadResponsabili = async () => {\n    if (!cantiere) return\n\n    try {\n      setLoadingResponsabili(true)\n      const response = await responsabiliApi.getResponsabili(cantiere.id_cantiere)\n      setResponsabili(response.data)\n    } catch (error) {\n      setResponsabili([])\n    } finally {\n      setLoadingResponsabili(false)\n    }\n  }\n\n  const getTipoComandaLabel = (tipo: string) => {\n    switch (tipo) {\n      case 'POSA':\n        return 'Posa Cavi'\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'Collegamento Partenza'\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'Collegamento Arrivo'\n      case 'CERTIFICAZIONE':\n        return 'Certificazione'\n      default:\n        return tipo\n    }\n  }\n\n  const handleCreaComanda = async () => {\n    if (!cantiere) return\n\n    if (!formData.responsabile) {\n      setError('Seleziona un responsabile per la comanda')\n      return\n    }\n\n    if (caviSelezionati.length === 0) {\n      setError('Seleziona almeno un cavo per la comanda')\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n\n      // Crea la comanda con i cavi assegnati\n      const comandaData = {\n        tipo_comanda: formData.tipo_comanda,\n        responsabile: formData.responsabile,\n        note: formData.note || null\n      }\n\n      const response = await comandeApi.createComandaWithCavi(\n        cantiere.id_cantiere,\n        comandaData,\n        caviSelezionati\n      )\n\n      onSuccess(`Comanda ${response.data.codice_comanda} creata con successo per ${caviSelezionati.length} cavi`)\n      onClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la creazione della comanda'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <DialogContent className=\"sm:max-w-[600px]\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <ClipboardList className=\"h-5 w-5\" />\n            Crea Nuova Comanda\n          </DialogTitle>\n          <DialogDescription>\n            Crea una nuova comanda per {caviSelezionati.length} cavi selezionati\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {/* Cavi selezionati */}\n          <div className=\"p-4 bg-gray-50 rounded-lg\">\n            <Label className=\"text-sm font-medium\">Cavi Selezionati ({caviSelezionati.length})</Label>\n            <div className=\"mt-2 max-h-32 overflow-y-auto\">\n              <div className=\"flex flex-wrap gap-1\">\n                {caviSelezionati.slice(0, 10).map((cavoId) => (\n                  <span\n                    key={cavoId}\n                    className=\"inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded\"\n                  >\n                    {cavoId}\n                  </span>\n                ))}\n                {caviSelezionati.length > 10 && (\n                  <span className=\"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded\">\n                    +{caviSelezionati.length - 10} altri...\n                  </span>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          {/* Tipo comanda */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"tipo\">Tipo Comanda *</Label>\n            <Select\n              value={formData.tipo_comanda}\n              onValueChange={(value) => setFormData(prev => ({ ...prev, tipo_comanda: value }))}\n            >\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"POSA\">🔧 Posa Cavi</SelectItem>\n                <SelectItem value=\"COLLEGAMENTO_PARTENZA\">🔌 Collegamento Partenza</SelectItem>\n                <SelectItem value=\"COLLEGAMENTO_ARRIVO\">⚡ Collegamento Arrivo</SelectItem>\n                <SelectItem value=\"CERTIFICAZIONE\">📋 Certificazione</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Responsabile */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"responsabile\">Responsabile *</Label>\n            <Select\n              value={formData.responsabile}\n              onValueChange={(value) => setFormData(prev => ({ ...prev, responsabile: value }))}\n              disabled={loadingResponsabili}\n            >\n              <SelectTrigger>\n                <SelectValue placeholder=\"Seleziona responsabile...\" />\n              </SelectTrigger>\n              <SelectContent>\n                {responsabili.map((resp) => (\n                  <SelectItem key={resp.id} value={resp.nome_responsabile}>\n                    <div className=\"flex items-center gap-2\">\n                      <Users className=\"h-4 w-4\" />\n                      <span>{resp.nome_responsabile}</span>\n                      {resp.numero_telefono && (\n                        <span className=\"text-xs text-gray-500\">- {resp.numero_telefono}</span>\n                      )}\n                    </div>\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Note */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"note\">Note (opzionale)</Label>\n            <Textarea\n              id=\"note\"\n              placeholder=\"Inserisci eventuali note per la comanda...\"\n              value={formData.note}\n              onChange={(e) => setFormData(prev => ({ ...prev, note: e.target.value }))}\n              rows={3}\n            />\n          </div>\n\n          {/* Riepilogo */}\n          <div className=\"p-4 bg-blue-50 rounded-lg\">\n            <Label className=\"text-sm font-medium\">Riepilogo Comanda</Label>\n            <div className=\"mt-2 space-y-1 text-sm\">\n              <div><strong>Tipo:</strong> {getTipoComandaLabel(formData.tipo_comanda)}</div>\n              <div><strong>Responsabile:</strong> {formData.responsabile || 'Non selezionato'}</div>\n              <div><strong>Cavi:</strong> {caviSelezionati.length} selezionati</div>\n              {formData.note && <div><strong>Note:</strong> {formData.note}</div>}\n            </div>\n          </div>\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={onClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button\n            onClick={handleCreaComanda}\n            disabled={loading || !formData.responsabile || caviSelezionati.length === 0}\n          >\n            {loading ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <ClipboardList className=\"h-4 w-4 mr-2\" />}\n            Crea Comanda\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AAEA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;AAzBA;;;;;;;;;;;AA2Ce,SAAS,kBAAkB,EACxC,IAAI,EACJ,OAAO,EACP,eAAe,EACf,WAAW,EACX,SAAS,EACT,OAAO,EACgB;;IACvB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,cAAc,eAAe;QAC7B,cAAc;QACd,MAAM;IACR;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,MAAM;gBACR,YAAY;oBACV,cAAc,eAAe;oBAC7B,cAAc;oBACd,MAAM;gBACR;gBACA,SAAS;gBACT;YACF;QACF;sCAAG;QAAC;QAAM;KAAY;IAEtB,MAAM,mBAAmB;QACvB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,uBAAuB;YACvB,MAAM,WAAW,MAAM,oHAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,SAAS,WAAW;YAC3E,gBAAgB,SAAS,IAAI;QAC/B,EAAE,OAAO,OAAO;YACd,gBAAgB,EAAE;QACpB,SAAU;YACR,uBAAuB;QACzB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,UAAU;QAEf,IAAI,CAAC,SAAS,YAAY,EAAE;YAC1B,SAAS;YACT;QACF;QAEA,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,uCAAuC;YACvC,MAAM,cAAc;gBAClB,cAAc,SAAS,YAAY;gBACnC,cAAc,SAAS,YAAY;gBACnC,MAAM,SAAS,IAAI,IAAI;YACzB;YAEA,MAAM,WAAW,MAAM,oHAAA,CAAA,aAAU,CAAC,qBAAqB,CACrD,SAAS,WAAW,EACpB,aACA;YAGF,UAAU,CAAC,QAAQ,EAAE,SAAS,IAAI,CAAC,cAAc,CAAC,yBAAyB,EAAE,gBAAgB,MAAM,CAAC,KAAK,CAAC;YAC1G;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGvC,6LAAC,qIAAA,CAAA,oBAAiB;;gCAAC;gCACW,gBAAgB,MAAM;gCAAC;;;;;;;;;;;;;8BAIvD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,WAAU;;wCAAsB;wCAAmB,gBAAgB,MAAM;wCAAC;;;;;;;8CACjF,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;4CACZ,gBAAgB,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,uBACjC,6LAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;4CAMR,gBAAgB,MAAM,GAAG,oBACxB,6LAAC;gDAAK,WAAU;;oDAAmE;oDAC/E,gBAAgB,MAAM,GAAG;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;wBAOvC,uBACC,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC,oIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;sCAKvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAO;;;;;;8CACtB,6LAAC,qIAAA,CAAA,SAAM;oCACL,OAAO,SAAS,YAAY;oCAC5B,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,cAAc;4CAAM,CAAC;;sDAE/E,6LAAC,qIAAA,CAAA,gBAAa;sDACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sDAEd,6LAAC,qIAAA,CAAA,gBAAa;;8DACZ,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAO;;;;;;8DACzB,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAwB;;;;;;8DAC1C,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAsB;;;;;;8DACxC,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAiB;;;;;;;;;;;;;;;;;;;;;;;;sCAMzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAe;;;;;;8CAC9B,6LAAC,qIAAA,CAAA,SAAM;oCACL,OAAO,SAAS,YAAY;oCAC5B,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,cAAc;4CAAM,CAAC;oCAC/E,UAAU;;sDAEV,6LAAC,qIAAA,CAAA,gBAAa;sDACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,6LAAC,qIAAA,CAAA,gBAAa;sDACX,aAAa,GAAG,CAAC,CAAC,qBACjB,6LAAC,qIAAA,CAAA,aAAU;oDAAe,OAAO,KAAK,iBAAiB;8DACrD,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;0EAAM,KAAK,iBAAiB;;;;;;4DAC5B,KAAK,eAAe,kBACnB,6LAAC;gEAAK,WAAU;;oEAAwB;oEAAG,KAAK,eAAe;;;;;;;;;;;;;mDALpD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;sCAehC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAO;;;;;;8CACtB,6LAAC,uIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,aAAY;oCACZ,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACvE,MAAM;;;;;;;;;;;;sCAKV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAAc;gDAAE,oBAAoB,SAAS,YAAY;;;;;;;sDACtE,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAAsB;gDAAE,SAAS,YAAY,IAAI;;;;;;;sDAC9D,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAAc;gDAAE,gBAAgB,MAAM;gDAAC;;;;;;;wCACnD,SAAS,IAAI,kBAAI,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAAc;gDAAE,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;8BAKlE,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAS,UAAU;sCAAS;;;;;;sCAG/D,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,WAAW,CAAC,SAAS,YAAY,IAAI,gBAAgB,MAAM,KAAK;;gCAEzE,wBAAU,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAAiC,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAOtH;GArOwB;;QAQD,kIAAA,CAAA,UAAO;;;KARN", "debugId": null}}, {"offset": {"line": 11317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/ImportExcelDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef } from 'react'\nimport {\n  <PERSON><PERSON>,\n  <PERSON>alogContent,\n  DialogDescription,\n  DialogFooter,\n  <PERSON>alogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Upload, FileSpreadsheet, CheckCircle } from 'lucide-react'\nimport { excelApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface ImportExcelDialogProps {\n  open: boolean\n  onClose: () => void\n  tipo: 'cavi' | 'bobine'\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function ImportExcelDialog({\n  open,\n  onClose,\n  tipo,\n  onSuccess,\n  onError\n}: ImportExcelDialogProps) {\n  const { cantiere } = useAuth()\n  const [file, setFile] = useState<File | null>(null)\n  const [revisione, setRevisione] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [uploadProgress, setUploadProgress] = useState(0)\n  const fileInputRef = useRef<HTMLInputElement>(null)\n\n  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const selectedFile = event.target.files?.[0]\n    if (selectedFile) {\n      // Verifica che sia un file Excel\n      const validTypes = [\n        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n        'application/vnd.ms-excel'\n      ]\n      \n      if (!validTypes.includes(selectedFile.type) && \n          !selectedFile.name.toLowerCase().endsWith('.xlsx') && \n          !selectedFile.name.toLowerCase().endsWith('.xls')) {\n        setError('Seleziona un file Excel valido (.xlsx o .xls)')\n        return\n      }\n\n      setFile(selectedFile)\n      setError('')\n    }\n  }\n\n  const handleImport = async () => {\n    if (!file || !cantiere) return\n\n    if (tipo === 'cavi' && !revisione.trim()) {\n      setError('Inserisci il codice revisione per l\\'importazione cavi')\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n      setUploadProgress(0)\n\n      let response\n      if (tipo === 'cavi') {\n        response = await excelApi.importCavi(cantiere.id_cantiere, file, revisione.trim())\n      } else {\n        response = await excelApi.importBobine(cantiere.id_cantiere, file)\n      }\n\n      setUploadProgress(100)\n\n      if (response.data.success) {\n        const details = response.data.details\n        let message = response.data.message\n        \n        if (tipo === 'cavi' && details?.cavi_importati) {\n          message += ` (${details.cavi_importati} cavi importati)`\n        } else if (tipo === 'bobine' && details?.bobine_importate) {\n          message += ` (${details.bobine_importate} bobine importate)`\n        }\n\n        onSuccess(message)\n        onClose()\n      } else {\n        onError(response.data.message || 'Errore durante l\\'importazione')\n      }\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\\'importazione del file'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n      setUploadProgress(0)\n    }\n  }\n\n  const handleClose = () => {\n    if (!loading) {\n      setFile(null)\n      setRevisione('')\n      setError('')\n      setUploadProgress(0)\n      if (fileInputRef.current) {\n        fileInputRef.current.value = ''\n      }\n      onClose()\n    }\n  }\n\n  const getTipoLabel = () => {\n    return tipo === 'cavi' ? 'Cavi' : 'Bobine'\n  }\n\n  const getFileRequirements = () => {\n    if (tipo === 'cavi') {\n      return [\n        'File Excel (.xlsx o .xls)',\n        'Colonne richieste: ID_CAVO, SISTEMA, UTILITY, TIPOLOGIA, ecc.',\n        'Prima riga deve contenere le intestazioni',\n        'Codice revisione obbligatorio per tracciabilità'\n      ]\n    } else {\n      return [\n        'File Excel (.xlsx o .xls)',\n        'Colonne richieste: NUMERO_BOBINA, UTILITY, TIPOLOGIA, METRI_TOTALI, ecc.',\n        'Prima riga deve contenere le intestazioni',\n        'I metri residui saranno impostati uguali ai metri totali'\n      ]\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"sm:max-w-[600px]\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Upload className=\"h-5 w-5\" />\n            Importa {getTipoLabel()} da Excel\n          </DialogTitle>\n          <DialogDescription>\n            Carica un file Excel per importare {getTipoLabel().toLowerCase()} nel cantiere\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {/* Requisiti file */}\n          <div className=\"p-4 bg-blue-50 rounded-lg\">\n            <Label className=\"text-sm font-medium\">Requisiti File</Label>\n            <ul className=\"mt-2 space-y-1 text-sm text-gray-600\">\n              {getFileRequirements().map((req, index) => (\n                <li key={index} className=\"flex items-start gap-2\">\n                  <span className=\"text-blue-500 mt-0.5\">•</span>\n                  <span>{req}</span>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          {/* Selezione file */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"file\">File Excel *</Label>\n            <div className=\"flex items-center gap-2\">\n              <Input\n                ref={fileInputRef}\n                id=\"file\"\n                type=\"file\"\n                accept=\".xlsx,.xls\"\n                onChange={handleFileSelect}\n                disabled={loading}\n                className=\"flex-1\"\n              />\n              {file && (\n                <div className=\"flex items-center gap-1 text-green-600\">\n                  <CheckCircle className=\"h-4 w-4\" />\n                  <span className=\"text-sm\">File selezionato</span>\n                </div>\n              )}\n            </div>\n            {file && (\n              <div className=\"text-sm text-gray-600\">\n                <FileSpreadsheet className=\"h-4 w-4 inline mr-1\" />\n                {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)\n              </div>\n            )}\n          </div>\n\n          {/* Revisione (solo per cavi) */}\n          {tipo === 'cavi' && (\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"revisione\">Codice Revisione *</Label>\n              <Input\n                id=\"revisione\"\n                value={revisione}\n                onChange={(e) => setRevisione(e.target.value)}\n                placeholder=\"es. REV001, V1.0, 2024-01\"\n                disabled={loading}\n              />\n              <p className=\"text-sm text-gray-500\">\n                Codice identificativo della revisione per tracciabilità delle modifiche\n              </p>\n            </div>\n          )}\n\n          {/* Progress bar */}\n          {loading && (\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center gap-2\">\n                <Loader2 className=\"h-4 w-4 animate-spin\" />\n                <span className=\"text-sm\">Caricamento in corso...</span>\n              </div>\n              <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                <div \n                  className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                  style={{ width: `${uploadProgress}%` }}\n                ></div>\n              </div>\n            </div>\n          )}\n\n          {/* Riepilogo */}\n          {file && (\n            <div className=\"p-4 bg-gray-50 rounded-lg\">\n              <Label className=\"text-sm font-medium\">Riepilogo Importazione</Label>\n              <div className=\"mt-2 space-y-1 text-sm\">\n                <div><strong>Tipo:</strong> {getTipoLabel()}</div>\n                <div><strong>File:</strong> {file.name}</div>\n                <div><strong>Dimensione:</strong> {(file.size / 1024 / 1024).toFixed(2)} MB</div>\n                {tipo === 'cavi' && revisione && (\n                  <div><strong>Revisione:</strong> {revisione}</div>\n                )}\n                <div><strong>Cantiere:</strong> {cantiere?.nome_cantiere}</div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={handleClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button\n            onClick={handleImport}\n            disabled={loading || !file || (tipo === 'cavi' && !revisione.trim())}\n          >\n            {loading ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <Upload className=\"h-4 w-4 mr-2\" />}\n            Importa {getTipoLabel()}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AAjBA;;;;;;;;;;AA2Be,SAAS,kBAAkB,EACxC,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,SAAS,EACT,OAAO,EACgB;;IACvB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,mBAAmB,CAAC;QACxB,MAAM,eAAe,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAC5C,IAAI,cAAc;YAChB,iCAAiC;YACjC,MAAM,aAAa;gBACjB;gBACA;aACD;YAED,IAAI,CAAC,WAAW,QAAQ,CAAC,aAAa,IAAI,KACtC,CAAC,aAAa,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAC1C,CAAC,aAAa,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS;gBACrD,SAAS;gBACT;YACF;YAEA,QAAQ;YACR,SAAS;QACX;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ,CAAC,UAAU;QAExB,IAAI,SAAS,UAAU,CAAC,UAAU,IAAI,IAAI;YACxC,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YACT,kBAAkB;YAElB,IAAI;YACJ,IAAI,SAAS,QAAQ;gBACnB,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,SAAS,WAAW,EAAE,MAAM,UAAU,IAAI;YACjF,OAAO;gBACL,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,YAAY,CAAC,SAAS,WAAW,EAAE;YAC/D;YAEA,kBAAkB;YAElB,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACzB,MAAM,UAAU,SAAS,IAAI,CAAC,OAAO;gBACrC,IAAI,UAAU,SAAS,IAAI,CAAC,OAAO;gBAEnC,IAAI,SAAS,UAAU,SAAS,gBAAgB;oBAC9C,WAAW,CAAC,EAAE,EAAE,QAAQ,cAAc,CAAC,gBAAgB,CAAC;gBAC1D,OAAO,IAAI,SAAS,YAAY,SAAS,kBAAkB;oBACzD,WAAW,CAAC,EAAE,EAAE,QAAQ,gBAAgB,CAAC,kBAAkB,CAAC;gBAC9D;gBAEA,UAAU;gBACV;YACF,OAAO;gBACL,QAAQ,SAAS,IAAI,CAAC,OAAO,IAAI;YACnC;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;YACX,kBAAkB;QACpB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ,QAAQ;YACR,aAAa;YACb,SAAS;YACT,kBAAkB;YAClB,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,KAAK,GAAG;YAC/B;YACA;QACF;IACF;IAEA,MAAM,eAAe;QACnB,OAAO,SAAS,SAAS,SAAS;IACpC;IAEA,MAAM,sBAAsB;QAC1B,IAAI,SAAS,QAAQ;YACnB,OAAO;gBACL;gBACA;gBACA;gBACA;aACD;QACH,OAAO;YACL,OAAO;gBACL;gBACA;gBACA;gBACA;aACD;QACH;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;gCACrB;gCAAe;;;;;;;sCAE1B,6LAAC,qIAAA,CAAA,oBAAiB;;gCAAC;gCACmB,eAAe,WAAW;gCAAG;;;;;;;;;;;;;8BAIrE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,6LAAC;oCAAG,WAAU;8CACX,sBAAsB,GAAG,CAAC,CAAC,KAAK,sBAC/B,6LAAC;4CAAe,WAAU;;8DACxB,6LAAC;oDAAK,WAAU;8DAAuB;;;;;;8DACvC,6LAAC;8DAAM;;;;;;;2CAFA;;;;;;;;;;;;;;;;wBAQd,uBACC,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC,oIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;sCAKvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAO;;;;;;8CACtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CACJ,KAAK;4CACL,IAAG;4CACH,MAAK;4CACL,QAAO;4CACP,UAAU;4CACV,UAAU;4CACV,WAAU;;;;;;wCAEX,sBACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;gCAI/B,sBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+NAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;wCAC1B,KAAK,IAAI;wCAAC;wCAAG,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;wCAAG;;;;;;;;;;;;;wBAMxD,SAAS,wBACR,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAY;;;;;;8CAC3B,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC5C,aAAY;oCACZ,UAAU;;;;;;8CAEZ,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;wBAOxC,yBACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;8CAE5B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,GAAG,eAAe,CAAC,CAAC;wCAAC;;;;;;;;;;;;;;;;;wBAO5C,sBACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAAc;gDAAE;;;;;;;sDAC7B,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAAc;gDAAE,KAAK,IAAI;;;;;;;sDACtC,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAAoB;gDAAE,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;gDAAG;;;;;;;wCACvE,SAAS,UAAU,2BAClB,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAAmB;gDAAE;;;;;;;sDAEpC,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAAkB;gDAAE,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;8BAMnD,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAa,UAAU;sCAAS;;;;;;sCAGnE,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,WAAW,CAAC,QAAS,SAAS,UAAU,CAAC,UAAU,IAAI;;gCAEhE,wBAAU,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAAiC,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAkB;gCAC1F;;;;;;;;;;;;;;;;;;;;;;;;AAMrB;GApPwB;;QAOD,kIAAA,CAAA,UAAO;;;KAPN", "debugId": null}}, {"offset": {"line": 11918, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/ExportDataDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Download, FileSpreadsheet, Database } from 'lucide-react'\nimport { excelApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface ExportDataDialogProps {\n  open: boolean\n  onClose: () => void\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function ExportDataDialog({\n  open,\n  onClose,\n  onSuccess,\n  onError\n}: ExportDataDialogProps) {\n  const { cantiere } = useAuth()\n  const [selectedExports, setSelectedExports] = useState({\n    cavi: true,\n    bobine: true,\n    comande: false,\n    certificazioni: false,\n    responsabili: false\n  })\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const handleExportChange = (exportType: string, checked: boolean) => {\n    setSelectedExports(prev => ({\n      ...prev,\n      [exportType]: checked\n    }))\n  }\n\n  const handleExportCavi = async () => {\n    if (!cantiere) return\n\n    try {\n      setLoading(true)\n      const response = await excelApi.exportCavi(cantiere.id_cantiere)\n      \n      // Crea un link per il download\n      const url = window.URL.createObjectURL(new Blob([response.data]))\n      const link = document.createElement('a')\n      link.href = url\n      link.setAttribute('download', `cavi_${cantiere.nome_cantiere}_${new Date().toISOString().split('T')[0]}.xlsx`)\n      document.body.appendChild(link)\n      link.click()\n      link.remove()\n      window.URL.revokeObjectURL(url)\n\n      onSuccess('Export cavi completato con successo')\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\\'export dei cavi'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleExportBobine = async () => {\n    if (!cantiere) return\n\n    try {\n      setLoading(true)\n      const response = await excelApi.exportBobine(cantiere.id_cantiere)\n      \n      // Crea un link per il download\n      const url = window.URL.createObjectURL(new Blob([response.data]))\n      const link = document.createElement('a')\n      link.href = url\n      link.setAttribute('download', `bobine_${cantiere.nome_cantiere}_${new Date().toISOString().split('T')[0]}.xlsx`)\n      document.body.appendChild(link)\n      link.click()\n      link.remove()\n      window.URL.revokeObjectURL(url)\n\n      onSuccess('Export bobine completato con successo')\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\\'export delle bobine'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleExportAll = async () => {\n    if (!cantiere) return\n\n    try {\n      setLoading(true)\n      setError('')\n\n      const exports = []\n\n      if (selectedExports.cavi) {\n        exports.push(handleExportCavi())\n      }\n\n      if (selectedExports.bobine) {\n        exports.push(handleExportBobine())\n      }\n\n      // TODO: Implementare export per comande, certificazioni, responsabili\n      if (selectedExports.comande) {\n      }\n\n      if (selectedExports.certificazioni) {\n      }\n\n      if (selectedExports.responsabili) {\n      }\n\n      await Promise.all(exports)\n\n      const exportCount = Object.values(selectedExports).filter(Boolean).length\n      onSuccess(`Export completato: ${exportCount} file scaricati`)\n      onClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\\'export dei dati'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const getSelectedCount = () => {\n    return Object.values(selectedExports).filter(Boolean).length\n  }\n\n  const exportOptions = [\n    {\n      key: 'cavi',\n      label: 'Cavi',\n      description: 'Esporta tutti i cavi del cantiere con stato, collegamenti e certificazioni',\n      icon: <Database className=\"h-4 w-4\" />,\n      available: true\n    },\n    {\n      key: 'bobine',\n      label: 'Bobine',\n      description: 'Esporta tutte le bobine del parco cavi con metri residui e assegnazioni',\n      icon: <FileSpreadsheet className=\"h-4 w-4\" />,\n      available: true\n    },\n    {\n      key: 'comande',\n      label: 'Comande',\n      description: 'Esporta tutte le comande con cavi assegnati e responsabili',\n      icon: <FileSpreadsheet className=\"h-4 w-4\" />,\n      available: false\n    },\n    {\n      key: 'certificazioni',\n      label: 'Certificazioni',\n      description: 'Esporta tutte le certificazioni con esiti e responsabili',\n      icon: <FileSpreadsheet className=\"h-4 w-4\" />,\n      available: false\n    },\n    {\n      key: 'responsabili',\n      label: 'Responsabili',\n      description: 'Esporta tutti i responsabili con contatti e ruoli',\n      icon: <FileSpreadsheet className=\"h-4 w-4\" />,\n      available: false\n    }\n  ]\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <DialogContent className=\"sm:max-w-[600px]\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Download className=\"h-5 w-5\" />\n            Esporta Dati Cantiere\n          </DialogTitle>\n          <DialogDescription>\n            Seleziona i dati da esportare dal cantiere {cantiere?.nome_cantiere}\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          {/* Opzioni di export */}\n          <div className=\"space-y-3\">\n            <Label className=\"text-sm font-medium\">Seleziona Dati da Esportare</Label>\n            \n            {exportOptions.map((option) => (\n              <div\n                key={option.key}\n                className={`flex items-start space-x-3 p-3 rounded-lg border ${\n                  option.available ? 'bg-white' : 'bg-gray-50'\n                }`}\n              >\n                <Checkbox\n                  id={option.key}\n                  checked={selectedExports[option.key as keyof typeof selectedExports]}\n                  onCheckedChange={(checked) => handleExportChange(option.key, checked as boolean)}\n                  disabled={!option.available || loading}\n                />\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center gap-2\">\n                    {option.icon}\n                    <Label\n                      htmlFor={option.key}\n                      className={`font-medium ${!option.available ? 'text-gray-500' : ''}`}\n                    >\n                      {option.label}\n                      {!option.available && (\n                        <span className=\"ml-2 text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded\">\n                          In sviluppo\n                        </span>\n                      )}\n                    </Label>\n                  </div>\n                  <p className={`text-sm mt-1 ${!option.available ? 'text-gray-400' : 'text-gray-600'}`}>\n                    {option.description}\n                  </p>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* Informazioni export */}\n          <div className=\"p-4 bg-blue-50 rounded-lg\">\n            <Label className=\"text-sm font-medium\">Informazioni Export</Label>\n            <ul className=\"mt-2 space-y-1 text-sm text-gray-600\">\n              <li>• I file saranno scaricati in formato Excel (.xlsx)</li>\n              <li>• I nomi file includeranno data e nome cantiere</li>\n              <li>• I dati esportati riflettono lo stato attuale del database</li>\n              <li>• L'export non modifica i dati originali</li>\n            </ul>\n          </div>\n\n          {/* Riepilogo */}\n          {getSelectedCount() > 0 && (\n            <div className=\"p-4 bg-gray-50 rounded-lg\">\n              <Label className=\"text-sm font-medium\">Riepilogo Export</Label>\n              <div className=\"mt-2 space-y-1 text-sm\">\n                <div><strong>Cantiere:</strong> {cantiere?.nome_cantiere}</div>\n                <div><strong>File da scaricare:</strong> {getSelectedCount()}</div>\n                <div><strong>Data export:</strong> {new Date().toLocaleDateString('it-IT')}</div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={onClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button\n            onClick={handleExportAll}\n            disabled={loading || getSelectedCount() === 0}\n          >\n            {loading ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <Download className=\"h-4 w-4 mr-2\" />}\n            Esporta {getSelectedCount() > 0 ? `(${getSelectedCount()})` : ''}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AAjBA;;;;;;;;;;AA0Be,SAAS,iBAAiB,EACvC,IAAI,EACJ,OAAO,EACP,SAAS,EACT,OAAO,EACe;;IACtB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrD,MAAM;QACN,QAAQ;QACR,SAAS;QACT,gBAAgB;QAChB,cAAc;IAChB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,qBAAqB,CAAC,YAAoB;QAC9C,mBAAmB,CAAA,OAAQ,CAAC;gBAC1B,GAAG,IAAI;gBACP,CAAC,WAAW,EAAE;YAChB,CAAC;IACH;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,SAAS,WAAW;YAE/D,+BAA+B;YAC/B,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC,IAAI,KAAK;gBAAC,SAAS,IAAI;aAAC;YAC/D,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,YAAY,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,aAAa,CAAC,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YAC7G,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,KAAK,MAAM;YACX,OAAO,GAAG,CAAC,eAAe,CAAC;YAE3B,UAAU;QACZ,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,YAAY,CAAC,SAAS,WAAW;YAEjE,+BAA+B;YAC/B,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC,IAAI,KAAK;gBAAC,SAAS,IAAI;aAAC;YAC/D,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,YAAY,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,aAAa,CAAC,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YAC/G,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,KAAK,MAAM;YACX,OAAO,GAAG,CAAC,eAAe,CAAC;YAE3B,UAAU;QACZ,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,UAAU,EAAE;YAElB,IAAI,gBAAgB,IAAI,EAAE;gBACxB,QAAQ,IAAI,CAAC;YACf;YAEA,IAAI,gBAAgB,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC;YACf;YAEA,sEAAsE;YACtE,IAAI,gBAAgB,OAAO,EAAE,CAC7B;YAEA,IAAI,gBAAgB,cAAc,EAAE,CACpC;YAEA,IAAI,gBAAgB,YAAY,EAAE,CAClC;YAEA,MAAM,QAAQ,GAAG,CAAC;YAElB,MAAM,cAAc,OAAO,MAAM,CAAC,iBAAiB,MAAM,CAAC,SAAS,MAAM;YACzE,UAAU,CAAC,mBAAmB,EAAE,YAAY,eAAe,CAAC;YAC5D;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,OAAO,OAAO,MAAM,CAAC,iBAAiB,MAAM,CAAC,SAAS,MAAM;IAC9D;IAEA,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,WAAW;QACb;QACA;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,+NAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,WAAW;QACb;QACA;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,+NAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,WAAW;QACb;QACA;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,+NAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,WAAW;QACb;QACA;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,+NAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,WAAW;QACb;KACD;IAED,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGlC,6LAAC,qIAAA,CAAA,oBAAiB;;gCAAC;gCAC2B,UAAU;;;;;;;;;;;;;8BAI1D,6LAAC;oBAAI,WAAU;;wBACZ,uBACC,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC,oIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;sCAKvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;gCAEtC,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC;wCAEC,WAAW,CAAC,iDAAiD,EAC3D,OAAO,SAAS,GAAG,aAAa,cAChC;;0DAEF,6LAAC,uIAAA,CAAA,WAAQ;gDACP,IAAI,OAAO,GAAG;gDACd,SAAS,eAAe,CAAC,OAAO,GAAG,CAAiC;gDACpE,iBAAiB,CAAC,UAAY,mBAAmB,OAAO,GAAG,EAAE;gDAC7D,UAAU,CAAC,OAAO,SAAS,IAAI;;;;;;0DAEjC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DACZ,OAAO,IAAI;0EACZ,6LAAC,oIAAA,CAAA,QAAK;gEACJ,SAAS,OAAO,GAAG;gEACnB,WAAW,CAAC,YAAY,EAAE,CAAC,OAAO,SAAS,GAAG,kBAAkB,IAAI;;oEAEnE,OAAO,KAAK;oEACZ,CAAC,OAAO,SAAS,kBAChB,6LAAC;wEAAK,WAAU;kFAA2D;;;;;;;;;;;;;;;;;;kEAMjF,6LAAC;wDAAE,WAAW,CAAC,aAAa,EAAE,CAAC,OAAO,SAAS,GAAG,kBAAkB,iBAAiB;kEAClF,OAAO,WAAW;;;;;;;;;;;;;uCA3BlB,OAAO,GAAG;;;;;;;;;;;sCAmCrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;wBAKP,qBAAqB,mBACpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAAkB;gDAAE,UAAU;;;;;;;sDAC3C,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAA2B;gDAAE;;;;;;;sDAC1C,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAAqB;gDAAE,IAAI,OAAO,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;8BAM1E,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAS,UAAU;sCAAS;;;;;;sCAG/D,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,WAAW,uBAAuB;;gCAE3C,wBAAU,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAAiC,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAkB;gCAC5F,qBAAqB,IAAI,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC,GAAG;;;;;;;;;;;;;;;;;;;;;;;;AAM1E;GAlQwB;;QAMD,kIAAA,CAAA,UAAO;;;KANN", "debugId": null}}, {"offset": {"line": 12468, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/cavi/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useCantiere } from '@/hooks/useCantiere'\nimport { CantiereErrorBoundary } from '@/components/cantiere/CantiereErrorBoundary'\nimport { caviApi } from '@/lib/api'\nimport { Cavo } from '@/types'\nimport CaviTable from '@/components/cavi/CaviTable'\nimport CaviStatistics from '@/components/cavi/CaviStatistics'\nimport InserisciMetriDialog from '@/components/cavi/InserisciMetriDialog'\n// Import dei nuovi componenti modali migliorati\nimport {\n  DisconnectCableModal,\n  GeneratePdfModal,\n  CertificationModal,\n  CertificationErrorModal,\n  SuccessToast\n} from '@/components/cavi/modals/CableActionModals'\nimport {\n  ModificaBobinaModal,\n  InserisciMetriModal\n} from '@/components/cavi/modals/BobinaManagementModals'\n// Import CSS per i modali migliorati\nimport '@/components/cavi/modals/enhanced-modals.css'\n// Import del vecchio dialog per InserisciMetri (temporaneo)\nimport InserisciMetriDialogOld from '@/components/cavi/InserisciMetriDialog'\nimport CreaComandaDialog from '@/components/cavi/CreaComandaDialog'\nimport ImportExcelDialog from '@/components/cavi/ImportExcelDialog'\nimport ExportDataDialog from '@/components/cavi/ExportDataDialog'\n// import { useToast } from '@/hooks/use-toast'\nimport {\n  Package,\n  AlertCircle,\n  Loader2\n} from 'lucide-react'\n\ninterface DashboardStats {\n  totali: number\n  installati: number\n  collegati: number\n  certificati: number\n  percentualeInstallazione: number\n  percentualeCollegamento: number\n  percentualeCertificazione: number\n  metriTotali: number\n  metriInstallati: number\n  metriCollegati: number\n  metriCertificati: number\n}\n\nexport default function CaviPage() {\n  const { user, isAuthenticated, isLoading: authLoading } = useAuth()\n  const { cantiereId, cantiere, isValidCantiere, isLoading: cantiereLoading, error: cantiereError } = useCantiere()\n  const router = useRouter()\n\n\n\n  // Sistema toast semplice\n  const toast = ({ title, description, variant }: { title: string, description: string, variant?: string }) => {\n    // TODO: Implementare sistema toast visuale\n  }\n  const [cavi, setCavi] = useState<Cavo[]>([])\n  const [caviSpare, setCaviSpare] = useState<Cavo[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState('')\n  const [selectedCavi, setSelectedCavi] = useState<string[]>([])\n  const [selectionEnabled, setSelectionEnabled] = useState(true)\n  const [filteredCavi, setFilteredCavi] = useState<Cavo[]>([])\n  const [revisioneCorrente, setRevisioneCorrente] = useState<string>('')\n\n  // Update filtered cavi when main cavi change\n  useEffect(() => {\n    setFilteredCavi(cavi)\n  }, [cavi])\n\n  // Stati per i dialoghi\n  const [inserisciMetriDialog, setInserisciMetriDialog] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n\n  const [modificaBobinaDialog, setModificaBobinaDialog] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n\n  const [collegamentiDialog, setCollegamentiDialog] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n\n  // Stati per i nuovi modali migliorati\n  const [disconnectModal, setDisconnectModal] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n\n  const [generatePdfModal, setGeneratePdfModal] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n\n  const [certificationModal, setCertificationModal] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n\n  const [certificationErrorModal, setCertificationErrorModal] = useState<{\n    open: boolean\n    cavo: Cavo | null\n    error: string\n  }>({ open: false, cavo: null, error: '' })\n\n  const [successToast, setSuccessToast] = useState<{\n    visible: boolean\n    message: string\n  }>({ visible: false, message: '' })\n\n  const [creaComandaDialog, setCreaComandaDialog] = useState<{\n    open: boolean\n    tipoComanda?: 'POSA' | 'COLLEGAMENTO_PARTENZA' | 'COLLEGAMENTO_ARRIVO' | 'CERTIFICAZIONE'\n  }>({ open: false })\n\n  const [importExcelDialog, setImportExcelDialog] = useState<{\n    open: boolean\n    tipo?: 'cavi' | 'bobine'\n  }>({ open: false })\n\n  const [exportDataDialog, setExportDataDialog] = useState(false)\n  const [stats, setStats] = useState<DashboardStats>({\n    totali: 0,\n    installati: 0,\n    collegati: 0,\n    certificati: 0,\n    percentualeInstallazione: 0,\n    percentualeCollegamento: 0,\n    percentualeCertificazione: 0,\n    metriTotali: 0,\n    metriInstallati: 0,\n    metriCollegati: 0,\n    metriCertificati: 0\n  })\n\n  useEffect(() => {\n    if (!authLoading && !isAuthenticated) {\n      router.push('/login')\n    }\n  }, [isAuthenticated, authLoading, router])\n\n  // Crea oggetto cantiere per il dialog\n  const cantiereForDialog = cantiere || (cantiereId && cantiereId > 0 ? {\n    id_cantiere: cantiereId,\n    commessa: `Cantiere ${cantiereId}`\n  } : null)\n\n  // Carica i cavi dal backend - MIGLIORATO con nuovo hook\n  useEffect(() => {\n    if (isValidCantiere && cantiereId && cantiereId > 0 && !cantiereLoading) {\n\n      loadCavi()\n      loadRevisioneCorrente()\n    } else if (!cantiereLoading && !isValidCantiere) {\n\n      setCavi([])\n      setCaviSpare([])\n      setError(cantiereError || 'Nessun cantiere selezionato')\n    }\n  }, [cantiereId, isValidCantiere, cantiereLoading, cantiereError])\n\n  const loadRevisioneCorrente = async () => {\n    try {\n      const response = await fetch(`http://localhost:8001/api/cavi/${cantiereId}/revisione-corrente`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        setRevisioneCorrente(data.revisione_corrente || '00')\n      } else {\n        setRevisioneCorrente('00')\n      }\n    } catch (error) {\n      setRevisioneCorrente('00')\n    }\n  }\n\n  const loadCavi = async () => {\n    try {\n      setLoading(true)\n      setError('')\n\n      // Prima prova con l'API normale\n      try {\n        const data = await caviApi.getCavi(cantiereId!)\n\n        // Separa cavi attivi e spare\n        const caviAttivi = data.filter((cavo: Cavo) => !cavo.spare)\n        const caviSpareFiltered = data.filter((cavo: Cavo) => cavo.spare)\n\n        setCavi(caviAttivi)\n        setCaviSpare(caviSpareFiltered)\n\n        // Calcola statistiche\n        calculateStats(caviAttivi)\n\n      } catch (apiError: any) {\n        throw apiError\n      }\n\n    } catch (error: any) {\n      setError(`Errore nel caricamento dei cavi: ${error.response?.data?.detail || error.message}`)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const calculateStats = (caviData: Cavo[]) => {\n    const totali = caviData.length\n    const installati = caviData.filter(c => (c.metri_posati || c.metratura_reale || 0) > 0).length\n    const collegati = caviData.filter(c => (c.collegamento || c.collegamenti) === 3).length // 3 = collegato\n    const certificati = caviData.filter(c => c.certificato).length\n\n    const metriTotali = caviData.reduce((sum, c) => sum + (c.metri_teorici || 0), 0)\n    const metriInstallati = caviData.reduce((sum, c) => sum + (c.metri_posati || 0), 0)\n    const metriCollegati = caviData.filter(c => c.collegamento === 3).reduce((sum, c) => sum + (c.metri_posati || 0), 0)\n    const metriCertificati = caviData.filter(c => c.certificato).reduce((sum, c) => sum + (c.metri_posati || 0), 0)\n\n    setStats({\n      totali,\n      installati,\n      collegati,\n      certificati,\n      percentualeInstallazione: totali > 0 ? Math.round((installati / totali) * 100) : 0,\n      percentualeCollegamento: totali > 0 ? Math.round((collegati / totali) * 100) : 0,\n      percentualeCertificazione: totali > 0 ? Math.round((certificati / totali) * 100) : 0,\n      metriTotali,\n      metriInstallati,\n      metriCollegati,\n      metriCertificati\n    })\n  }\n\n  // Gestione azioni sui cavi con i nuovi modali migliorati\n  const handleStatusAction = (cavo: Cavo, action: string, label?: string) => {\n\n    switch (action) {\n      case 'insert_meters':\n        setInserisciMetriDialog({ open: true, cavo })\n        break\n      case 'modify_reel':\n        setModificaBobinaDialog({ open: true, cavo })\n        break\n      case 'view_command':\n        toast({\n          title: \"Visualizza Comanda\",\n          description: `Apertura comanda ${label} per cavo ${cavo.id_cavo}`,\n        })\n        break\n      case 'connect_cable':\n      case 'connect_arrival':\n      case 'connect_departure':\n      case 'manage_connections':\n        // Usa il nuovo modal di certificazione per gestione collegamenti\n        setCertificationModal({ open: true, cavo })\n        break\n      case 'disconnect_cable':\n        // Usa il nuovo modal di disconnessione\n        setDisconnectModal({ open: true, cavo })\n        break\n      case 'create_certificate':\n        // Usa il nuovo modal di certificazione\n        setCertificationModal({ open: true, cavo })\n        break\n      case 'generate_pdf':\n        // Usa il nuovo modal di generazione PDF\n        setGeneratePdfModal({ open: true, cavo })\n        break\n    }\n  }\n\n  const handleContextMenuAction = (cavo: Cavo, action: string) => {\n\n    switch (action) {\n      case 'view_details':\n        toast({\n          title: \"Visualizza Dettagli\",\n          description: `Apertura dettagli per cavo ${cavo.id_cavo}`,\n        })\n        break\n      case 'edit':\n        toast({\n          title: \"Modifica Cavo\",\n          description: \"Funzione modifica cavo in sviluppo\",\n        })\n        break\n      case 'delete':\n        toast({\n          title: \"Elimina Cavo\",\n          description: \"Funzione eliminazione cavo in sviluppo\",\n          variant: \"destructive\"\n        })\n        break\n      case 'add_new':\n        toast({\n          title: \"Aggiungi Nuovo Cavo\",\n          description: \"Funzione aggiunta nuovo cavo in sviluppo\",\n        })\n        break\n      case 'select':\n        const isSelected = selectedCavi.includes(cavo.id_cavo)\n        if (isSelected) {\n          setSelectedCavi(selectedCavi.filter(id => id !== cavo.id_cavo))\n          toast({\n            title: \"Cavo Deselezionato\",\n            description: `Cavo ${cavo.id_cavo} deselezionato`,\n          })\n        } else {\n          setSelectedCavi([...selectedCavi, cavo.id_cavo])\n          toast({\n            title: \"Cavo Selezionato\",\n            description: `Cavo ${cavo.id_cavo} selezionato`,\n          })\n        }\n        break\n      case 'copy_id':\n        navigator.clipboard.writeText(cavo.id_cavo)\n        toast({\n          title: \"ID Copiato\",\n          description: `ID cavo ${cavo.id_cavo} copiato negli appunti`,\n        })\n        break\n      case 'copy_details':\n        const details = `ID: ${cavo.id_cavo}, Tipologia: ${cavo.tipologia}, Formazione: ${cavo.formazione || cavo.sezione}, Metri: ${cavo.metri_teorici}`\n        navigator.clipboard.writeText(details)\n        toast({\n          title: \"Dettagli Copiati\",\n          description: \"Dettagli cavo copiati negli appunti\",\n        })\n        break\n      case 'add_to_command':\n        toast({\n          title: \"Aggiungi a Comanda\",\n          description: \"Funzione aggiunta a comanda in sviluppo\",\n        })\n        break\n      case 'remove_from_command':\n        toast({\n          title: \"Rimuovi da Comanda\",\n          description: \"Funzione rimozione da comanda in sviluppo\",\n        })\n        break\n      case 'create_command_posa':\n        setCreaComandaDialog({ open: true, tipoComanda: 'POSA' })\n        break\n      case 'create_command_collegamento_partenza':\n        setCreaComandaDialog({ open: true, tipoComanda: 'COLLEGAMENTO_PARTENZA' })\n        break\n      case 'create_command_collegamento_arrivo':\n        setCreaComandaDialog({ open: true, tipoComanda: 'COLLEGAMENTO_ARRIVO' })\n        break\n      case 'create_command_certificazione':\n        setCreaComandaDialog({ open: true, tipoComanda: 'CERTIFICAZIONE' })\n        break\n      case 'add_multiple_to_command':\n        toast({\n          title: \"Aggiungi Tutti a Comanda\",\n          description: \"Funzione aggiunta multipla a comanda in sviluppo\",\n        })\n        break\n      case 'remove_multiple_from_commands':\n        toast({\n          title: \"Rimuovi Tutti dalle Comande\",\n          description: \"Funzione rimozione multipla dalle comande in sviluppo\",\n        })\n        break\n      default:\n        toast({\n          title: \"Azione non implementata\",\n          description: `Azione ${action} non ancora implementata`,\n        })\n        break\n    }\n  }\n\n  // Gestione successo/errore dialoghi\n  const handleDialogSuccess = (message: string) => {\n    toast({\n      title: \"Operazione completata\",\n      description: message,\n    })\n    // Ricarica i dati\n    loadCavi()\n  }\n\n  const handleDialogError = (message: string) => {\n    toast({\n      title: \"Errore\",\n      description: message,\n      variant: \"destructive\"\n    })\n  }\n\n  // Handler specifico per ModificaBobinaModal\n  const handleModificaBobina = async (cavoId: string, bobinaId: string, option: string) => {\n    try {\n      let message = ''\n\n      switch (option) {\n        case 'cambia-bobina':\n          // Logica per cambiare bobina\n          message = `Bobina ${bobinaId} assegnata al cavo ${cavoId}`\n          break\n        case 'bobina-vuota':\n          // Logica per rimuovere bobina\n          message = `Bobina rimossa dal cavo ${cavoId}`\n          break\n        case 'annulla-posa':\n          // Logica per annullare installazione\n          message = `Installazione annullata per il cavo ${cavoId} - metri restituiti alla bobina`\n          break\n        default:\n          message = `Operazione completata per il cavo ${cavoId}`\n      }\n\n      // Simula chiamata API - da sostituire con chiamata reale\n      await new Promise(resolve => setTimeout(resolve, 1000))\n\n      handleDialogSuccess(message)\n    } catch (error: any) {\n      handleDialogError(error.message || 'Errore durante la modifica della bobina')\n    }\n  }\n\n  // Gestione dei nuovi modali migliorati\n  const handleDisconnectSuccess = () => {\n    setSuccessToast({ visible: true, message: 'Cavo scollegato con successo' })\n    setDisconnectModal({ open: false, cavo: null })\n    loadCavi() // Ricarica i dati\n  }\n\n  const handleDisconnectError = (error: string) => {\n    setCertificationErrorModal({\n      open: true,\n      cavo: disconnectModal.cavo,\n      error\n    })\n    setDisconnectModal({ open: false, cavo: null })\n  }\n\n  const handlePdfSuccess = () => {\n    setSuccessToast({ visible: true, message: 'PDF generato con successo' })\n    setGeneratePdfModal({ open: false, cavo: null })\n  }\n\n  const handlePdfError = (error: string) => {\n    setCertificationErrorModal({\n      open: true,\n      cavo: generatePdfModal.cavo,\n      error\n    })\n    setGeneratePdfModal({ open: false, cavo: null })\n  }\n\n  const handleCertificationSuccess = () => {\n    setSuccessToast({ visible: true, message: 'Certificazione completata con successo' })\n    setCertificationModal({ open: false, cavo: null })\n    loadCavi() // Ricarica i dati\n  }\n\n  const handleCertificationError = (error: string) => {\n    setCertificationErrorModal({\n      open: true,\n      cavo: certificationModal.cavo,\n      error\n    })\n    setCertificationModal({ open: false, cavo: null })\n  }\n\n  // Mostra loader se stiamo caricando i dati dei cavi\n  if (loading && isValidCantiere) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <Loader2 className=\"h-8 w-8 animate-spin\" />\n        <span className=\"ml-2\">Caricamento cavi...</span>\n      </div>\n    )\n  }\n\n  return (\n    <CantiereErrorBoundary>\n      <div className=\"max-w-[90%] mx-auto p-6\">\n\n        {/* Mostra errore specifico dei cavi se presente */}\n        {error && (\n          <Alert variant=\"destructive\" className=\"mb-6\">\n            <AlertCircle className=\"h-4 w-4\" />\n            <AlertDescription>{error}</AlertDescription>\n          </Alert>\n        )}\n\n        {/* Statistics */}\n        <CaviStatistics\n        cavi={cavi}\n        filteredCavi={filteredCavi}\n        revisioneCorrente={revisioneCorrente}\n        className=\"mb-2\"\n      />\n\n      {/* Tabella Cavi Attivi */}\n      <div className=\"mb-8\">\n        <CaviTable\n          cavi={cavi}\n          loading={loading}\n          selectionEnabled={selectionEnabled}\n          selectedCavi={selectedCavi}\n          onSelectionChange={setSelectedCavi}\n          onStatusAction={handleStatusAction}\n          onContextMenuAction={handleContextMenuAction}\n        />\n      </div>\n\n      {/* Tabella Cavi Spare */}\n      {caviSpare.length > 0 && (\n        <div className=\"mb-8\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Package className=\"h-5 w-5\" />\n                <span>Cavi Spare ({caviSpare.length})</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <CaviTable\n                cavi={caviSpare}\n                loading={loading}\n                selectionEnabled={false}\n                onStatusAction={handleStatusAction}\n                onContextMenuAction={handleContextMenuAction}\n              />\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n\n\n      {/* Nuovi Modali Migliorati */}\n      <InserisciMetriDialogOld\n        open={inserisciMetriDialog.open}\n        onClose={() => setInserisciMetriDialog({ open: false, cavo: null })}\n        cavo={inserisciMetriDialog.cavo}\n        cantiere={cantiereForDialog}\n        onSuccess={handleDialogSuccess}\n        onError={handleDialogError}\n      />\n\n      <ModificaBobinaModal\n        open={modificaBobinaDialog.open}\n        onClose={() => setModificaBobinaDialog({ open: false, cavo: null })}\n        cavo={modificaBobinaDialog.cavo}\n        onSave={handleModificaBobina}\n      />\n\n      <DisconnectCableModal\n        open={disconnectModal.open}\n        onClose={() => setDisconnectModal({ open: false, cavo: null })}\n        cavo={disconnectModal.cavo}\n        onConfirm={handleDisconnectSuccess}\n        onError={handleDisconnectError}\n      />\n\n      <GeneratePdfModal\n        open={generatePdfModal.open}\n        onClose={() => setGeneratePdfModal({ open: false, cavo: null })}\n        cavo={generatePdfModal.cavo}\n        onSuccess={handlePdfSuccess}\n        onError={handlePdfError}\n      />\n\n      <CertificationModal\n        open={certificationModal.open}\n        onClose={() => setCertificationModal({ open: false, cavo: null })}\n        cavo={certificationModal.cavo}\n        onSuccess={handleCertificationSuccess}\n        onError={handleCertificationError}\n      />\n\n      <CertificationErrorModal\n        open={certificationErrorModal.open}\n        onClose={() => setCertificationErrorModal({ open: false, cavo: null, error: '' })}\n        cavo={certificationErrorModal.cavo}\n        error={certificationErrorModal.error}\n        onRetry={() => {\n          setCertificationErrorModal({ open: false, cavo: null, error: '' })\n          // Riapri il modal appropriato basato sul contesto\n          if (certificationErrorModal.cavo) {\n            setCertificationModal({ open: true, cavo: certificationErrorModal.cavo })\n          }\n        }}\n      />\n\n      <SuccessToast\n        visible={successToast.visible}\n        message={successToast.message}\n        onClose={() => setSuccessToast({ visible: false, message: '' })}\n      />\n\n      <CreaComandaDialog\n        open={creaComandaDialog.open}\n        onClose={() => setCreaComandaDialog({ open: false })}\n        caviSelezionati={selectedCavi}\n        tipoComanda={creaComandaDialog.tipoComanda}\n        onSuccess={handleDialogSuccess}\n        onError={handleDialogError}\n      />\n\n      <ImportExcelDialog\n        open={importExcelDialog.open}\n        onClose={() => setImportExcelDialog({ open: false })}\n        tipo={importExcelDialog.tipo || 'cavi'}\n        onSuccess={handleDialogSuccess}\n        onError={handleDialogError}\n      />\n\n      <ExportDataDialog\n        open={exportDataDialog}\n        onClose={() => setExportDataDialog(false)}\n        onSuccess={handleDialogSuccess}\n        onError={handleDialogError}\n      />\n      </div>\n    </CantiereErrorBoundary>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA,gDAAgD;AAChD;AAOA;AAMA,4DAA4D;AAC5D;AACA;AACA;AACA;AACA,+CAA+C;AAC/C;AAAA;AAAA;;;AAnCA;;;;;;;;;;;;;;;;;;;AAuDe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,eAAe,EAAE,WAAW,eAAe,EAAE,OAAO,aAAa,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAC9G,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAIvB,yBAAyB;IACzB,MAAM,QAAQ,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAA4D;IACtG,2CAA2C;IAC7C;IACA,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEnE,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,gBAAgB;QAClB;6BAAG;QAAC;KAAK;IAET,uBAAuB;IACvB,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAG5D;QAAE,MAAM;QAAO,MAAM;IAAK;IAE7B,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAG5D;QAAE,MAAM;QAAO,MAAM;IAAK;IAE7B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGxD;QAAE,MAAM;QAAO,MAAM;IAAK;IAE7B,sCAAsC;IACtC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGlD;QAAE,MAAM;QAAO,MAAM;IAAK;IAE7B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGpD;QAAE,MAAM;QAAO,MAAM;IAAK;IAE7B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGxD;QAAE,MAAM;QAAO,MAAM;IAAK;IAE7B,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAIlE;QAAE,MAAM;QAAO,MAAM;QAAM,OAAO;IAAG;IAExC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAG5C;QAAE,SAAS;QAAO,SAAS;IAAG;IAEjC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGtD;QAAE,MAAM;IAAM;IAEjB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGtD;QAAE,MAAM;IAAM;IAEjB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QACjD,QAAQ;QACR,YAAY;QACZ,WAAW;QACX,aAAa;QACb,0BAA0B;QAC1B,yBAAyB;QACzB,2BAA2B;QAC3B,aAAa;QACb,iBAAiB;QACjB,gBAAgB;QAChB,kBAAkB;IACpB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,CAAC,eAAe,CAAC,iBAAiB;gBACpC,OAAO,IAAI,CAAC;YACd;QACF;6BAAG;QAAC;QAAiB;QAAa;KAAO;IAEzC,sCAAsC;IACtC,MAAM,oBAAoB,YAAY,CAAC,cAAc,aAAa,IAAI;QACpE,aAAa;QACb,UAAU,CAAC,SAAS,EAAE,YAAY;IACpC,IAAI,IAAI;IAER,wDAAwD;IACxD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,mBAAmB,cAAc,aAAa,KAAK,CAAC,iBAAiB;gBAEvE;gBACA;YACF,OAAO,IAAI,CAAC,mBAAmB,CAAC,iBAAiB;gBAE/C,QAAQ,EAAE;gBACV,aAAa,EAAE;gBACf,SAAS,iBAAiB;YAC5B;QACF;6BAAG;QAAC;QAAY;QAAiB;QAAiB;KAAc;IAEhE,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,+BAA+B,EAAE,WAAW,mBAAmB,CAAC,EAAE;gBAC9F,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,UAAU;oBAC1D,gBAAgB;gBAClB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,qBAAqB,KAAK,kBAAkB,IAAI;YAClD,OAAO;gBACL,qBAAqB;YACvB;QACF,EAAE,OAAO,OAAO;YACd,qBAAqB;QACvB;IACF;IAEA,MAAM,WAAW;QACf,IAAI;YACF,WAAW;YACX,SAAS;YAET,gCAAgC;YAChC,IAAI;gBACF,MAAM,OAAO,MAAM,oHAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAEnC,6BAA6B;gBAC7B,MAAM,aAAa,KAAK,MAAM,CAAC,CAAC,OAAe,CAAC,KAAK,KAAK;gBAC1D,MAAM,oBAAoB,KAAK,MAAM,CAAC,CAAC,OAAe,KAAK,KAAK;gBAEhE,QAAQ;gBACR,aAAa;gBAEb,sBAAsB;gBACtB,eAAe;YAEjB,EAAE,OAAO,UAAe;gBACtB,MAAM;YACR;QAEF,EAAE,OAAO,OAAY;YACnB,SAAS,CAAC,iCAAiC,EAAE,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,EAAE;QAC9F,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS,SAAS,MAAM;QAC9B,MAAM,aAAa,SAAS,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,YAAY,IAAI,EAAE,eAAe,IAAI,CAAC,IAAI,GAAG,MAAM;QAC9F,MAAM,YAAY,SAAS,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,YAAY,IAAI,EAAE,YAAY,MAAM,GAAG,MAAM,CAAC,gBAAgB;;QACxG,MAAM,cAAc,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM;QAE9D,MAAM,cAAc,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,aAAa,IAAI,CAAC,GAAG;QAC9E,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,YAAY,IAAI,CAAC,GAAG;QACjF,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,GAAG,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,YAAY,IAAI,CAAC,GAAG;QAClH,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,YAAY,IAAI,CAAC,GAAG;QAE7G,SAAS;YACP;YACA;YACA;YACA;YACA,0BAA0B,SAAS,IAAI,KAAK,KAAK,CAAC,AAAC,aAAa,SAAU,OAAO;YACjF,yBAAyB,SAAS,IAAI,KAAK,KAAK,CAAC,AAAC,YAAY,SAAU,OAAO;YAC/E,2BAA2B,SAAS,IAAI,KAAK,KAAK,CAAC,AAAC,cAAc,SAAU,OAAO;YACnF;YACA;YACA;YACA;QACF;IACF;IAEA,yDAAyD;IACzD,MAAM,qBAAqB,CAAC,MAAY,QAAgB;QAEtD,OAAQ;YACN,KAAK;gBACH,wBAAwB;oBAAE,MAAM;oBAAM;gBAAK;gBAC3C;YACF,KAAK;gBACH,wBAAwB;oBAAE,MAAM;oBAAM;gBAAK;gBAC3C;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa,CAAC,iBAAiB,EAAE,MAAM,UAAU,EAAE,KAAK,OAAO,EAAE;gBACnE;gBACA;YACF,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,iEAAiE;gBACjE,sBAAsB;oBAAE,MAAM;oBAAM;gBAAK;gBACzC;YACF,KAAK;gBACH,uCAAuC;gBACvC,mBAAmB;oBAAE,MAAM;oBAAM;gBAAK;gBACtC;YACF,KAAK;gBACH,uCAAuC;gBACvC,sBAAsB;oBAAE,MAAM;oBAAM;gBAAK;gBACzC;YACF,KAAK;gBACH,wCAAwC;gBACxC,oBAAoB;oBAAE,MAAM;oBAAM;gBAAK;gBACvC;QACJ;IACF;IAEA,MAAM,0BAA0B,CAAC,MAAY;QAE3C,OAAQ;YACN,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa,CAAC,2BAA2B,EAAE,KAAK,OAAO,EAAE;gBAC3D;gBACA;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;gBACA;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,MAAM,aAAa,aAAa,QAAQ,CAAC,KAAK,OAAO;gBACrD,IAAI,YAAY;oBACd,gBAAgB,aAAa,MAAM,CAAC,CAAA,KAAM,OAAO,KAAK,OAAO;oBAC7D,MAAM;wBACJ,OAAO;wBACP,aAAa,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,cAAc,CAAC;oBACnD;gBACF,OAAO;oBACL,gBAAgB;2BAAI;wBAAc,KAAK,OAAO;qBAAC;oBAC/C,MAAM;wBACJ,OAAO;wBACP,aAAa,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,YAAY,CAAC;oBACjD;gBACF;gBACA;YACF,KAAK;gBACH,UAAU,SAAS,CAAC,SAAS,CAAC,KAAK,OAAO;gBAC1C,MAAM;oBACJ,OAAO;oBACP,aAAa,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC,sBAAsB,CAAC;gBAC9D;gBACA;YACF,KAAK;gBACH,MAAM,UAAU,CAAC,IAAI,EAAE,KAAK,OAAO,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,cAAc,EAAE,KAAK,UAAU,IAAI,KAAK,OAAO,CAAC,SAAS,EAAE,KAAK,aAAa,EAAE;gBACjJ,UAAU,SAAS,CAAC,SAAS,CAAC;gBAC9B,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,qBAAqB;oBAAE,MAAM;oBAAM,aAAa;gBAAO;gBACvD;YACF,KAAK;gBACH,qBAAqB;oBAAE,MAAM;oBAAM,aAAa;gBAAwB;gBACxE;YACF,KAAK;gBACH,qBAAqB;oBAAE,MAAM;oBAAM,aAAa;gBAAsB;gBACtE;YACF,KAAK;gBACH,qBAAqB;oBAAE,MAAM;oBAAM,aAAa;gBAAiB;gBACjE;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF;gBACE,MAAM;oBACJ,OAAO;oBACP,aAAa,CAAC,OAAO,EAAE,OAAO,wBAAwB,CAAC;gBACzD;gBACA;QACJ;IACF;IAEA,oCAAoC;IACpC,MAAM,sBAAsB,CAAC;QAC3B,MAAM;YACJ,OAAO;YACP,aAAa;QACf;QACA,kBAAkB;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM;YACJ,OAAO;YACP,aAAa;YACb,SAAS;QACX;IACF;IAEA,4CAA4C;IAC5C,MAAM,uBAAuB,OAAO,QAAgB,UAAkB;QACpE,IAAI;YACF,IAAI,UAAU;YAEd,OAAQ;gBACN,KAAK;oBACH,6BAA6B;oBAC7B,UAAU,CAAC,OAAO,EAAE,SAAS,mBAAmB,EAAE,QAAQ;oBAC1D;gBACF,KAAK;oBACH,8BAA8B;oBAC9B,UAAU,CAAC,wBAAwB,EAAE,QAAQ;oBAC7C;gBACF,KAAK;oBACH,qCAAqC;oBACrC,UAAU,CAAC,oCAAoC,EAAE,OAAO,+BAA+B,CAAC;oBACxF;gBACF;oBACE,UAAU,CAAC,kCAAkC,EAAE,QAAQ;YAC3D;YAEA,yDAAyD;YACzD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,oBAAoB;QACtB,EAAE,OAAO,OAAY;YACnB,kBAAkB,MAAM,OAAO,IAAI;QACrC;IACF;IAEA,uCAAuC;IACvC,MAAM,0BAA0B;QAC9B,gBAAgB;YAAE,SAAS;YAAM,SAAS;QAA+B;QACzE,mBAAmB;YAAE,MAAM;YAAO,MAAM;QAAK;QAC7C,WAAW,kBAAkB;;IAC/B;IAEA,MAAM,wBAAwB,CAAC;QAC7B,2BAA2B;YACzB,MAAM;YACN,MAAM,gBAAgB,IAAI;YAC1B;QACF;QACA,mBAAmB;YAAE,MAAM;YAAO,MAAM;QAAK;IAC/C;IAEA,MAAM,mBAAmB;QACvB,gBAAgB;YAAE,SAAS;YAAM,SAAS;QAA4B;QACtE,oBAAoB;YAAE,MAAM;YAAO,MAAM;QAAK;IAChD;IAEA,MAAM,iBAAiB,CAAC;QACtB,2BAA2B;YACzB,MAAM;YACN,MAAM,iBAAiB,IAAI;YAC3B;QACF;QACA,oBAAoB;YAAE,MAAM;YAAO,MAAM;QAAK;IAChD;IAEA,MAAM,6BAA6B;QACjC,gBAAgB;YAAE,SAAS;YAAM,SAAS;QAAyC;QACnF,sBAAsB;YAAE,MAAM;YAAO,MAAM;QAAK;QAChD,WAAW,kBAAkB;;IAC/B;IAEA,MAAM,2BAA2B,CAAC;QAChC,2BAA2B;YACzB,MAAM;YACN,MAAM,mBAAmB,IAAI;YAC7B;QACF;QACA,sBAAsB;YAAE,MAAM;YAAO,MAAM;QAAK;IAClD;IAEA,oDAAoD;IACpD,IAAI,WAAW,iBAAiB;QAC9B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,oNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;8BACnB,6LAAC;oBAAK,WAAU;8BAAO;;;;;;;;;;;;IAG7B;IAEA,qBACE,6LAAC,0JAAA,CAAA,wBAAqB;kBACpB,cAAA,6LAAC;YAAI,WAAU;;gBAGZ,uBACC,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAc,WAAU;;sCACrC,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC,oIAAA,CAAA,mBAAgB;sCAAE;;;;;;;;;;;;8BAKvB,6LAAC,+IAAA,CAAA,UAAc;oBACf,MAAM;oBACN,cAAc;oBACd,mBAAmB;oBACnB,WAAU;;;;;;8BAIZ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,0IAAA,CAAA,UAAS;wBACR,MAAM;wBACN,SAAS;wBACT,kBAAkB;wBAClB,cAAc;wBACd,mBAAmB;wBACnB,gBAAgB;wBAChB,qBAAqB;;;;;;;;;;;gBAKxB,UAAU,MAAM,GAAG,mBAClB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,2MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,6LAAC;;gDAAK;gDAAa,UAAU,MAAM;gDAAC;;;;;;;;;;;;;;;;;;0CAGxC,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC,0IAAA,CAAA,UAAS;oCACR,MAAM;oCACN,SAAS;oCACT,kBAAkB;oCAClB,gBAAgB;oCAChB,qBAAqB;;;;;;;;;;;;;;;;;;;;;;8BAU/B,6LAAC,qJAAA,CAAA,UAAuB;oBACtB,MAAM,qBAAqB,IAAI;oBAC/B,SAAS,IAAM,wBAAwB;4BAAE,MAAM;4BAAO,MAAM;wBAAK;oBACjE,MAAM,qBAAqB,IAAI;oBAC/B,UAAU;oBACV,WAAW;oBACX,SAAS;;;;;;8BAGX,6LAAC,iKAAA,CAAA,sBAAmB;oBAClB,MAAM,qBAAqB,IAAI;oBAC/B,SAAS,IAAM,wBAAwB;4BAAE,MAAM;4BAAO,MAAM;wBAAK;oBACjE,MAAM,qBAAqB,IAAI;oBAC/B,QAAQ;;;;;;8BAGV,6LAAC,4JAAA,CAAA,uBAAoB;oBACnB,MAAM,gBAAgB,IAAI;oBAC1B,SAAS,IAAM,mBAAmB;4BAAE,MAAM;4BAAO,MAAM;wBAAK;oBAC5D,MAAM,gBAAgB,IAAI;oBAC1B,WAAW;oBACX,SAAS;;;;;;8BAGX,6LAAC,4JAAA,CAAA,mBAAgB;oBACf,MAAM,iBAAiB,IAAI;oBAC3B,SAAS,IAAM,oBAAoB;4BAAE,MAAM;4BAAO,MAAM;wBAAK;oBAC7D,MAAM,iBAAiB,IAAI;oBAC3B,WAAW;oBACX,SAAS;;;;;;8BAGX,6LAAC,4JAAA,CAAA,qBAAkB;oBACjB,MAAM,mBAAmB,IAAI;oBAC7B,SAAS,IAAM,sBAAsB;4BAAE,MAAM;4BAAO,MAAM;wBAAK;oBAC/D,MAAM,mBAAmB,IAAI;oBAC7B,WAAW;oBACX,SAAS;;;;;;8BAGX,6LAAC,4JAAA,CAAA,0BAAuB;oBACtB,MAAM,wBAAwB,IAAI;oBAClC,SAAS,IAAM,2BAA2B;4BAAE,MAAM;4BAAO,MAAM;4BAAM,OAAO;wBAAG;oBAC/E,MAAM,wBAAwB,IAAI;oBAClC,OAAO,wBAAwB,KAAK;oBACpC,SAAS;wBACP,2BAA2B;4BAAE,MAAM;4BAAO,MAAM;4BAAM,OAAO;wBAAG;wBAChE,kDAAkD;wBAClD,IAAI,wBAAwB,IAAI,EAAE;4BAChC,sBAAsB;gCAAE,MAAM;gCAAM,MAAM,wBAAwB,IAAI;4BAAC;wBACzE;oBACF;;;;;;8BAGF,6LAAC,4JAAA,CAAA,eAAY;oBACX,SAAS,aAAa,OAAO;oBAC7B,SAAS,aAAa,OAAO;oBAC7B,SAAS,IAAM,gBAAgB;4BAAE,SAAS;4BAAO,SAAS;wBAAG;;;;;;8BAG/D,6LAAC,kJAAA,CAAA,UAAiB;oBAChB,MAAM,kBAAkB,IAAI;oBAC5B,SAAS,IAAM,qBAAqB;4BAAE,MAAM;wBAAM;oBAClD,iBAAiB;oBACjB,aAAa,kBAAkB,WAAW;oBAC1C,WAAW;oBACX,SAAS;;;;;;8BAGX,6LAAC,kJAAA,CAAA,UAAiB;oBAChB,MAAM,kBAAkB,IAAI;oBAC5B,SAAS,IAAM,qBAAqB;4BAAE,MAAM;wBAAM;oBAClD,MAAM,kBAAkB,IAAI,IAAI;oBAChC,WAAW;oBACX,SAAS;;;;;;8BAGX,6LAAC,iJAAA,CAAA,UAAgB;oBACf,MAAM;oBACN,SAAS,IAAM,oBAAoB;oBACnC,WAAW;oBACX,SAAS;;;;;;;;;;;;;;;;;AAKjB;GA1kBwB;;QACoC,kIAAA,CAAA,UAAO;QACmC,8HAAA,CAAA,cAAW;QAChG,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}]}