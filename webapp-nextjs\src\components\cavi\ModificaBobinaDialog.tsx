'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Loader2, AlertCircle, Package, Search, CheckCircle, AlertTriangle } from 'lucide-react'
import { Cavo, Bobina } from '@/types'
import { parcoCaviApi, caviApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'

interface ModificaBobinaDialogProps {
  open: boolean
  onClose: () => void
  cavo: Cavo | null
  cantiere?: { id_cantiere: string; nome_cantiere: string } | null
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

export default function ModificaBobinaD<PERSON>og({
  open,
  onClose,
  cavo,
  cantiere: cantiereProp,
  onSuccess,
  onError
}: ModificaBobinaDialogProps) {
  const { cantiere: cantiereFromContext } = useAuth()
  const cantiere = cantiereProp || cantiereFromContext

  const [selectedOption, setSelectedOption] = useState<string>('assegna_nuova')
  const [selectedBobina, setSelectedBobina] = useState<string>('')
  const [bobine, setBobine] = useState<Bobina[]>([])
  const [loadingBobine, setLoadingBobine] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string>('')
  const [searchText, setSearchText] = useState('')
  const [activeTab, setActiveTab] = useState<'compatibili' | 'incompatibili'>('compatibili')

  // Reset form quando il dialog si apre/chiude
  useEffect(() => {
    if (open) {
      setSelectedOption('assegna_nuova')
      setSelectedBobina('')
      setSearchText('')
      setActiveTab('compatibili')
      setError('')
      if (cantiere?.id_cantiere) {
        loadBobine()
      }
    }
  }, [open, cantiere?.id_cantiere])

  const loadBobine = async () => {
    if (!cantiere?.id_cantiere) {
      setError('Cantiere non disponibile')
      return
    }

    try {
      setLoadingBobine(true)
      setError('')

      console.log('🔄 ModificaBobinaDialog: Caricamento bobine per cantiere:', cantiere.id_cantiere)

      // Usa la stessa logica di InserisciMetriDialog
      const response = await parcoCaviApi.getBobine(cantiere.id_cantiere)
      
      if (response && Array.isArray(response)) {
        // Filtra solo bobine utilizzabili (con metri residui > 0)
        const bobineUtilizzabili = response.filter(bobina => {
          const isUsable = bobina.metri_residui > 0 && 
                          bobina.stato_bobina !== 'Vuota' && 
                          bobina.stato_bobina !== 'Terminata'
          return isUsable
        })

        setBobine(bobineUtilizzabili)
        console.log('✅ ModificaBobinaDialog: Bobine caricate:', bobineUtilizzabili.length)
      } else {
        setBobine([])
        console.log('⚠️ ModificaBobinaDialog: Nessuna bobina trovata')
      }
    } catch (error) {
      console.error('❌ ModificaBobinaDialog: Errore caricamento bobine:', error)
      setError('Errore nel caricamento delle bobine')
      setBobine([])
    } finally {
      setLoadingBobine(false)
    }
  }

  // Funzione per estrarre il numero della bobina dall'ID completo
  const getBobinaNumber = (idBobina: string) => {
    if (!idBobina || idBobina === 'BOBINA_VUOTA') return 'VUOTA'
    if (idBobina && idBobina.includes('_B')) {
      return idBobina.split('_B')[1]
    }
    const bobina = bobine.find(b => b.id_bobina === idBobina)
    return bobina ? bobina.numero_bobina || idBobina : idBobina
  }

  // Filtra le bobine compatibili
  const getBobineCompatibili = () => {
    if (!cavo) return []

    return bobine.filter(bobina => {
      const isCompatible = bobina.tipologia === cavo.tipologia &&
                          bobina.sezione === cavo.sezione
      const matchesSearch = searchText === '' ||
                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) ||
                           (bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase())) ||
                           (bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase()))
      return isCompatible && matchesSearch && bobina.metri_residui > 0
    })
  }

  // Filtra le bobine incompatibili
  const getBobineIncompatibili = () => {
    if (!cavo) return []

    return bobine.filter(bobina => {
      const isIncompatible = bobina.tipologia !== cavo.tipologia ||
                            bobina.sezione !== cavo.sezione
      const matchesSearch = searchText === '' ||
                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) ||
                           (bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase())) ||
                           (bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase()))
      return isIncompatible && matchesSearch && bobina.metri_residui > 0
    })
  }

  const bobineCompatibili = getBobineCompatibili()
  const bobineIncompatibili = getBobineIncompatibili()

  const handleClose = () => {
    setSelectedOption('assegna_nuova')
    setSelectedBobina('')
    setSearchText('')
    setError('')
    onClose()
  }

  const handleSave = async () => {
    console.log('🔄 ModificaBobinaDialog: Salvataggio:', {
      selectedOption,
      selectedBobina,
      cavoId: cavo?.id_cavo,
      cantiereId: cantiere?.id_cantiere
    })

    if (!cavo) {
      return
    }

    try {
      setLoading(true)
      setError('')

      if (selectedOption === 'assegna_nuova') {
        if (!selectedBobina) {
          onError('Selezionare una bobina')
          return
        }

        // Usa updateMetriPosati come in InserisciMetriDialog
        const result = await caviApi.updateMetriPosati({
          id_cavo: cavo.id_cavo,
          metri_posati: cavo.metratura_reale || 0, // Mantieni i metri attuali
          id_bobina: selectedBobina,
          force_over: true // Permetti operazioni incompatibili
        })

        if (result.success) {
          onSuccess(`Bobina aggiornata con successo per il cavo ${cavo.id_cavo}`)
          handleClose()
        } else {
          onError(result.message || 'Errore durante l\'aggiornamento della bobina')
        }
      } else if (selectedOption === 'rimuovi_bobina') {
        // Assegna BOBINA_VUOTA
        const result = await caviApi.updateMetriPosati({
          id_cavo: cavo.id_cavo,
          metri_posati: cavo.metratura_reale || 0,
          id_bobina: 'BOBINA_VUOTA',
          force_over: false
        })

        if (result.success) {
          onSuccess(`Bobina rimossa dal cavo ${cavo.id_cavo}`)
          handleClose()
        } else {
          onError(result.message || 'Errore durante la rimozione della bobina')
        }
      } else if (selectedOption === 'annulla_installazione') {
        // Reset completo del cavo
        const result = await caviApi.updateMetriPosati({
          id_cavo: cavo.id_cavo,
          metri_posati: 0,
          id_bobina: 'BOBINA_VUOTA',
          force_over: false
        })

        if (result.success) {
          onSuccess(`Installazione annullata per il cavo ${cavo.id_cavo}`)
          handleClose()
        } else {
          onError(result.message || 'Errore durante l\'annullamento dell\'installazione')
        }
      }
    } catch (error) {
      console.error('❌ ModificaBobinaDialog: Errore salvataggio:', error)
      onError('Errore durante il salvataggio')
    } finally {
      setLoading(false)
    }
  }

  if (!cavo) return null

  return (
    <>
      <Dialog open={open} onOpenChange={handleClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>
              Modifica Bobina Cavo {cavo.id_cavo}
            </DialogTitle>
          </DialogHeader>

          <div className="flex-1 overflow-hidden space-y-6">
            {/* Sezione Cavo Selezionato */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Package className="h-5 w-5 text-blue-600" />
                <h3 className="font-medium text-gray-900">Cavo Selezionato</h3>
              </div>

              {/* Informazioni Cavo - Formato esatto come richiesto */}
              <div className="p-4 bg-blue-50 rounded-lg border-2 border-blue-200">
                <h3 className="font-semibold text-blue-800 mb-3">Informazioni Cavo</h3>
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div><strong>Tipologia:</strong> {cavo.tipologia || 'N/A'}</div>
                  <div><strong>Da:</strong> {cavo.ubicazione_partenza || 'N/A'}</div>
                  <div><strong>Formazione:</strong> {cavo.sezione || 'N/A'}</div>
                  <div><strong>A:</strong> {cavo.ubicazione_arrivo || 'N/A'}</div>
                  <div className="col-span-2"><strong>Metri Posati:</strong> {cavo.metratura_reale || 0} m</div>
                </div>
              </div>
            </div>

            {/* Sezione Opzioni di modifica */}
            <div className="space-y-3">
              <h3 className="font-medium">Opzioni di modifica</h3>

              <div className="space-y-2">
                <label className="flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50">
                  <input
                    type="radio"
                    name="operazione"
                    value="assegna_nuova"
                    checked={selectedOption === 'assegna_nuova'}
                    onChange={(e) => setSelectedOption(e.target.value)}
                    className="w-4 h-4 text-blue-600"
                  />
                  <span className="text-sm">Assegna nuova bobina</span>
                </label>

                <label className="flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50">
                  <input
                    type="radio"
                    name="operazione"
                    value="rimuovi_bobina"
                    checked={selectedOption === 'rimuovi_bobina'}
                    onChange={(e) => setSelectedOption(e.target.value)}
                    className="w-4 h-4 text-blue-600"
                  />
                  <span className="text-sm">Rimuovi bobina (BOBINA VUOTA)</span>
                </label>

                <label className="flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50">
                  <input
                    type="radio"
                    name="operazione"
                    value="annulla_installazione"
                    checked={selectedOption === 'annulla_installazione'}
                    onChange={(e) => setSelectedOption(e.target.value)}
                    className="w-4 h-4 text-blue-600"
                  />
                  <span className="text-sm">Annulla installazione</span>
                </label>
              </div>
            </div>

            {/* Sezione Seleziona bobina - solo se "Assegna nuova bobina" è selezionato */}
            {selectedOption === 'assegna_nuova' && (
              <div className="space-y-3">
                <h3 className="font-medium">Seleziona bobina</h3>

                {/* Campo ricerca */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Cerca bobina per ID, tipologia o numero..."
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    className="pl-10"
                  />
                </div>

                {/* Tab per bobine compatibili/incompatibili */}
                <div className="flex space-x-1 border-b">
                  <button
                    onClick={() => setActiveTab('compatibili')}
                    className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                      activeTab === 'compatibili'
                        ? 'border-green-500 text-green-600 bg-green-50'
                        : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4" />
                      <span>Bobine Compatibili ({bobineCompatibili.length})</span>
                    </div>
                  </button>
                  <button
                    onClick={() => setActiveTab('incompatibili')}
                    className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                      activeTab === 'incompatibili'
                        ? 'border-orange-500 text-orange-600 bg-orange-50'
                        : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <AlertCircle className="h-4 w-4" />
                      <span>Bobine Incompatibili ({bobineIncompatibili.length})</span>
                    </div>
                  </button>
                </div>

                {/* Lista bobine */}
                <div className="border rounded-lg h-64 overflow-y-auto">
                  {loadingBobine ? (
                    <div className="flex items-center justify-center h-full">
                      <div className="flex items-center space-x-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="text-sm text-gray-600">Caricamento bobine...</span>
                      </div>
                    </div>
                  ) : (
                    <div className="p-2">
                      {activeTab === 'compatibili' ? (
                        bobineCompatibili.length === 0 ? (
                          <div className="text-center py-8">
                            <div className="text-gray-500 text-sm mb-2">
                              Nessuna bobina compatibile trovata
                            </div>
                            <div className="text-xs text-gray-400">
                              Cercando bobine con tipologia <strong>{cavo.tipologia}</strong> e formazione <strong>{cavo.sezione}</strong>
                            </div>
                          </div>
                        ) : (
                          <div className="space-y-2">
                            {bobineCompatibili.map((bobina) => (
                              <div
                                key={bobina.id_bobina}
                                onClick={() => setSelectedBobina(bobina.id_bobina)}
                                className={`p-3 rounded-lg cursor-pointer transition-all duration-200 ${
                                  selectedBobina === bobina.id_bobina
                                    ? 'bg-blue-100 border-2 border-blue-300 shadow-md'
                                    : 'hover:bg-gray-50 border border-gray-200 hover:border-gray-300'
                                }`}
                              >
                                <div className="flex justify-between items-start">
                                  <div className="flex-1">
                                    <div className="flex items-center space-x-2 mb-1">
                                      <div className="font-medium text-sm text-gray-900">{bobina.id_bobina}</div>
                                      {bobina.stato_bobina && (
                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                          bobina.stato_bobina === 'Disponibile' ? 'bg-green-100 text-green-800' :
                                          bobina.stato_bobina === 'In uso' ? 'bg-blue-100 text-blue-800' :
                                          'bg-yellow-100 text-yellow-800'
                                        }`}>
                                          {bobina.stato_bobina}
                                        </span>
                                      )}
                                    </div>
                                    <div className="text-xs text-gray-500 mb-1">
                                      <span className="font-medium">{bobina.tipologia}</span> • <span>{bobina.sezione}</span>
                                    </div>
                                  </div>
                                  <div className="text-right ml-3">
                                    <div className={`text-sm font-medium ${
                                      bobina.metri_residui > 0 ? 'text-green-600' : 'text-gray-500'
                                    }`}>
                                      {bobina.metri_residui}m
                                    </div>
                                    <div className="text-xs text-gray-400">
                                      {bobina.metri_residui > 0 ? 'disponibili' : 'esaurita'}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        )
                      ) : (
                        bobineIncompatibili.length === 0 ? (
                          <div className="text-center py-8 text-gray-500 text-sm">
                            Nessuna bobina incompatibile trovata
                          </div>
                        ) : (
                          <div className="space-y-2">
                            {/* Avviso per bobine incompatibili */}
                            <div className="p-3 bg-orange-50 border border-orange-200 rounded-lg">
                              <div className="flex items-start space-x-2">
                                <AlertCircle className="h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0" />
                                <div className="text-sm text-orange-800">
                                  <div className="font-medium mb-1">Bobine Incompatibili</div>
                                  <div className="text-xs">
                                    Selezionando una bobina incompatibile, le caratteristiche del cavo verranno aggiornate.
                                  </div>
                                </div>
                              </div>
                            </div>

                            {bobineIncompatibili.map((bobina) => (
                              <div
                                key={bobina.id_bobina}
                                onClick={() => setSelectedBobina(bobina.id_bobina)}
                                className={`p-3 rounded-lg cursor-pointer transition-all duration-200 ${
                                  selectedBobina === bobina.id_bobina
                                    ? 'bg-orange-100 border-2 border-orange-300 shadow-md'
                                    : 'hover:bg-gray-50 border border-gray-200 hover:border-gray-300'
                                }`}
                              >
                                <div className="flex justify-between items-start">
                                  <div className="flex-1">
                                    <div className="flex items-center space-x-2 mb-1">
                                      <div className="font-medium text-sm text-gray-900">{bobina.id_bobina}</div>
                                      <span className="px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                        INCOMPATIBILE
                                      </span>
                                    </div>
                                    <div className="text-xs text-gray-500 mb-1">
                                      <span className="font-medium">{bobina.tipologia}</span> • <span>{bobina.sezione}</span>
                                    </div>
                                  </div>
                                  <div className="text-right ml-3">
                                    <div className={`text-sm font-medium ${
                                      bobina.metri_residui > 0 ? 'text-orange-600' : 'text-gray-500'
                                    }`}>
                                      {bobina.metri_residui}m
                                    </div>
                                    <div className="text-xs text-gray-400">
                                      {bobina.metri_residui > 0 ? 'disponibili' : 'esaurita'}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        )
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Errori */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <DialogFooter className="flex justify-end space-x-2">
            <Button variant="outline" onClick={handleClose} disabled={loading}>
              Annulla
            </Button>
            <Button 
              onClick={handleSave} 
              disabled={loading || (selectedOption === 'assegna_nuova' && !selectedBobina)}
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Salva
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
