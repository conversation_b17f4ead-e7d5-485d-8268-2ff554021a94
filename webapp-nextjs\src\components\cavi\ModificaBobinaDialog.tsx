'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'

import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle, Package, Search, CheckCircle } from 'lucide-react'
import { Cavo } from '@/types'
import { parcoCaviApi, caviApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'

interface Bobina {
  id_bobina: string
  tipologia: string
  sezione: string
  metri_residui: number
  fornitore?: string
  numero_bobina?: string
  stato_bobina?: string
}

interface Cantiere {
  id_cantiere: number
  commessa: string
}

interface ModificaBobinaDialogProps {
  open: boolean
  onClose: () => void
  cavo: Cavo | null
  cantiere: Cantiere | null
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

export default function ModificaBobinaDialog({
  open,
  onClose,
  cavo,
  cantiere: cantiereProp,
  onSuccess,
  onError
}: ModificaBobinaDialogProps) {
  const { cantiere: cantiereAuth } = useAuth()

  // Usa cantiere dalla prop o da auth come fallback
  const cantiere = cantiereProp || cantiereAuth

  const [bobine, setBobine] = useState<Bobina[]>([])
  const [selectedOption, setSelectedOption] = useState('')
  const [selectedBobina, setSelectedBobina] = useState('')
  const [searchText, setSearchText] = useState('')
  const [loading, setLoading] = useState(false)
  const [loadingBobine, setLoadingBobine] = useState(false)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState<'compatibili' | 'incompatibili'>('compatibili')

  const loadBobineCompatibili = async () => {
    if (!cavo) {
      return
    }

    try {
      setLoadingBobine(true)
      setError('')

      // Fallback per cantiere come negli altri dialog
      let cantiereId = cantiere?.id_cantiere

      // Se non abbiamo cantiere dalla prop, prova localStorage
      if (!cantiereId && typeof window !== 'undefined') {
        const storedId = localStorage.getItem('selectedCantiereId')
        cantiereId = storedId ? parseInt(storedId) : 0

        // FALLBACK TEMPORANEO PER DEBUG - se non c'è niente, usa cantiere 1
        if (!cantiereId || cantiereId === 0) {
          cantiereId = 1
        }
      }

      if (!cantiereId || cantiereId === 0) {
        setBobine([])
        setError('Nessun cantiere selezionato. Seleziona un cantiere e riprova.')
        return
      }

      console.log('🔍 ModificaBobinaDialog: Caricamento bobine per cavo:', {
        cantiereId,
        cavo: {
          id_cavo: cavo.id_cavo,
          tipologia: cavo.tipologia,
          n_conduttori: cavo.n_conduttori,
          sezione: cavo.sezione
        }
      })

      // Usa la stessa logica di InserisciMetriDialog: carica tutte le bobine disponibili
      const response = await parcoCaviApi.getBobine(cantiereId)

      console.log('🔍 ModificaBobinaDialog: Risposta API getBobine (stesso metodo di InserisciMetriDialog):', response)

      // Gestisce diversi formati di risposta (stesso codice di InserisciMetriDialog)
      let bobineData: any[] = []
      if (Array.isArray(response)) {
        bobineData = response
      } else if (response && Array.isArray((response as any).data)) {
        bobineData = (response as any).data
      } else if (response && response.bobine && Array.isArray((response as any).bobine)) {
        bobineData = (response as any).bobine
      } else {
        throw new Error('Formato risposta API non valido')
      }

      console.log('🔍 ModificaBobinaDialog: Bobine totali ricevute:', bobineData.length)

      // Filtra solo per stato (disponibile o in uso) e metri residui > 0 (stesso filtro di InserisciMetriDialog)
      const bobineUtilizzabili = bobineData.filter((bobina: any) =>
        bobina.stato_bobina !== 'Terminata' &&
        bobina.stato_bobina !== 'Over' &&
        bobina.metri_residui > 0
      )

      console.log('🔍 ModificaBobinaDialog: Bobine utilizzabili dopo filtro stato:', bobineUtilizzabili.length)

      // Mappa le bobine al formato corretto
      const bobineMappate: Bobina[] = bobineUtilizzabili.map((b: any) => ({
        id_bobina: b.id_bobina,
        tipologia: b.tipologia,
        sezione: b.sezione, // sezione nel DB = formazione sistema
        metri_residui: b.metri_residui,
        fornitore: b.fornitore,
        numero_bobina: b.numero_bobina,
        stato_bobina: b.stato_bobina
      }))

      // Aggiungi sempre BOBINA_VUOTA come opzione
      const bobineConVuota: Bobina[] = [
        {
          id_bobina: 'BOBINA_VUOTA',
          tipologia: cavo.tipologia || '',
          sezione: cavo.sezione || '',
          metri_residui: 0,
          numero_bobina: '',
          stato_bobina: 'Vuota'
        },
        ...bobineMappate
      ]

      // Se il cavo ha già una bobina, assicurati che sia nella lista
      if (cavo.id_bobina && cavo.id_bobina !== 'BOBINA_VUOTA' && !bobineConVuota.find(b => b.id_bobina === cavo.id_bobina)) {
        bobineConVuota.push({
          id_bobina: cavo.id_bobina,
          tipologia: cavo.tipologia || '',
          sezione: cavo.sezione || '',
          metri_residui: 0, // Bobina attualmente in uso
          numero_bobina: '',
          stato_bobina: 'In uso'
        })
      }

      setBobine(bobineConVuota)
      console.log('🔍 ModificaBobinaDialog: Bobine caricate con successo:', bobineConVuota.length)

    } catch (error: any) {
      console.error('🔍 ModificaBobinaDialog: Errore nel caricamento delle bobine:', error)

      // Fallback con solo BOBINA_VUOTA e bobina corrente
      const fallbackBobine: Bobina[] = [
        {
          id_bobina: 'BOBINA_VUOTA',
          tipologia: cavo.tipologia || '',
          sezione: cavo.sezione || '',
          metri_residui: 0,
          numero_bobina: '',
          stato_bobina: 'Vuota'
        }
      ]

      if (cavo.id_bobina && cavo.id_bobina !== 'BOBINA_VUOTA') {
        fallbackBobine.push({
          id_bobina: cavo.id_bobina,
          tipologia: cavo.tipologia || '',
          sezione: cavo.sezione || '',
          metri_residui: 0,
          numero_bobina: '',
          stato_bobina: 'In uso'
        })
      }

      setBobine(fallbackBobine)

      // Non mostrare errore se è solo un problema di rete, permetti di usare BOBINA_VUOTA (stesso comportamento di InserisciMetriDialog)
      if (error.response?.status !== 404) {
        const errorMessage = error.response?.data?.detail || error.message || 'Errore nel caricamento delle bobine. Puoi comunque usare BOBINA VUOTA.'
        setError(errorMessage)
        onError(errorMessage)
      }
    } finally {
      setLoadingBobine(false)
    }
  }

  // Carica bobine compatibili quando si apre il dialog
  useEffect(() => {

    if (open && cavo) {
      loadBobineCompatibili()
      setSelectedOption('')
      setSelectedBobina('')
      setSearchText('')
      setError('')
    }
  }, [open, cavo, cantiere, cantiereProp, cantiereAuth])

  // Filtra bobine compatibili
  const getBobineCompatibili = () => {
    if (!cavo) return []

    return bobine.filter(bobina => {
      const isCompatible = bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione
      const matchesSearch = !searchText ||
                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase())
      return isCompatible && matchesSearch && bobina.metri_residui > 0
    })
  }

  // Filtra bobine incompatibili
  const getBobineIncompatibili = () => {
    if (!cavo) return []

    return bobine.filter(bobina => {
      const isIncompatible = bobina.tipologia !== cavo.tipologia || bobina.sezione !== cavo.sezione
      const matchesSearch = !searchText ||
                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase())
      return isIncompatible && matchesSearch && bobina.metri_residui > 0
    })
  }

  const bobineCompatibili = getBobineCompatibili()
  const bobineIncompatibili = getBobineIncompatibili()

  const handleSave = async () => {
    if (!cavo) return

    // Validazione basata su opzione selezionata
    if (!selectedOption) {
      setError('Selezionare un\'operazione')
      return
    }

    if (selectedOption === 'assegna_nuova' && !selectedBobina) {
      setError('Selezionare una bobina dalla lista')
      return
    }

    // Determina la nuova bobina e valida
    let newBobinaId: string = ''

    if (selectedOption === 'annulla_installazione') {
      // Per annullamento installazione, non serve bobina specifica
      newBobinaId = '' // Non usato per questa operazione
    } else if (selectedOption === 'assegna_nuova') {
      newBobinaId = selectedBobina
      // Controlla se la bobina è già associata
      if (newBobinaId === cavo.id_bobina) {
        setError('La bobina selezionata è già associata al cavo')
        return
      }
    } else if (selectedOption === 'rimuovi_bobina') {
      newBobinaId = 'BOBINA_VUOTA'
      // Controlla se il cavo ha già BOBINA_VUOTA
      if (newBobinaId === cavo.id_bobina) {
        setError('Il cavo ha già la bobina vuota assegnata')
        return
      }
    } else {
      setError('Opzione non valida')
      return
    }

    // Verifica metri sufficienti solo per bobine reali e compatibili
    if (selectedOption === 'assegna_nuova') {
      const bobina = bobine.find(b => b.id_bobina === selectedBobina)
      const metriPosati = cavo.metratura_reale || 0

      if (bobina) {
        // Controlla se la bobina è compatibile
        const isCompatible = bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione

        // Per bobine compatibili, controlla i metri solo come avviso (non bloccare)
        if (isCompatible && metriPosati > bobina.metri_residui) {
          console.warn(`⚠️ ModificaBobinaDialog: Bobina compatibile con metri insufficienti - sarà in stato OVER`)
          // Non bloccare l'operazione, force_over gestirà questo caso
        }

        // Per bobine incompatibili, non controllare i metri - force_over gestirà tutto
        if (!isCompatible) {
          console.log(`🔄 ModificaBobinaDialog: Bobina incompatibile selezionata - aggiornamento caratteristiche cavo`)
        }
      }
    }

    try {
      setLoading(true)
      setError('')

      if (!cantiere) {
        throw new Error('Cantiere non selezionato')
      }

      // Gestisci diverse operazioni con endpoint appropriati
      console.log('🔧 ModificaBobinaDialog: Operazione bobina:', {
        cantiere: cantiere.id_cantiere,
        cavo: cavo.id_cavo,
        operazione: selectedOption,
        nuovaBobina: newBobinaId
      })

      if (selectedOption === 'annulla_installazione') {
        // Annulla installazione: resetta completamente il cavo (metri = 0, stato = "da installare")
        await caviApi.cancelInstallation(cantiere.id_cantiere, cavo.id_cavo)
      } else {
        // Assegna nuova bobina o rimuovi bobina: usa updateMetriPosati con metri attuali
        // Questo è lo stesso metodo che funziona in InserisciMetriDialog
        const metriAttuali = cavo.metratura_reale || 0

        console.log('🔧 ModificaBobinaDialog: Usando updateMetriPosati per cambio bobina:', {
          cantiere: cantiere.id_cantiere,
          cavo: cavo.id_cavo,
          metri: metriAttuali,
          nuovaBobina: newBobinaId,
          forceOver: true
        })

        await caviApi.updateMetriPosati(
          cantiere.id_cantiere,
          cavo.id_cavo,
          metriAttuali,
          newBobinaId,
          true  // force_over: true per permettere bobine incompatibili
        )
      }

      let message = ''
      switch (selectedOption) {
        case 'assegna_nuova':
          message = `Nuova bobina ${newBobinaId} assegnata al cavo ${cavo.id_cavo}`
          break
        case 'rimuovi_bobina':
          message = `Bobina rimossa dal cavo ${cavo.id_cavo} (metri restituiti alla bobina precedente)`
          break
        case 'annulla_installazione':
          message = `Installazione annullata per il cavo ${cavo.id_cavo} (metri restituiti, stato resettato a "da installare")`
          break
        default:
          message = `Operazione completata per il cavo ${cavo.id_cavo}`
      }

      onSuccess(message)
      onClose()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la modifica della bobina'
      onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    if (!loading) {
      setSelectedOption('')
      setSelectedBobina('')
      setSearchText('')
      setError('')
      setActiveTab('compatibili')
      onClose()
    }
  }

  if (!cavo) return null

  return (
    <>
      <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>
            Modifica Bobina Cavo {cavo.id_cavo}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-hidden space-y-6">
          {/* Sezione Cavo Selezionato */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Package className="h-5 w-5 text-blue-600" />
              <h3 className="font-medium text-gray-900">Cavo Selezionato</h3>
            </div>

            {/* Informazioni Cavo - Formato esatto come richiesto */}
            <div className="p-4 bg-blue-50 rounded-lg border-2 border-blue-200">
              <h3 className="font-semibold text-blue-800 mb-3">Informazioni Cavo</h3>
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div><strong>Tipologia:</strong> {cavo.tipologia || 'N/A'}</div>
                <div><strong>Da:</strong> {cavo.ubicazione_partenza || 'N/A'}</div>
                <div><strong>Formazione:</strong> {cavo.sezione || 'N/A'}</div>
                <div><strong>A:</strong> {cavo.ubicazione_arrivo || 'N/A'}</div>
                <div className="col-span-2"><strong>Metri Posati:</strong> {cavo.metratura_reale || 0} m</div>
              </div>
            </div>



              {/* Informazioni aggiuntive */}
              {(cavo.sistema || cavo.utility || cavo.colore_cavo) && (
                <div className="grid grid-cols-3 gap-2 mt-3">
                  {cavo.sistema && (
                    <div className="text-xs">
                      <span className="text-gray-500">Sistema:</span>
                      <span className="ml-1 font-medium">{cavo.sistema}</span>
                    </div>
                  )}
                  {cavo.utility && (
                    <div className="text-xs">
                      <span className="text-gray-500">Utility:</span>
                      <span className="ml-1 font-medium">{cavo.utility}</span>
                    </div>
                  )}
                  {cavo.colore_cavo && (
                    <div className="text-xs">
                      <span className="text-gray-500">Colore:</span>
                      <span className="ml-1 font-medium">{cavo.colore_cavo}</span>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Sezione Opzioni di modifica */}
          <div className="space-y-3">
            <h3 className="font-medium">Opzioni di modifica</h3>

            <div className="space-y-2">
              <label className="flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50">
                <input
                  type="radio"
                  name="operazione"
                  value="assegna_nuova"
                  checked={selectedOption === 'assegna_nuova'}
                  onChange={(e) => setSelectedOption(e.target.value)}
                  className="w-4 h-4 text-blue-600"
                />
                <span className="text-sm">Assegna nuova bobina</span>
              </label>

              <label className="flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50">
                <input
                  type="radio"
                  name="operazione"
                  value="rimuovi_bobina"
                  checked={selectedOption === 'rimuovi_bobina'}
                  onChange={(e) => setSelectedOption(e.target.value)}
                  className="w-4 h-4 text-blue-600"
                />
                <span className="text-sm">Rimuovi bobina attuale</span>
              </label>

              <label className="flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50">
                <input
                  type="radio"
                  name="operazione"
                  value="annulla_installazione"
                  checked={selectedOption === 'annulla_installazione'}
                  onChange={(e) => setSelectedOption(e.target.value)}
                  className="w-4 h-4 text-blue-600"
                />
                <span className="text-sm">Annulla installazione</span>
              </label>
            </div>
          </div>

          {/* Sezione Seleziona bobina - solo se "Assegna nuova bobina" è selezionato */}
          {selectedOption === 'assegna_nuova' && (
            <div className="space-y-3">
              <h3 className="font-medium">Seleziona bobina</h3>

              {/* Campo ricerca */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Cerca bobina per ID, tipologia o numero..."
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Tab per bobine compatibili/incompatibili */}
              <div className="flex space-x-1 border-b">
                <button
                  onClick={() => setActiveTab('compatibili')}
                  className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === 'compatibili'
                      ? 'border-green-500 text-green-600 bg-green-50'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4" />
                    <span>Bobine Compatibili ({bobineCompatibili.length})</span>
                  </div>
                </button>
                <button
                  onClick={() => setActiveTab('incompatibili')}
                  className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === 'incompatibili'
                      ? 'border-orange-500 text-orange-600 bg-orange-50'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    <AlertCircle className="h-4 w-4" />
                    <span>Bobine Incompatibili ({bobineIncompatibili.length})</span>
                  </div>
                </button>
              </div>

              {/* Errore caricamento bobine */}
              {error && (
                <Alert className="mb-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {/* Lista bobine */}
              <div className="border rounded-lg h-64 overflow-y-auto">
                {loadingBobine ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="flex items-center space-x-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span className="text-sm text-gray-600">Caricamento bobine...</span>
                    </div>
                  </div>
                ) : (
                  <div className="p-2">
                    {activeTab === 'compatibili' ? (
                      bobineCompatibili.length === 0 ? (
                        <div className="text-center py-8">
                          <div className="text-gray-500 text-sm mb-2">
                            Nessuna bobina compatibile trovata
                          </div>
                          <div className="text-xs text-gray-400">
                            Cercando bobine con tipologia <strong>{cavo.tipologia}</strong> e formazione <strong>{cavo.sezione}</strong>
                          </div>
                          <div className="text-xs text-gray-400 mt-1">
                            Prova a verificare se esistono bobine nel parco cavi o usa il tab "Incompatibili"
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          {bobineCompatibili.map((bobina) => (
                            <div
                              key={bobina.id_bobina}
                              onClick={() => setSelectedBobina(bobina.id_bobina)}
                              className={`p-3 rounded-lg cursor-pointer transition-all duration-200 ${
                                selectedBobina === bobina.id_bobina
                                  ? 'bg-blue-100 border-2 border-blue-300 shadow-md'
                                  : 'hover:bg-gray-50 border border-gray-200 hover:border-gray-300'
                              }`}
                            >
                              <div className="flex justify-between items-start">
                                <div className="flex-1">
                                  <div className="flex items-center space-x-2 mb-1">
                                    <div className="font-medium text-sm text-gray-900">{bobina.id_bobina}</div>
                                    {bobina.stato_bobina && (
                                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                        bobina.stato_bobina === 'Disponibile' ? 'bg-green-100 text-green-800' :
                                        bobina.stato_bobina === 'In uso' ? 'bg-blue-100 text-blue-800' :
                                        bobina.stato_bobina === 'Vuota' ? 'bg-gray-100 text-gray-600' :
                                        'bg-yellow-100 text-yellow-800'
                                      }`}>
                                        {bobina.stato_bobina}
                                      </span>
                                    )}
                                  </div>
                                  <div className="text-xs text-gray-500 mb-1">
                                    <span className="font-medium">{bobina.tipologia}</span> • <span>{bobina.sezione}</span>
                                  </div>
                                  {bobina.numero_bobina && (
                                    <div className="text-xs text-gray-400">
                                      Numero: {bobina.numero_bobina}
                                    </div>
                                  )}
                                  {bobina.fornitore && (
                                    <div className="text-xs text-gray-400">
                                      Fornitore: {bobina.fornitore}
                                    </div>
                                  )}
                                </div>
                                <div className="text-right ml-3">
                                  <div className={`text-sm font-medium ${
                                    bobina.metri_residui > 0 ? 'text-green-600' : 'text-gray-500'
                                  }`}>
                                    {bobina.metri_residui}m
                                  </div>
                                  <div className="text-xs text-gray-400">
                                    {bobina.metri_residui > 0 ? 'disponibili' : 'esaurita'}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )
                    ) : (
                      bobineIncompatibili.length === 0 ? (
                        <div className="text-center py-8 text-gray-500 text-sm">
                          Nessuna bobina incompatibile trovata
                        </div>
                      ) : (
                        <div className="space-y-2">
                          {/* Avviso per bobine incompatibili */}
                          <div className="p-3 bg-orange-50 border border-orange-200 rounded-lg">
                            <div className="flex items-start space-x-2">
                              <AlertCircle className="h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0" />
                              <div className="text-sm text-orange-800">
                                <div className="font-medium mb-1">Bobine Incompatibili</div>
                                <div className="text-xs">
                                  Selezionando una bobina incompatibile, le caratteristiche del cavo (tipologia e formazione)
                                  verranno aggiornate per corrispondere a quelle della bobina selezionata.
                                </div>
                              </div>
                            </div>
                          </div>

                          {bobineIncompatibili.map((bobina) => (
                            <div
                              key={bobina.id_bobina}
                              onClick={() => setSelectedBobina(bobina.id_bobina)}
                              className={`p-3 rounded-lg cursor-pointer transition-all duration-200 ${
                                selectedBobina === bobina.id_bobina
                                  ? 'bg-orange-100 border-2 border-orange-300 shadow-md'
                                  : 'hover:bg-gray-50 border border-gray-200 hover:border-gray-300'
                              }`}
                            >
                              <div className="flex justify-between items-start">
                                <div className="flex-1">
                                  <div className="flex items-center space-x-2 mb-1">
                                    <div className="font-medium text-sm text-gray-900">{bobina.id_bobina}</div>
                                    <span className="px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                      INCOMPATIBILE
                                    </span>
                                    {bobina.stato_bobina && (
                                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                        bobina.stato_bobina === 'Disponibile' ? 'bg-green-100 text-green-800' :
                                        bobina.stato_bobina === 'In uso' ? 'bg-blue-100 text-blue-800' :
                                        bobina.stato_bobina === 'Vuota' ? 'bg-gray-100 text-gray-600' :
                                        'bg-yellow-100 text-yellow-800'
                                      }`}>
                                        {bobina.stato_bobina}
                                      </span>
                                    )}
                                  </div>
                                  <div className="text-xs text-gray-500 mb-1">
                                    <span className="font-medium">{bobina.tipologia}</span> • <span>{bobina.sezione}</span>
                                  </div>
                                  {bobina.numero_bobina && (
                                    <div className="text-xs text-gray-400">
                                      Numero: {bobina.numero_bobina}
                                    </div>
                                  )}
                                  {bobina.fornitore && (
                                    <div className="text-xs text-gray-400">
                                      Fornitore: {bobina.fornitore}
                                    </div>
                                  )}
                                </div>
                                <div className="text-right ml-3">
                                  <div className={`text-sm font-medium ${
                                    bobina.metri_residui > 0 ? 'text-orange-600' : 'text-gray-500'
                                  }`}>
                                    {bobina.metri_residui}m
                                  </div>
                                  <div className="text-xs text-gray-400">
                                    {bobina.metri_residui > 0 ? 'disponibili' : 'esaurita'}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Errori */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <DialogFooter className="flex justify-end space-x-2">
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            Annulla
          </Button>
          <Button
            onClick={handleSave}
            disabled={
              loading ||
              !selectedOption ||
              (selectedOption === 'bobina_compatibile' && !selectedBobina)
            }
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Salva
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
    </>
  )
}
