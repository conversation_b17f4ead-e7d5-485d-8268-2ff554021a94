'use client'

import { useState, useMemo, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { getCavoColorClasses } from '@/utils/softColors'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { TableRow, TableCell } from '@/components/ui/table'
import { Cavo } from '@/types'
import FilterableTable, { ColumnDef } from '@/components/common/FilterableTable'
import SmartCaviFilter from './SmartCaviFilter'
import TruncatedText from '@/components/common/TruncatedText'
import { ActionTooltip } from './tooltips/CableTooltips'
import {
  MoreHorizontal,
  Cable,
  Settings,
  Zap,
  CheckCircle,
  AlertCircle,
  Clock,
  Package,
  Link,
  Unlink,
  Award,
  Play,
  Pause,
  X,
  Check,
  FileText,
  Download,
  AlertTriangle,
  Wrench,
  ChevronDown,
  Info
} from 'lucide-react'

interface CaviTableProps {
  cavi: Cavo[]
  loading?: boolean
  selectionEnabled?: boolean
  selectedCavi?: string[]
  onSelectionChange?: (selectedIds: string[]) => void
  onStatusAction?: (cavo: Cavo, action: string) => void
  onContextMenuAction?: (cavo: Cavo, action: string) => void
}

export default function CaviTable({
  cavi = [],
  loading = false,
  selectionEnabled = false,
  selectedCavi = [],
  onSelectionChange,
  onStatusAction,
  onContextMenuAction
}: CaviTableProps) {
  const [smartFilteredCavi, setSmartFilteredCavi] = useState(cavi)
  const [filteredCavi, setFilteredCavi] = useState(cavi)
  const [internalSelectionEnabled, setInternalSelectionEnabled] = useState(selectionEnabled)

  // Aggiorna i cavi quando cambiano i cavi originali
  useEffect(() => {
    setSmartFilteredCavi(cavi)
    setFilteredCavi(cavi)
  }, [cavi])

  // Gestione filtri intelligenti
  const handleSmartFilterChange = (filtered: Cavo[]) => {
    setSmartFilteredCavi(filtered)
  }

  // Gestione filtri tabella
  const handleTableFilterChange = (filtered: Cavo[]) => {
    setFilteredCavi(filtered)
  }

  const handleSelectionToggle = () => {
    setInternalSelectionEnabled(!internalSelectionEnabled)
  }

  // Gestione selezione
  const handleSelectAll = (checked: boolean) => {
    if (onSelectionChange) {
      onSelectionChange(checked ? filteredCavi.map(c => c.id_cavo) : [])
    }
  }

  const handleSelectCavo = (cavoId: string, checked: boolean) => {
    if (onSelectionChange) {
      const newSelection = checked
        ? [...selectedCavi, cavoId]
        : selectedCavi.filter(id => id !== cavoId)
      onSelectionChange(newSelection)
    }
  }

  // Bulk action handlers
  const handleBulkExport = async () => {
    try {
      const response = await fetch('/api/cavi/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          selectedIds: selectedCavi,
          cantiereId: 1 // TODO: Get from context
        })
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `cavi_export_${new Date().toISOString().split('T')[0]}.csv`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        const error = await response.json()
        alert(`Errore durante l'esportazione: ${error.error}`)
      }
    } catch (error) {
      alert('Errore durante l\'esportazione')
    }
  }

  const handleBulkStatusChange = async () => {
    const newStatus = prompt('Inserisci il nuovo stato (Da installare, In corso, Installato):')
    if (!newStatus) return

    try {
      const response = await fetch('/api/cavi/bulk-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          selectedIds: selectedCavi,
          cantiereId: 1, // TODO: Get from context
          newStatus
        })
      })

      const result = await response.json()
      if (result.success) {
        alert(result.message)
        // TODO: Refresh data
      } else {
        alert(`Errore: ${result.error}`)
      }
    } catch (error) {
      alert('Errore durante il cambio stato')
    }
  }

  const handleBulkAssignCommand = () => {
    // TODO: Implementare modal per selezione comanda
    alert(`Assegnazione comanda per ${selectedCavi.length} cavi`)
  }

  const handleBulkDelete = async () => {
    if (!confirm(`Sei sicuro di voler eliminare ${selectedCavi.length} cavi?`)) {
      return
    }

    try {
      const response = await fetch('/api/cavi/bulk-delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          selectedIds: selectedCavi,
          cantiereId: 1 // TODO: Get from context
        })
      })

      const result = await response.json()
      if (result.success) {
        alert(result.message)
        // TODO: Refresh data
      } else {
        alert(`Errore: ${result.error}`)
      }
    } catch (error) {
      alert('Errore durante l\'eliminazione')
    }
  }

  // Define columns matching original webapp structure
  const columns: ColumnDef[] = useMemo(() => {
    const baseColumns: ColumnDef[] = [
      {
        field: 'id_cavo',
        headerName: 'ID',
        dataType: 'text',
        width: 70,
        align: 'left',
        renderCell: (row: Cavo) => (
          <span className="font-semibold text-mariner-900">{row.id_cavo}</span>
        )
      },
      {
        field: 'sistema',
        headerName: 'Sistema',
        dataType: 'text',
        width: 80,
        renderCell: (row: Cavo) => (
          <TruncatedText text={row.sistema || ''} maxLength={8} />
        )
      },
      {
        field: 'utility',
        headerName: 'Utility',
        dataType: 'text',
        width: 80,
        renderCell: (row: Cavo) => (
          <TruncatedText text={row.utility || ''} maxLength={8} />
        )
      },
      {
        field: 'tipologia',
        headerName: 'Tipologia',
        dataType: 'text',
        width: 100,
        renderCell: (row: Cavo) => (
          <TruncatedText text={row.tipologia || ''} maxLength={12} />
        )
      },
      {
        field: 'formazione',
        headerName: 'Form.',
        dataType: 'text',
        align: 'left',
        width: 60,
        renderCell: (row: Cavo) => row.formazione || row.sezione
      },
      {
        field: 'metri_teorici',
        headerName: 'M.Teor.',
        dataType: 'number',
        align: 'left',
        width: 70,
        renderCell: (row: Cavo) => row.metri_teorici ? row.metri_teorici.toFixed(1) : '0'
      },
      {
        field: 'metri_posati',
        headerName: 'M.Reali',
        dataType: 'number',
        align: 'left',
        width: 70,
        renderCell: (row: Cavo) => {
          const metri = row.metri_posati || row.metratura_reale || 0
          return metri ? metri.toFixed(1) : '0'
        }
      },
      {
        field: 'ubicazione_partenza',
        headerName: 'Da',
        dataType: 'text',
        width: 140,
        renderCell: (row: Cavo) => (
          <TruncatedText
            text={row.da || row.ubicazione_partenza || ''}
            maxLength={18}
          />
        )
      },
      {
        field: 'ubicazione_arrivo',
        headerName: 'A',
        dataType: 'text',
        width: 140,
        renderCell: (row: Cavo) => (
          <TruncatedText
            text={row.a || row.ubicazione_arrivo || ''}
            maxLength={18}
          />
        )
      },
      {
        field: 'id_bobina',
        headerName: 'Bobina',
        dataType: 'text',
        width: 80,
        align: 'center',
        renderCell: (row: Cavo) => getBobinaDisplay(row)
      },
      {
        field: 'stato_installazione',
        headerName: 'Stato',
        dataType: 'text',
        align: 'left',
        width: 120,
        disableFilter: true,
        disableSort: true,
        renderCell: (row: Cavo) => getStatusBadge(row)
      },
      {
        field: 'collegamenti',
        headerName: 'Collegamenti',
        dataType: 'text',
        align: 'left',
        width: 180,
        disableFilter: true,
        disableSort: true,
        renderCell: (row: Cavo) => getConnectionButton(row)
      },
      {
        field: 'certificato',
        headerName: 'Certificato',
        dataType: 'text',
        align: 'left',
        width: 130,
        disableFilter: true,
        disableSort: true,
        renderCell: (row: Cavo) => getCertificationDisplay(row)
      },

    ]

    // Add selection column if enabled
    if (internalSelectionEnabled) {
      baseColumns.unshift({
        field: 'selection',
        headerName: '',
        disableFilter: true,
        disableSort: true,
        width: 50,
        align: 'left',
        renderHeader: () => (
          <Checkbox
            checked={selectedCavi.length === filteredCavi.length && filteredCavi.length > 0}
            onCheckedChange={handleSelectAll}
          />
        ),
        renderCell: (row: Cavo) => (
          <Checkbox
            checked={selectedCavi.includes(row.id_cavo)}
            onCheckedChange={(checked) => handleSelectCavo(row.id_cavo, checked as boolean)}
            onClick={(e) => e.stopPropagation()}
          />
        )
      })
    }

    return baseColumns
  }, [internalSelectionEnabled, selectedCavi, filteredCavi, handleSelectAll, handleSelectCavo])

  // Custom row renderer for selection and context menu
  const renderRow = (row: Cavo, index: number) => {
    const isSelected = selectedCavi.includes(row.id_cavo)

    return (
      <TableRow
        key={row.id_cavo}
        className={`
          ${isSelected ? 'bg-blue-50 border-blue-200' : 'bg-white'}
          hover:bg-blue-50 hover:border-blue-200 hover:shadow-sm
          cursor-pointer border-b border-gray-200
          transition-all duration-200 ease-in-out
          ${isSelected ? 'ring-1 ring-blue-300' : ''}
        `}
        onClick={() => internalSelectionEnabled && handleSelectCavo(row.id_cavo, !isSelected)}
        onContextMenu={(e) => {
          e.preventDefault()
          onContextMenuAction?.(row, 'context_menu')
        }}
      >
        {columns.map((column) => (
          <TableCell
            key={column.field}
            className={`
              py-2 px-2 text-sm text-left
              ${isSelected ? 'text-blue-900' : 'text-gray-900'}
              transition-colors duration-200
            `}
            style={{ width: column.width, ...column.cellStyle }}
            onClick={(e) => {
              // Prevent row click for action columns
              if (['stato_installazione', 'collegamenti', 'certificato'].includes(column.field)) {
                e.stopPropagation()
              }
            }}
          >
            {column.renderCell ? column.renderCell(row) : (row[column.field] || <span className="text-gray-400">-</span>)}
          </TableCell>
        ))}
      </TableRow>
    )
  }

  // ===== COMPONENTI UI MIGLIORATI SECONDO LE SPECIFICHE =====

  // 1. COLONNA BOBINA - Badge Cliccabile con Indicazione di Interazione
  const getBobinaDisplay = (cavo: Cavo) => {
    const idBobina = cavo.id_bobina

    if (!idBobina || idBobina === 'N/A') {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
          -
        </span>
      )
    }

    if (idBobina === 'BOBINA_VUOTA') {
      // Bobina vuota - Badge cliccabile per assegnare una nuova bobina
      return (
        <button
          className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-orange-50 text-orange-700 border border-orange-200 hover:bg-orange-100 hover:border-orange-300 transition-colors cursor-pointer"
          onClick={(e) => {
            e.stopPropagation()
            onStatusAction?.(cavo, 'modify_reel')
          }}
          title="Clicca per assegnare bobina"
        >
          <span>Vuota</span>
          <ChevronDown className="w-3 h-3 opacity-60" />
        </button>
      )
    }

    // Estrai il numero della bobina usando la stessa logica della versione originale
    let numeroDisplay = idBobina

    // Pattern dalla webapp originale: /_B(.+)$/
    let match = idBobina.match(/_B(.+)$/)
    if (match) {
      numeroDisplay = match[1]
    } else {
      // Pattern alternativo: _b (minuscolo)
      match = idBobina.match(/_b(.+)$/)
      if (match) {
        numeroDisplay = match[1]
      } else {
        // Pattern per cX_bY o cX_BY
        match = idBobina.match(/c\d+_[bB](\d+)$/)
        if (match) {
          numeroDisplay = match[1]
        } else {
          // Pattern generale per numeri alla fine
          match = idBobina.match(/(\d+)$/)
          if (match) {
            numeroDisplay = match[1]
          }
        }
      }
    }

    // Bobina assegnata - Badge cliccabile con indicatore dropdown
    return (
      <button
        className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 hover:bg-blue-100 hover:border-blue-300 transition-colors cursor-pointer"
        onClick={(e) => {
          e.stopPropagation()
          onStatusAction?.(cavo, 'modify_reel')
        }}
        title="Clicca per gestire bobina"
      >
        <span>{numeroDisplay}</span>
        <ChevronDown className="w-3 h-3 opacity-60" />
      </button>
    )
  }

  // 2. COLONNA STATO - Badge con Azioni per Stati Modificabili
  const getStatusBadge = (cavo: Cavo) => {
    const metriInstallati = cavo.metri_posati || cavo.metratura_reale || 0
    const isInstalled = metriInstallati > 0
    const stato = cavo.stato_installazione || 'Da installare'

    // Verifica se il cavo è assegnato a una comanda attiva
    const comandaPosa = cavo.comanda_posa
    const comandaPartenza = cavo.comanda_partenza
    const comandaArrivo = cavo.comanda_arrivo
    const comandaCertificazione = cavo.comanda_certificazione
    const comandaAttiva = comandaPosa || comandaPartenza || comandaArrivo || comandaCertificazione

    // Se c'è una comanda attiva e lo stato è "In corso", mostra il codice comanda (cliccabile)
    if (comandaAttiva && stato === 'In corso') {
      return (
        <button
          className="inline-flex items-center gap-1 px-2 py-1 rounded text-xs font-medium bg-blue-600 text-white hover:bg-blue-700 transition-colors cursor-pointer"
          onClick={(e) => {
            e.stopPropagation()
            onStatusAction?.(cavo, 'view_command', comandaAttiva)
          }}
          title="Clicca per visualizzare comanda"
        >
          <Settings className="w-3 h-3" />
          <span>{comandaAttiva}</span>
        </button>
      )
    }

    // Stati con azioni disponibili
    if (stato === 'Installato' || isInstalled) {
      // Installato - Cliccabile per modificare bobina
      return (
        <button
          className="inline-flex items-center gap-1 px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800 border border-green-300 hover:bg-green-200 hover:text-green-900 transition-colors cursor-pointer"
          onClick={(e) => {
            e.stopPropagation()
            onStatusAction?.(cavo, 'modify_reel')
          }}
          title="Clicca per modificare bobina"
        >
          <CheckCircle className="w-3 h-3" />
          <span>Installato</span>
        </button>
      )
    } else if (stato === 'In corso') {
      // In corso - Cliccabile per inserire metri
      return (
        <button
          className="inline-flex items-center gap-1 px-2 py-1 rounded text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-300 hover:bg-yellow-200 hover:text-yellow-900 transition-colors cursor-pointer"
          onClick={(e) => {
            e.stopPropagation()
            onStatusAction?.(cavo, 'insert_meters')
          }}
          title="Clicca per inserire metri posati"
        >
          <Play className="w-3 h-3" />
          <span>In corso</span>
        </button>
      )
    } else {
      // Da installare - Cliccabile per inserire metri
      return (
        <button
          className="inline-flex items-center gap-1 px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-700 border border-gray-300 hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300 transition-colors cursor-pointer"
          onClick={(e) => {
            e.stopPropagation()
            onStatusAction?.(cavo, 'insert_meters')
          }}
          title="Clicca per inserire metri posati"
        >
          <Pause className="w-3 h-3" />
          <span>Da installare</span>
        </button>
      )
    }
  }

  // 3. COLONNA COLLEGAMENTI - Pulsanti di Azione Chiari
  const getConnectionButton = (cavo: Cavo) => {
    const isInstalled = cavo.metri_posati > 0 || cavo.metratura_reale > 0
    const collegamento = cavo.collegamento || cavo.collegamenti || 0

    if (!isInstalled) {
      // Non disponibile - Testo statico con tooltip
      return (
        <div className="flex items-center gap-1 text-xs text-gray-400" title="Collegamento disponibile solo per cavi installati">
          <Info className="w-3 h-3" />
          <span>Non disponibile</span>
        </div>
      )
    }

    // Pulsanti di azione standardizzati
    const getActionButton = (label: string, icon: React.ReactNode, actionType: string, variant: 'primary' | 'warning' | 'success' = 'primary') => {
      const baseClasses = "inline-flex items-center gap-1 px-2 py-1 rounded text-xs font-medium border transition-colors cursor-pointer"
      const variantClasses = {
        primary: "text-blue-700 border-blue-300 bg-white hover:bg-blue-50 hover:border-blue-400",
        warning: "text-yellow-700 border-yellow-300 bg-white hover:bg-yellow-50 hover:border-yellow-400",
        success: "text-green-700 border-green-300 bg-white hover:bg-green-50 hover:border-green-400"
      }

      return (
        <button
          className={`${baseClasses} ${variantClasses[variant]}`}
          onClick={(e) => {
            e.stopPropagation()
            onStatusAction?.(cavo, actionType)
          }}
        >
          {icon}
          <span>{label}</span>
        </button>
      )
    }

    switch (collegamento) {
      case 0:
        return getActionButton("Collega", <Link className="w-3 h-3" />, "connect_cable", 'primary')
      case 1:
        return getActionButton("Completa Arrivo", <Link className="w-3 h-3" />, "connect_arrival", 'warning')
      case 2:
        return getActionButton("Completa Partenza", <Link className="w-3 h-3" />, "connect_departure", 'warning')
      case 3:
        return getActionButton("Scollega", <Unlink className="w-3 h-3" />, "disconnect_cable", 'success')
      default:
        return getActionButton("Gestisci", <Settings className="w-3 h-3" />, "manage_connections", 'primary')
    }
  }

  // 4. COLONNA CERTIFICATO - Distinzione tra Stato e Azione
  const getCertificationDisplay = (cavo: Cavo) => {
    const isInstalled = cavo.metri_posati > 0 || cavo.metratura_reale > 0
    const isCertified = cavo.certificato === true || cavo.certificato === 'SI' || cavo.certificato === 'CERTIFICATO'

    if (!isInstalled) {
      // Non disponibile - Testo statico con tooltip
      return (
        <div className="flex items-center gap-1 text-xs text-gray-400" title="Certificazione disponibile solo per cavi installati">
          <Info className="w-3 h-3" />
          <span>Non disponibile</span>
        </div>
      )
    }

    if (isCertified) {
      // Certificato - Pulsante per generare PDF
      return (
        <button
          className="inline-flex items-center gap-1 px-2 py-1 rounded text-xs font-medium text-green-700 border border-green-300 bg-white hover:bg-green-50 hover:border-green-400 transition-colors cursor-pointer"
          onClick={(e) => {
            e.stopPropagation()
            onStatusAction?.(cavo, 'generate_pdf')
          }}
        >
          <Download className="w-3 h-3" />
          <span>Genera PDF</span>
        </button>
      )
    }

    // Non certificato - Pulsante per certificare
    return (
      <button
        className="inline-flex items-center gap-1 px-2 py-1 rounded text-xs font-medium text-purple-700 border border-purple-300 bg-white hover:bg-purple-50 hover:border-purple-400 transition-colors cursor-pointer"
        onClick={(e) => {
          e.stopPropagation()
          onStatusAction?.(cavo, 'create_certificate')
        }}
      >
        <Award className="w-3 h-3" />
        <span>Certifica</span>
      </button>
    )
  }

  return (
    <div className="relative">
      {/* Smart Filter */}
      <SmartCaviFilter
        cavi={cavi}
        onFilteredDataChange={handleSmartFilterChange}
        loading={loading}
        selectionEnabled={internalSelectionEnabled}
        onSelectionToggle={handleSelectionToggle}
      />

      {/* Filterable Table */}
      <FilterableTable
        data={smartFilteredCavi}
        columns={columns}
        loading={loading}
        emptyMessage="Nessun cavo disponibile"
        onFilteredDataChange={handleTableFilterChange}
        renderRow={renderRow}
      />

      {/* Contextual Action Bar - appears only when items are selected */}
      {internalSelectionEnabled && selectedCavi.length > 0 && (
        <div className="sticky bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-10">
          <div className="flex items-center justify-between p-3">
            <div className="flex items-center space-x-3">
              <Badge variant="secondary" className="bg-mariner-100 text-mariner-800">
                {selectedCavi.length} cavi selezionati
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSelectAll(false)}
                className="text-xs"
              >
                Deseleziona tutto
              </Button>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkExport()}
                className="flex items-center space-x-1"
              >
                <span>📊</span>
                <span>Esporta</span>
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkStatusChange()}
                className="flex items-center space-x-1"
              >
                <span>🔄</span>
                <span>Cambia Stato</span>
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkAssignCommand()}
                className="flex items-center space-x-1"
              >
                <span>📋</span>
                <span>Assegna Comanda</span>
              </Button>

              <Button
                variant="destructive"
                size="sm"
                onClick={() => handleBulkDelete()}
                className="flex items-center space-x-1"
              >
                <span>🗑️</span>
                <span>Elimina</span>
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
